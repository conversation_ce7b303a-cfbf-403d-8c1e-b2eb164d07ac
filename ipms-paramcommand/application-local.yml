eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/registry/eureka/
  instance:
    hostname: localhost
    instance-id: ${spring.application.name}:${instanceId:${random.value}}
    preferIpAddress: true

kafka:
  server: localhost:29092
  parameter-create-topic: parameterCreateTopic
  parameter-update-topic: parameterUpdateTopic
  parameter-delete-topic: parameterDeleteTopic
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic
  exchange-rate-topic: exchangeRateTopic
  jde-price-topic: jdePriceTopic

spring:
  datasource:
    driverClassName: org.postgresql.Driver
    url: ********************************************************************************
    username: ipms_dev_user
    password: JJKw3Z6wMMyLDbWm
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn
        provider:
          keycloak:
            token-uri: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth/realms/ipms-default/protocol/openid-connect/token
  cache:
    type: redis
    redis:
      time-to-live: 960000
  redis:
    database: 0
    host: localhost
    password: eYVX7EwVmmxKPCDmwMtyKVge8oLd2t81
    port: 6379



ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-default
  auth-server-url: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn