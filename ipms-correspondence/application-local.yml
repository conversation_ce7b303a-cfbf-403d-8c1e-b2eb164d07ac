eureka:
  client:
    service-url:
      defaultZone: http://localhost:8761/registry/eureka/
  instance:
    hostname: localhost
    instance-id: ${spring.application.name}:${instanceId:${random.value}}
    preferIpAddress: true

kafka:
  server: localhost:29092
  email-trx-topic: emailTrxTopic
  email-topic: emailTopic
  reply-due-date-topic: correspondenceReplyDueDateTopic
  reply-status-topic: replyStatusTopic
  notification-topic: notificationTopic
  data-transfer-topic: dataTransferTopic
  correspondence-updated-topic: correspondenceUpdatedTopic
  convert-issue-rollback-topic: convertIssueRollbackTopic
  close-issue-topic: closeIssueTopic
  issue-update-relational-topic: issueUpdateRelationalTopic

spring:
  datasource:
    driverClassName: org.postgresql.Driver
    url: ********************************************************************************
    username: ipms_dev_user
    password: JJKw3Z6wMMyLDbWm
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: true
  security:
    oauth2:
      client:
        registration:
          keycloak:
            authorization-grant-type: client_credentials
            client-id: ipms-backend
            client-secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn
        provider:
          keycloak:
            token-uri: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth/realms/ipms-default/protocol/openid-connect/token

ipms:
  keycloak-web-client: ipms-web

keycloak:
  realm: ipms-default
  auth-server-url: http://test-ipms-keycloak.westeurope.cloudapp.azure.com/auth
  ssl-required: none
  resource: ipms-backend
  use-resource-role-mappings: true
  bearer-only: true
  credentials:
    secret: NTULhE8DgIPiNkKs2vCeepUlyr4xIkSn