eureka:
  client:
    service-url:
      defaultZone: ${EUREKA_URI}
  instance:
    hostname: ${HOSTNAME:gateway}
    instance-id: ${HOSTNAME:${spring.application.name}:${instanceId:${random.value}}}
    preferIpAddress: true

spring:
  cloud:
    gateway:
      routes:
        - id: ipms-user
          uri: lb://ipms-user
          predicates:
            - Path=/api/users/**
        - id: users-swagger
          uri: http://localhost:8010/api/users/docs/**
          predicates:
            - Path=/api/users/docs/**

        - id: ipms-mail
          uri: lb://ipms-mail
          predicates:
            - Path=/api/mail/**

        - id: ipms-correspondence
          uri: lb://ipms-correspondence
          predicates:
            - Path=/api/correspondence/**

        - id: ipms-issue
          uri: lb://ipms-issue
          predicates:
            - Path=/api/issue/**

        - id: ipms-firm
          uri: lb://ipms-firm
          predicates:
            - Path=/api/firm/**

        - id: ipms-matter
          uri: lb://ipms-matter
          predicates:
            - Path=/api/matter/**

        - id: ipms-activity
          uri: lb://ipms-activity
          predicates:
            - Path=/api/activity/**

        - id: ipms-notification
          uri: lb://ipms-notification
          predicates:
            - Path=/api/notification/**

        - id: ipms-executor
          uri: lb://ipms-executor
          predicates:
            - Path=/api/executor/**

        - id: ipms-paramcommand
          uri: lb://ipms-paramcommand
          predicates:
            - Path=/api/paramcommand/**

        - id: ipms-paramquery
          uri: lb://ipms-paramquery
          predicates:
            - Path=/api/paramquery/**

        - id: ipms-transfer
          uri: lb://ipms-transfer
          predicates:
            - Path=/api/transfer/**

        - id: ipms-document
          uri: lb://ipms-document
          predicates:
            - Path=/api/document/**

        - id: ipms-quotation
          uri: lb://ipms-quotation
          predicates:
            - Path=/api/quotation/**

        - id: ipms-billing
          uri: lb://ipms-billing
          predicates:
            - Path=/api/billing/**

        - id: ipms-integration
          uri: lb://ipms-integration
          predicates:
            - Path=/api/integration/**