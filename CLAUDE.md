# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Build and Development Commands

### Maven Commands
```bash
# Build the project
mvn clean compile -DskipTests=true

# Run all tests
mvn clean test -Djacoco.skip=true -Dnet.bytebuddy.experimental=true

# Run single test class
mvn clean test -Dtest=FirmServiceTest -Djacoco.skip=true -Dnet.bytebuddy.experimental=true

# Run specific test method
mvn clean test -Dtest=FirmServiceTest#givenFirmDto_whenCheckByTitleAndCountryAndCity_thenDoesNotThrowAnyException -Djacoco.skip=true -Dnet.bytebuddy.experimental=true
```

### Spring Boot Application
```bash
# Run the application locally
mvn spring-boot:run

# Build JAR file
mvn clean package -DskipTests=true

# Run JAR file
java -jar target/ipms-firm-0.0.1-SNAPSHOT.jar
```

### Docker Commands
```bash
# Build Docker image
docker build -t ipms-firm .

# Run Docker container
docker run -p 8050:8050 ipms-firm
```

## Architecture Overview

### Microservice Structure
This is the **ipms-firm** microservice, part of the IPMS (Intellectual Property Management System) platform. The service manages law firms, companies, contacts, leads, and related geographical entities.

### Key Domain Models
- **Firm**: Central entity representing law firms and IP service providers
- **Lead**: Prospective client information with lead management capabilities
- **Contact**: Contact information associated with firms
- **Country/State/Province/City**: Geographical hierarchy for address management
- **GroupCompany**: Company grouping structure for firm organization
- **AgentInfo/RightOwnerInfo**: Specialized firm role information

### Technology Stack
- **Java 17** with Spring Boot 2.7.0
- **Spring Cloud** (Eureka, OpenFeign, Config Server)
- **PostgreSQL** with Hibernate/JPA and Envers auditing
- **MapStruct** for object mapping
- **Lombok** for boilerplate code reduction
- **Docker** containerization
- **Maven** build system

## Package Structure

### Core Packages
- `controller/`: REST API endpoints (FirmController, LeadController, etc.)
- `service/impl/`: Business logic implementation
- `repository/`: JPA data access layer
- `model/`: Entity classes extending VersionedEntity
- `dto/`: Data Transfer Objects
- `mapper/`: MapStruct mapping interfaces
- `client/`: Feign clients for inter-service communication

### Supporting Packages
- `handler/`: Request/response processing layer
- `exception/`: Custom exception classes
- `enums/`: Enumeration types and response codes
- `specification/`: JPA Specification for dynamic queries
- `validator/`: Custom validation logic
- `converter/`: JPA attribute converters

## Key Design Patterns

### Entity Inheritance
All entities extend `VersionedEntity` which provides:
- Audit trails (createdAt, updatedAt, createdBy, updatedBy)
- Soft deletion with `@Where(clause = "is_deleted='0'")`
- Optimistic locking with `@Version`
- Hibernate Envers audit history

### Microservice Communication
Uses **OpenFeign** clients for inter-service communication:
- `ActivityClient`: Communication with ipms-activity service
- `MatterClient`: Communication with ipms-matter service

### Configuration Management
- **Local Profile**: File-based configuration (`application-local.yml`)
- **Test/Prod Profiles**: Spring Cloud Config Server integration
- Environment-specific configurations in separate yml files

## Testing Strategy

### Test Structure
- **Unit Tests**: Service layer tests with Mockito
- **Integration Tests**: Controller integration tests
- **Handler Tests**: Request/response processing tests

### Test Execution
Always use `mvn clean test` when tests fail mysteriously. The `-Djacoco.skip=true` flag skips coverage analysis for faster execution.

### Test Coverage
JaCoCo configuration excludes:
- Model classes (`model/**/*`)
- DTO classes (`dto/**/*`)

## Common Development Patterns

### Exception Handling
Custom exceptions extend appropriate base classes:
- `FirmNotFoundException`: Entity not found scenarios
- `FirmValidationException`: Business rule validation failures
- Response codes defined in enums (e.g., `FirmValidationResponseCode`)

### Feign Client Usage
Guard against empty collections in path variables:
```java
if (expenseIds.isEmpty()) {
    return List.of();
}
return activityClient.getActivitiesByExpenses(expenseIds);
```

### Entity Validation
Always verify entity creation by checking ID changes:
```java
Long lastIdBefore = getLastId();
entityService.create(newEntity);
Long lastIdAfter = getLastId();
if (lastIdBefore.equals(lastIdAfter)) {
    throw new ValidationException(ResponseCode.ENTITY_NOT_CREATED);
}
```

## Service Dependencies

### Internal Dependencies
- **ipms-core-entity**: Base entity classes
- **ipms-core-common**: Shared utilities
- **ipms-core-i18n**: Internationalization support
- **ipms-core-exception-handling**: Exception handling framework
- **ipms-config-security**: Security configuration
- **ipms-config-kafka**: Kafka messaging configuration
- **ipms-config-storage**: File storage configuration
- **ipms-config-apidoc**: API documentation configuration

### External Service Dependencies
- **ipms-activity**: Activity and workflow management
- **ipms-matter**: Legal matter management
- **ipms-eureka**: Service discovery
- **ipms-config-api**: Configuration server

### Bill of Materials (BOM) Pattern
The **ipms-bom** module provides centralized dependency management:

#### BOM Import
```xml
<dependencyManagement>
    <dependencies>
        <dependency>
            <groupId>com.ipms</groupId>
            <artifactId>ipms-bom</artifactId>
            <version>1.0.0</version>
            <type>pom</type>
            <scope>import</scope>
        </dependency>
    </dependencies>
</dependencyManagement>
```

#### Managed Dependencies
- **IPMS Core Libraries**: Entity, Common, i18n, Exception Handling
- **IPMS Config Libraries**: Security, Kafka, Storage, API Documentation  
- **Third-Party Libraries**: MapStruct, Lombok, Commons utilities

#### Benefits
- **Version Consistency**: Single source of truth for all dependency versions
- **Simplified Maintenance**: Update versions in one place
- **Conflict Resolution**: Prevents version conflicts between services
- **Developer Experience**: No need to specify versions for managed dependencies

## Database Schema

### Key Tables
- `firm`: Main firm/company information
- `lead`: Lead management data
- `contact`: Contact information
- `country`, `state`, `province`, `city`: Geographical hierarchy
- `group_company`: Company grouping
- `firm_contact`: Many-to-many relationship between firms and contacts
- `firm_billing_account`: Firm billing account associations

### Audit Tables
All main entities have corresponding audit tables created by Hibernate Envers (e.g., `firm_aud`, `lead_aud`).

## API Endpoints

### Firm Management
- `POST /`: Create new firm
- `GET /{id}`: Get firm by ID
- `PUT /{id}`: Update firm
- `DELETE /{id}/{version}`: Soft delete firm
- `GET /firms`: Get firms with filtering
- `GET /summary`: Get firm summaries by title

### Lead Management
- `POST /leads`: Create lead
- `GET /leads/{id}`: Get lead by ID
- `PUT /leads/{id}`: Update lead
- `DELETE /leads/{id}/{version}`: Delete lead

### Geographical Entities
- Country, State, Province, City CRUD operations
- Hierarchical relationship management

## Development Notes

### Code Style
- Use `@Slf4j` for logging instead of `System.out.println`
- Prefer `List.of()` for immutable lists
- Use `var` for local variables where type is obvious
- No explanatory comments in code

### Performance Considerations
- Use pagination for large result sets
- Implement proper indexing on frequently queried fields
- Use `@ElementCollection` for simple collections like `billingAccountIds`

### Security
- All endpoints protected by Spring Security
- Keycloak integration for authentication
- Audit trails for all entity changes