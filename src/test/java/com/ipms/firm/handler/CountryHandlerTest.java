package com.ipms.firm.handler;

import com.ipms.firm.dto.CountryDto;
import com.ipms.firm.handler.impl.CountryHandlerImpl;
import com.ipms.firm.mapper.CountryMapper;
import com.ipms.firm.service.CountryService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(SpringRunner.class)
public class CountryHandlerTest {

    @MockBean
    private CountryHandler handler;

    @Mock
    private CountryService service;

    @Mock
    private CountryMapper mapper;

    @Before
    public void setUp() {
        handler = new CountryHandlerImpl(service, mapper);
    }

    @Test
    public void givenCountryList_whenFindAll_thenReturnCountryList() {
        List<CountryDto> countryDtoList = new ArrayList<>();
        countryDtoList.add(CountryDto.builder().id(1L).name("Turkey").iso2("TR").iso3("TUR").build());
        countryDtoList.add(CountryDto.builder().id(2L).name("USA").iso2("US").iso3("USA").build());
        Mockito.when(handler.getAll()).
                thenReturn(countryDtoList);
        assertThat(countryDtoList).hasSize(2);
    }

    @Test
    public void givenIso2_whenFindByIso2_thenReturnCountry() {
        CountryDto countryDto = CountryDto.builder().id(1L).name("Turkey").iso2("TR").iso3("TUR").build();
        Mockito.when(handler.getByIso2("TR"))
                .thenReturn(countryDto);
        assertThat(countryDto.getIso2()).isEqualTo("TR");
    }

    @Test
    public void givenId_whenFindIso2ById_thenReturnCountry() {
        CountryDto countryDto = CountryDto.builder().id(1L).name("Turkey").iso2("TR").iso3("TUR").build();
        Mockito.when(handler.getIso2ById(1L))
                .thenReturn(countryDto);
        assertThat(countryDto.getId()).isEqualTo(1L);
        assertThat(countryDto.getIso2()).isEqualTo("TR");
    }

}
