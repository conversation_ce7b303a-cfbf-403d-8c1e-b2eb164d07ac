package com.ipms.firm.handler;

import com.ipms.firm.dto.StateDto;
import com.ipms.firm.handler.impl.StateHandlerImpl;
import com.ipms.firm.mapper.StateMapper;
import com.ipms.firm.service.StateService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;


@RunWith(SpringRunner.class)
public class StateHandlerTest {

    @MockBean
    private StateHandler handler;

    @Mock
    private StateService service;

    @Mock
    private StateMapper mapper;

    @Before
    public void setUp() {
        handler = new StateHandlerImpl(service, mapper);
    }

    @Test
    public void givenCountryCode_whenFindByCountry_thenReturnStateList() {
        List<StateDto> stateDtoList = new ArrayList<>();
        stateDtoList.add(StateDto.builder().id(1L).name("Texas").countryCode("US").iso2("TX").build());
        stateDtoList.add(StateDto.builder().id(2L).name("Alaska").countryCode("US").iso2("AK").build());
        Mockito.when(handler.getByCountry("US"))
                .thenReturn(stateDtoList);
        assertThat(stateDtoList).hasSize(2);
    }

    @Test
    public void givenCountryCodeAndIso2_whenFindByCountryAndIso2_thenReturnState() {
        StateDto stateDto = StateDto.builder().id(1L).name("Texas").countryCode("US").iso2("TX").build();
        Mockito.when(handler.getByCountryAndIso2("US","TX"))
                .thenReturn(stateDto);
        assertThat(stateDto.getIso2()).isEqualTo("TX");
    }
}
