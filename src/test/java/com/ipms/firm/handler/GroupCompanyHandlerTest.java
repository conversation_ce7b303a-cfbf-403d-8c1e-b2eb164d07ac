package com.ipms.firm.handler;


import com.ipms.firm.dto.GroupCompanyDto;
import com.ipms.firm.handler.impl.GroupCompanyHandlerImpl;
import com.ipms.firm.mapper.GroupCompanyMapper;
import com.ipms.firm.model.GroupCompany;
import com.ipms.firm.service.GroupCompanyService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

@RunWith(SpringRunner.class)
public class GroupCompanyHandlerTest {

    @MockBean
    private GroupCompanyHandler handler;

    @Mock
    private GroupCompanyService service;

    @Mock
    private GroupCompanyMapper mapper;

    @Before
    public void setUp() {
        handler = new GroupCompanyHandlerImpl(service, mapper);
    }

    @Test
    public void whenGetAll_thenReturnGroupCompanyDtoList() {
        var groupCompanyDto = GroupCompanyDto.builder()
                .name("testName")
                .id(1L)
                .build();
        var groupCompany = GroupCompany.builder()
                .id(1L)
                .name("testName")
                .build();
        List<GroupCompany> groupCompanyList = new ArrayList<>(List.of(groupCompany));
        Mockito.when(service.getAll())
                .thenReturn(groupCompanyList);
        Mockito.when(mapper.toGroupCompanyDto(groupCompany))
                .thenReturn(groupCompanyDto);
        assertThat(handler.getAll())
                .isNotNull();
    }

    @Test
    public void whenGetByName_thenReturnGroupCompanyDtoList() {
        var groupCompanyDto = GroupCompanyDto.builder()
                .name("testName")
                .id(1L)
                .build();
        var groupCompany = GroupCompany.builder()
                .id(1L)
                .name("testName")
                .build();
        Mockito.when(service.getByName(Mockito.anyString()))
                .thenReturn(List.of(groupCompany));
        Mockito.when(mapper.toGroupCompanyDto(groupCompany))
                .thenReturn(groupCompanyDto);
        assertThat(handler.getByName("testName").get(0).getName())
                .isEqualTo("testName");
    }

}