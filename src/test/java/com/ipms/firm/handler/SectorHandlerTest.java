package com.ipms.firm.handler;

import com.ipms.firm.dto.SectorDto;
import com.ipms.firm.handler.impl.SectorHandlerImpl;
import com.ipms.firm.mapper.SectorMapper;
import com.ipms.firm.model.Sector;
import com.ipms.firm.service.SectorService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

@RunWith(SpringRunner.class)
public class SectorHandlerTest {

    @MockBean
    private SectorHandler handler;

    @Mock
    private SectorService service;

    @Mock
    private SectorMapper mapper;

    @Before
    public void setUp() {
        handler = new SectorHandlerImpl(service, mapper);
    }

    @Test
    public void whenGetAll_thenReturnSectorDtoList() {
        SectorDto sectorDto = SectorDto
                .builder()
                .id(1L)
                .nameTr("nameTrTest")
                .build();
        Sector sector = Sector
                .builder()
                .id(1L)
                .nameTr("nameTrTest")
                .build();
        List<Sector> sectorList = new ArrayList<>(List.of(sector));
        Mockito.when(service.getAll())
                .thenReturn(sectorList);
        Mockito.when(mapper.toSectorDto(sector))
                .thenReturn(sectorDto);
        assertThat(handler.getAll())
                .isNotNull();
    }
}