package com.ipms.firm.handler;

import com.ipms.firm.dto.ContactDto;
import com.ipms.firm.dto.ContactPageDto;
import com.ipms.firm.handler.impl.ContactHandlerImpl;
import com.ipms.firm.mapper.ContactMapper;
import com.ipms.firm.model.Contact;
import com.ipms.firm.service.ContactService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;

@RunWith(SpringRunner.class)
public class ContactHandlerTest {

    @MockBean
    private ContactHandler handler;

    @Mock
    private ContactService service;

    @Mock
    private ContactMapper mapper;

    @Before
    public void setUp() {
        handler = new ContactHandlerImpl(service, mapper);
    }

    @Test
    public void givenId_whenDelete_thenDoesNotThrowAnyException() {
        Mockito.doNothing().when(service).delete(Mockito.anyLong());
        assertThatCode(() -> handler.delete(1L))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenMailAddress_whenCheckByMailAddress_thenReturnBoolean() {
        var contact = Contact
                .builder()
                .id(1L)
                .mailName("test-mail-name")
                .mailAddress("<EMAIL>")
                .build();
        Mockito.when(service.getOptionalByMailAddress(Mockito.anyString())).thenReturn(Optional.of(contact));
        assertThat(handler.checkByMailAddress("<EMAIL>"))
                .isTrue();
    }

    @Test
    public void when_DeleteByEmail_thenDoesNotThrowAnyException() {
        Mockito.doNothing().when(service).delete(Mockito.anyLong());
        var contact = Contact
                .builder()
                .id(1L)
                .mailName("test-mail-name")
                .mailAddress("<EMAIL>")
                .build();
        Mockito.when(service.getOptionalByMailAddress(Mockito.anyString())).thenReturn(Optional.of(contact));
        assertThatCode(() -> handler.deleteByEmail("<EMAIL>"))
                .doesNotThrowAnyException();
    }

    @Test
    public void whenDeleteByContactIdAndFirmId_thenDoesNotThrowAnyException() {
        Mockito.doNothing().when(service).delete(Mockito.anyLong(),Mockito.anyLong());
        assertThatCode(() -> handler.delete(1L,1L))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenFirmIdPageSize_whenGetContactListByFirm_thenReturnContactPageDto() {
        var contactDto = ContactDto
                .builder()
                .id(1L)
                .mailName("test-mail-name")
                .mailAddress("<EMAIL>")
                .build();
        var contactPageDto = ContactPageDto
                .builder()
                .contacts(List.of(contactDto))
                .build();
        Mockito.when(service.getContactListByFirm(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(contactPageDto);
        assertThat(handler.getContactListByFirm(1L, 0, 15).getContacts().get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenMailAddress_whenGetContactListByMailAddress_thenReturnContactList() {
        var contact = Contact
                .builder()
                .id(1L)
                .mailName("test-mail-name")
                .mailAddress("<EMAIL>")
                .build();
        Mockito.when(service.getContactListByMailAddress(Mockito.anyString()))
                .thenReturn(List.of(contact));
        assertThat(handler.getContactListByMailAddress("<EMAIL>"))
                .isNotNull();
    }
}
