package com.ipms.firm.handler;

import com.ipms.config.storage.model.StorageFile;
import com.ipms.config.storage.service.StorageService;
import com.ipms.firm.dto.LeadAttachmentDto;
import com.ipms.firm.handler.impl.LeadAttachmentHandlerImpl;
import com.ipms.firm.mapper.LeadAttachmentMapper;
import com.ipms.firm.model.Lead;
import com.ipms.firm.model.LeadAttachment;
import com.ipms.firm.service.LeadAttachmentService;
import com.ipms.firm.service.LeadService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import javax.persistence.EntityNotFoundException;

import static org.junit.Assert.*;
import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class LeadAttachmentHandlerTest {
    @MockBean
    private LeadAttachmentHandler handler;

    @Mock
    private LeadAttachmentService leadAttachmentService;

    @Mock
    private StorageService storageService;

    @Mock
    private LeadService leadService;

    @Mock
    private LeadAttachmentMapper mapper;

    @Before
    public void setUp() {
        handler = new LeadAttachmentHandlerImpl(leadAttachmentService, leadService, mapper, storageService);
    }

    @Test
    public void givenLeadAttachmentDto_whenSave_thenReturnLeadAttachmentDto() {
        var dto = LeadAttachmentDto.builder()
                .id(1L)
                .leadId(100L)
                .fileName("test.pdf")
                .build();

        var lead = Lead.builder()
                .id(100L)
                .build();

        var attachment = LeadAttachment.builder()
                .id(1L)
                .fileName("test.pdf")
                .lead(lead)
                .build();

        var savedAttachment = LeadAttachment.builder()
                .id(1L)
                .fileName("test.pdf")
                .fileUniqueName("q123e4567-e89b-12d3-a456-426614174000.pdf")
                .lead(lead)
                .build();

        var savedDto = LeadAttachmentDto.builder()
                .id(1L)
                .leadId(100L)
                .fileName("test.pdf")
                .fileUniqueName("q123e4567-e89b-12d3-a456-426614174000.pdf")
                .fileSignedUrl("signed-url")
                .build();

        when(mapper.toAttachment(dto)).thenReturn(attachment);
        when(leadService.getById(dto.getLeadId())).thenReturn(lead);
        when(leadAttachmentService.save(new LeadAttachment())).thenReturn(savedAttachment);
        when(mapper.toAttachmentDto(any())).thenReturn(savedDto);
        when(mapper.toStorageFile(savedDto)).thenReturn(new StorageFile());
        when(storageService.generateSas(new StorageFile())).thenReturn("signed-url");

        var result = handler.save(dto);

        assertEquals(savedDto.getId(), result.getId());
        assertEquals(savedDto.getLeadId(), result.getLeadId());
        assertEquals(savedDto.getFileName(), result.getFileName());
        assertEquals(savedDto.getFileSignedUrl(), result.getFileSignedUrl());
        assertTrue(result.getFileUniqueName().endsWith(".pdf"));
    }

    @Test
    public void givenNonExistingLead_whenSave_thenThrowException() {
        var dto = LeadAttachmentDto.builder()
                .leadId(123L)
                .build();

        when(leadService.getById(123L)).thenThrow(new EntityNotFoundException("Lead not found"));

        assertThrows(EntityNotFoundException.class, () -> handler.save(dto));
        verify(leadService).getById(123L);
        verifyNoInteractions(leadAttachmentService, storageService);
    }
}
