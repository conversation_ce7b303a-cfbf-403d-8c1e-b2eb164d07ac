package com.ipms.firm.handler;

import com.ipms.firm.dto.ProvinceDto;
import com.ipms.firm.handler.impl.ProvinceHandlerImpl;
import com.ipms.firm.mapper.ProvinceMapper;
import com.ipms.firm.service.ProvinceService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;

@RunWith(SpringRunner.class)
public class ProvinceHandlerTest {

    @MockBean
    private ProvinceHandler handler;

    @Mock
    private ProvinceService service;

    @Mock
    private ProvinceMapper mapper;

    @Before
    public void setUp() {
        handler = new ProvinceHandlerImpl(service, mapper);
    }

    @Test
    public void givenCountryCodeAndCityCode_whenFindByCountryAndCity_thenReturnProvinceList() {
        List<ProvinceDto> provinceDtoList = new ArrayList<>();
        provinceDtoList.add(ProvinceDto.builder().id(1L).name("Adalar").countryCode("TR").cityCode("34").build());
        provinceDtoList.add(ProvinceDto.builder().id(2L).name("Beykoz").countryCode("TR").cityCode("34").build());
        Mockito.when(handler.getByCountryAndCity("TR","34"))
                .thenReturn(provinceDtoList);
        assertThat(provinceDtoList).hasSize(2);
    }

    @Test
    public void givenCountryCodeAndCityCodeAndProvinceName_whenFindByCountryCodeAndCityCodeAndName_thenReturnProvince() {
        ProvinceDto provinceDto = ProvinceDto.builder().id(1L).name("Adalar").countryCode("TR").cityCode("34").build();
        Mockito.when(handler.getByCountryAndCityAndProvince("TR","34","Adalar"))
                .thenReturn(provinceDto);
        assertThat(provinceDto.getName()).isEqualTo("Adalar");
    }
}
