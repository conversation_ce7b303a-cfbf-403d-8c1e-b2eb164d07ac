package com.ipms.firm.handler;

import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.enums.Domain;
import com.ipms.core.common.enums.TransferType;
import com.ipms.core.common.model.TransferEvent;
import com.ipms.firm.dto.*;
import com.ipms.firm.enums.FirmRole;
import com.ipms.firm.handler.impl.FirmHandlerImpl;
import com.ipms.firm.mapper.ContactMapper;
import com.ipms.firm.mapper.FirmMapper;
import com.ipms.firm.mapper.FirmMapperImpl;
import com.ipms.firm.mapper.GroupCompanyMapper;
import com.ipms.firm.model.*;
import com.ipms.firm.service.*;
import com.ipms.firm.validator.FirmValidator;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;
import static org.junit.jupiter.api.Assertions.assertEquals;


@RunWith(SpringRunner.class)
public class FirmHandlerTest {

    @MockBean
    private FirmHandler handler;

    @Mock
    private FirmService service;

    private final FirmMapper mapper = new FirmMapperImpl();

    @Mock
    private CountryHandler countryHandler;

    @Mock
    private StateHandler stateHandler;

    @Mock
    private CityHandler cityHandler;

    @Mock
    private ProvinceHandler provinceHandler;

    @Mock
    private FirmValidator validator;

    @Mock
    private GroupCompanyService groupCompanyService;

    @Mock
    private SectorService sectorService;

    @Mock
    private ContactMapper contactMapper;

    @Mock
    private ContactService contactService;

    @Mock
    private ProducerService producerService;

    @Mock
    private CityService cityService;

    @Mock
    private GroupCompanyMapper groupCompanyMapper;

    @Before
    public void setUp() {
        handler = new FirmHandlerImpl(countryHandler, service, mapper, validator, cityHandler, provinceHandler,
                stateHandler, contactService, contactMapper, sectorService, groupCompanyService, producerService,
                cityService, groupCompanyMapper);
    }

    @Test
    public void givenTitle_whenListFirmSummaryDtoList_thenReturnFirmSummaryDtoList() {
        var firm = Firm
                .builder()
                .id(1L)
                .country("TR")
                .city("34")
                .build();
        Map<String, City> cityMap = new HashMap<>();
        var city = City.builder().id(1L).iso2("34").name("34").countryCode("TR").build();
        cityMap.put("TR34",city);
        var firmList = new ArrayList<>(List.of(firm));
        Mockito.when(service.getFirmListByTitle(Mockito.anyString()))
                .thenReturn(firmList);
        Mockito.when(cityService.getByIso2Map(Mockito.anyList()))
                .thenReturn(cityMap);
        assertThatCode(() -> handler.listFirmSummaryDtoList(Mockito.anyString()))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenId_whenGetByIdAndStateIsNotRequired_thenReturnFirm() {
        var firm = Firm.builder()
                .id(1L)
                .country("testCountry")
                .city("testCity")
                .state("testState")
                .province("testProvince")
                .build();
        var countryDto = CountryDto
                .builder()
                .id(1L)
                .name("test")
                .translations("testTranslation")
                .isProvinceRequired(true)
                .isStateRequired(false)
                .build();
        var cityDto = CityDto
                .builder()
                .id(1L)
                .name("testName")
                .build();
        var provinceDto = ProvinceDto
                .builder()
                .id(1L)
                .name("province-name")
                .build();
        Mockito.when(service.getFirmById(Mockito.anyLong()))
                .thenReturn(firm);
        Mockito.when(countryHandler.getByIso2(Mockito.anyString()))
                .thenReturn(countryDto);
        Mockito.when(cityHandler.getByCountryAndIso2(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(cityDto);
        Mockito.when(provinceHandler.getProvince(Mockito.anyString(), Mockito.anyString(), Mockito.anyString()))
                .thenReturn(provinceDto);
        assertThat(handler.getById(Mockito.anyLong()))
                .isNotNull();
    }

    @Test
    public void givenId_whenGetByIdAndProvinceIsNotRequired_thenReturnFirm() {
        var firm = Firm.builder()
                .id(1L)
                .country("testCountry")
                .city("testCity")
                .state("testState")
                .build();
        var countryDto = CountryDto
                .builder().id(1L)
                .name("test")
                .translations("testTranslation")
                .isProvinceRequired(false)
                .isStateRequired(true)
                .build();
        var cityDto = CityDto
                .builder()
                .id(1L)
                .name("testName")
                .build();
        var stateDto = StateDto
                .builder()
                .id(1L)
                .build();
        Mockito.when(service.getFirmById(Mockito.anyLong()))
                .thenReturn(firm);
        Mockito.when(countryHandler.getByIso2(Mockito.anyString()))
                .thenReturn(countryDto);
        Mockito.when(stateHandler.getByCountryAndIso2(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(stateDto);
        Mockito.when(cityHandler.getByCountryAndIso2(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(cityDto);
        assertThat(handler.getById(Mockito.anyLong()))
                .isNotNull();
    }

    @Test
    public void givenFirmDto_whenSave_thenReturnFirmDto() {
        var sector = Sector.builder().id(1L).build();
        Set<Sector> sectorSet = new HashSet<>(Set.of(sector));
        var sectorDto = SectorDto.builder().id(1L).build();
        List<SectorDto> sectorDtoList = new ArrayList<>(List.of(sectorDto));
        var agentInfoDto = AgentInfoDto.builder()
                .emailAddress("<EMAIL>").build();
        var agentInfo = AgentInfo.builder()
                .emailAddress("<EMAIL>").build();
        var rightOwnerInfo = RightOwnerInfo.builder()
                .id(1L).taxOffice("testTaxOffice").sectors(sectorSet).build();
        var rightOwnerInfoDto = RightOwnerInfoDto.builder()
                .taxOffice("testTaxOffice").sectors(sectorDtoList).build();
        var groupCompanyDto = GroupCompanyDto.builder()
                .id(1L).name("testName").build();
        var groupCompany = GroupCompany.builder()
                .id(1L).name("testName").build();
        var contactDto = ContactDto.builder().mailAddress("<EMAIL>")
                .mailName("testName").build();
        var contact = Contact.builder().mailAddress("<EMAIL>")
                .mailName("testName").build();
        var firmDto = FirmDto.builder()
                .id(1L).rightOwnerInfo(rightOwnerInfoDto).type("LEGAL_ENTITY")
                .state("testState").title("testTitle").city("testCity").country("testCountry")
                .province("testProvince").address("testAddress")
                .agentInfo(agentInfoDto).groupCompany(groupCompanyDto)
                .contacts(new HashSet<>(Set.of(contactDto))).contact(contactDto).build();
        var firm = Firm.builder()
                .id(1L).rightOwnerInfo(rightOwnerInfo).state("testState")
                .title("testTitle").city("testCity").country("testCountry")
                .province("testProvince").address("testAddress")
                .agentInfo(agentInfo).groupCompany(groupCompany).build();
        Mockito.when(contactMapper.toContact(contactDto))
                .thenReturn(contact);
        Mockito.when(service.save(Mockito.any(Firm.class)))
                .thenReturn(firm);
        assertThat(handler.save(firmDto))
                .isNotNull();
    }

    @Test
    public void givenFirmDtoAndId_whenUpdate_thenReturnFirmDto() {
        var agentInfoDto = AgentInfoDto.builder()
                .emailAddress("<EMAIL>").build();
        var agentInfo = AgentInfo.builder()
                .emailAddress("<EMAIL>").build();
        var groupCompanyDto = GroupCompanyDto.builder()
                .id(1L).name("testName").build();
        var groupCompany = GroupCompany.builder()
                .id(1L).name("testName").build();
        var contactDto = ContactDto.builder().mailAddress("<EMAIL>")
                .mailName("testName").build();
        var contact = Contact.builder().mailAddress("<EMAIL>")
                .mailName("testName").build();
        var firmDto = FirmDto.builder()
                .id(1L).type("LEGAL_ENTITY").state("testState").title("testTitle")
                .city("testCity").country("testCountry")
                .province("testProvince").address("testAddress")
                .agentInfo(agentInfoDto).groupCompany(groupCompanyDto)
                .contacts(new HashSet<>(Set.of(contactDto))).contact(contactDto).build();
        var firm = Firm.builder()
                .id(1L).state("testState").title("testTitle")
                .city("testCity").country("testCountry")
                .province("testProvince").address("testAddress")
                .agentInfo(agentInfo).groupCompany(groupCompany).build();
        Mockito.when(service.getFirmById(Mockito.anyLong()))
                        .thenReturn(firm);
        Mockito.when(contactService.getOptionalByMailAddress(Mockito.anyString()))
                .thenReturn(Optional.of(contact));
        Mockito.when(service.save(Mockito.any(Firm.class)))
                .thenReturn(firm);
        assertThat(handler.update(firmDto, 1L))
                .isNotNull();
    }

    @Test
    public void givenFirmDto_whenSaveWithContact_thenReturnFirmDto(){
        var contact = Contact.builder().mailAddress("<EMAIL>")
                .mailName("testName").build();
        var contactDto = ContactDto
                .builder()
                .mailAddress("<EMAIL>")
                .mailName("testName")
                .build();
        var firmDto = FirmDto.builder()
                .id(1L).type("LEGAL_ENTITY").state("testState").title("testTitle")
                .city("testCity").country("testCountry")
                .province("testProvince").address("testAddress").contact(contactDto)
                .contacts(new HashSet<>(Set.of(contactDto)))
                .build();
        var firm = Firm.builder()
                .id(1L).state("testState")
                .title("testTitle").city("testCity").country("testCountry")
                .province("testProvince").address("testAddress")
                .build();
        Mockito.when(contactMapper.toContact(contactDto))
                .thenReturn(contact);
        Mockito.when(service.save(Mockito.any(Firm.class)))
                .thenReturn(firm);
        assertThat(handler.saveWithContact(firmDto))
                .isNotNull();
    }

    @Test
    public void givenGroupCompanyId_whenGetFirmListByGroupCompany_thenReturnFirmDtoList(){
        var firm = Firm.builder()
                .id(1L).state("testState")
                .title("testTitle").city("testCity").country("testCountry")
                .province("testProvince").address("testAddress")
                .build();
        Mockito.when(service.getFirmListByGroupCompany(Mockito.anyLong())).thenReturn(List.of(firm));
        assertThat(handler.getFirmListByGroupCompany(1L).get(0).getTitle())
                .isEqualTo("testTitle");
    }

    @Test
    public void givenTitleAndRole_whenGetFirmListByTitleAndRole_thenReturnFirmSummaryDtoList(){
        var firm = Firm.builder()
                .id(1L).state("testState")
                .title("testTitle").city("testCity").country("testCountry")
                .province("testProvince").address("testAddress")
                .build();
        var city = City.builder()
                .id(1L)
                .name("testName")
                .build();
        Mockito.when(service.getFirmListByTitleAndRole(Mockito.anyString(), Mockito.anyList())).thenReturn(List.of(firm));
        Mockito.when(cityService.getByCountryAndIso2(Mockito.anyString(), Mockito.anyString())).thenReturn(city);
        assertThat(handler.getFirmListByTitleAndRole("testTitle", FirmRole.AGENT.getValue()).get(0).getTitle())
                .isEqualTo("testTitle");
    }

    @Test
    public void whenGetByIdIn_thenReturnDtoList() {
        var firm = Firm.builder()
                .id(1L)
                .title("testTitle")
                .city("34")
                .country("TR")
                .province("beykoz")
                .address("testAddress")
                .build();
        Mockito.when(service.getByIdIn(Mockito.anyList()))
                .thenReturn(List.of(firm));
        Assertions.assertThat(service.getByIdIn(List.of(1L,2L)))
                .isNotEmpty();
    }

    @Test
    public void whenUpdateGroupCompany_thenReturnFirmDto() {
        var groupCompany = GroupCompany.builder()
                .id(1L)
                .name("test-group-company")
                .build();
        var firm = Firm.builder()
                .id(1L)
                .title("testTitle")
                .city("34")
                .country("TR")
                .province("beykoz")
                .address("testAddress")
                .version(0L)
                .groupCompany(groupCompany)
                .build();
        var groupCompanyDto = GroupCompanyDto.builder()
                .name("test-group-company")
                .build();
        Mockito.when(groupCompanyMapper.toGroupCompany(groupCompanyDto))
                .thenReturn(groupCompany);
        Mockito.when(service.updateGroupCompany(1L, groupCompany, 1L))
                .thenReturn(firm);
        assertThat(handler.updateGroupCompany(1L, groupCompanyDto, 1L).getGroupCompany().getName())
                .isEqualTo("test-group-company");
    }

    @Test
    public void whenDelete_thenDoesNotThrowAnyException(){
        var transferEvent = TransferEvent
                .builder()
                .type(TransferType.DELETE)
                .domainObjectId(1L)
                .domain(Domain.IPMS_FIRM)
                .build();
        Mockito.doNothing().when(service).delete(Mockito.anyLong(), Mockito.anyLong());
        Mockito.doNothing().when(producerService).sendTransfer(transferEvent);
        assertThatCode(() -> handler.delete(1L, 1L)).doesNotThrowAnyException();
    }

    @Test
    public void whenGetByIdIn_thenReturnFirmList(){
        var firm = Firm.builder().id(1L).city("34").country("TR").build();
        var firmList = List.of(firm);
        var city = City.builder().id(1L).name("Ist").countryCode("TR").iso2("34").build();
        Map<String, City> cityMap = new HashMap();
        cityMap.put("TR34", city);
        Mockito.when(service.getByIdIn(List.of(1L))).thenReturn(firmList);
        Mockito.when(cityService.getByIso2Map(List.of("34"))).thenReturn(cityMap);
        assertThat(handler.getByIdIn(List.of(1L)).get(0).getCity())
                .isEqualTo("34");
    }

    @Test
    public void whenDeleteAgent_thenDoesNotThrowAnyException(){
        var transferEvent = TransferEvent
                .builder()
                .type(TransferType.DELETE)
                .domainObjectId(1L)
                .domain(Domain.IPMS_FIRM)
                .build();
        Mockito.doNothing().when(service).deleteAgent(Mockito.anyLong(), Mockito.anyLong());
        Mockito.doNothing().when(producerService).sendTransfer(transferEvent);
        assertThatCode(() -> handler.deleteAgent(1L, 1L)).doesNotThrowAnyException();
    }

    @Test
    public void whenDeleteRightOwner_thenDoesNotThrowAnyException(){
        var transferEvent = TransferEvent
                .builder()
                .type(TransferType.DELETE)
                .domainObjectId(1L)
                .domain(Domain.IPMS_FIRM)
                .build();
        Mockito.doNothing().when(service).deleteRightOwner(Mockito.anyLong(), Mockito.anyLong());
        Mockito.doNothing().when(producerService).sendTransfer(transferEvent);
        assertThatCode(() -> handler.deleteRightOwner(1L, 1L)).doesNotThrowAnyException();
    }

    @Test
    public void whenGetFirmSummaryPage_thenReturnFirmSummaryPage() {
        Page<Firm> page = new PageImpl<>(List.of(new Firm(), new Firm()));
        Mockito.when(service.getFirmPage(Mockito.any(FirmFilterRequest.class), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(page);
        var pageDto = handler.getFirmSummaryPage(Mockito.mock(FirmFilterRequest.class), 1, 10);

        assertEquals(1, pageDto.getTotalPages());
        assertEquals(2, pageDto.getTotalElements());
        assertEquals(2, pageDto.getContent().size());
    }

    @Test
    public void givenIdAndBillingAccountId_whenLinkBillingAccount_thenReturnFirmDto(){
        var billingAccountIds = List.of(1L, 2L);
        var firm = Firm.builder()
                .id(1L)
                .billingAccountIds(billingAccountIds)
                .build();
        Mockito.when(service.linkBillingAccount(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(firm);
        assertThat(handler.linkBillingAccount(1L, 1L).getBillingAccountIds())
                .isEqualTo(billingAccountIds);
    }
}