package com.ipms.firm.handler;

import com.ipms.firm.dto.LeadDto;
import com.ipms.firm.dto.LeadFilterRequest;
import com.ipms.firm.dto.SectorDto;
import com.ipms.firm.enums.Quadrant;
import com.ipms.firm.handler.impl.LeadHandlerImpl;
import com.ipms.firm.mapper.LeadMapper;
import com.ipms.firm.model.Lead;
import com.ipms.firm.model.Sector;
import com.ipms.firm.service.LeadService;
import com.ipms.firm.service.SectorService;
import com.ipms.firm.specification.LeadSpecification;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.junit4.SpringRunner;

import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.junit.Assert.assertThrows;
import static org.junit.Assert.assertTrue;
import static org.junit.jupiter.api.Assertions.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class LeadHandlerTest {
    @MockBean
    private LeadService service;

    @Mock
    private LeadMapper mapper;

    @Mock
    private SectorService sectorService;

    private LeadHandler handler;

    @Before
    public void setUp() {
        handler = new LeadHandlerImpl(service, mapper, sectorService);
    }

    @Test
    public void givenValidData_whenGetAll_thenReturnsListOfLeadDtos() {
        var lead1 = Lead.builder().id(1L).nameSurname("Lead1").phoneNumber("123456").build();
        var lead2 = Lead.builder().id(2L).nameSurname("Lead2").phoneNumber("654321").build();
        var leads = List.of(lead1, lead2);
        var leadDtos = List.of(
                LeadDto.builder().nameSurname("Lead1").phoneNumber("123456").build(),
                LeadDto.builder().nameSurname("Lead2").phoneNumber("654321").build()
        );

        when(service.getAll()).thenReturn(leads);
        when(mapper.toDtoList(leads)).thenReturn(leadDtos);

        var result = handler.getAll();

        assertThat(result).hasSize(2);
        verify(service, times(1)).getAll();
        verify(mapper, times(1)).toDtoList(leads);
    }

    @Test
    public void givenValidLeadDtoWithSector_whenSave_thenReturnsSavedLeadDto() {
        var sector = Sector.builder().id(1L).name("Test Sector").build();
        var sectorDto = SectorDto.builder().id(1L).name("Test Sector").build();
        var leadDto = LeadDto.builder()
                .nameSurname("Lead1")
                .phoneNumber("123456")
                .sector(sectorDto)
                .build();
        var lead = Lead.builder()
                .nameSurname("Lead1")
                .phoneNumber("123456")
                .sector(sector)
                .build();
        var savedLead = Lead.builder()
                .id(1L)
                .nameSurname("Lead1")
                .phoneNumber("123456")
                .sector(sector)
                .build();
        var savedLeadDto = LeadDto.builder()
                .nameSurname("Lead1")
                .phoneNumber("123456")
                .sector(sectorDto)
                .build();

        when(mapper.toLead(leadDto)).thenReturn(lead);
        when(sectorService.getById(1L)).thenReturn(sector);
        when(service.save(lead)).thenReturn(savedLead);
        when(mapper.toLeadDto(savedLead)).thenReturn(savedLeadDto);

        var result = handler.save(leadDto);

        assertThat(result).isNotNull();
        verify(service, times(1)).checkByNameAndPhoneNumber(leadDto.getNameSurname(), leadDto.getPhoneNumber());
        verify(mapper, times(1)).toLead(leadDto);
        verify(service, times(1)).save(lead);
        verify(mapper, times(1)).toLeadDto(savedLead);
    }

    @Test
    public void givenValidLeadDtoAndId_whenUpdate_thenReturnsUpdatedLeadDto() {
        var id = 1L;
        var sector = Sector.builder().id(1L).name("Test Sector").build();
        var sectorDto = SectorDto.builder().id(1L).name("Test Sector").build();
        var leadDto = LeadDto.builder()
                .nameSurname("Updated Lead")
                .phoneNumber("987654")
                .sector(sectorDto)
                .build();
        var oldLead = Lead.builder()
                .id(id)
                .nameSurname("Old Lead")
                .phoneNumber("123456")
                .createdAt(LocalDateTime.now())
                .createdBy("user")
                .build();
        var updatedLead = Lead.builder()
                .id(id)
                .nameSurname("Updated Lead")
                .phoneNumber("987654")
                .sector(sector)
                .createdAt(oldLead.getCreatedAt())
                .createdBy(oldLead.getCreatedBy())
                .build();

        when(service.getById(id)).thenReturn(oldLead);
        when(mapper.toLeadFromDto(leadDto, oldLead)).thenReturn(updatedLead);
        when(sectorService.getById(1L)).thenReturn(sector);
        when(service.save(updatedLead)).thenReturn(updatedLead);
        when(mapper.toLeadDto(updatedLead)).thenReturn(leadDto);

        var result = handler.update(leadDto, id);

        assertThat(result).isNotNull();
        verify(service, times(1)).getById(id);
        verify(mapper, times(1)).toLeadFromDto(leadDto, oldLead);
        verify(service, times(1)).save(updatedLead);
        verify(mapper, times(1)).toLeadDto(updatedLead);
    }

    @Test
    public void givenValidId_whenDelete_thenCallsServiceDelete() {
        var id = 1L;
        handler.delete(id);
        verify(service, times(1)).delete(id);
    }

    @Test
    public void givenLeadFilterRequest_whenGetAllPageable_thenReturnLeadPageDto() {
        var filterRequest = LeadFilterRequest.builder()
                .idList(List.of(1L, 2L))
                .statusList(List.of("NEW", "IN_PROGRESS"))
                .quadrantList(List.of(Quadrant.SUPERSTARS, Quadrant.POTENTIAL_STARS))
                .leadOwnerList(List.of("John"))
                .firmList(List.of("Company A"))
                .sortField("createdDate")
                .sortDirection("DESC")
                .build();

        var lead1 = Lead.builder()
                .id(1L)
                .leadStatus("NEW")
                .quadrant(Quadrant.SUPERSTARS)
                .leadOwner("John")
                .firm("Company A")
                .build();

        var lead2 = Lead.builder()
                .id(2L)
                .leadStatus("IN_PROGRESS")
                .quadrant(Quadrant.POTENTIAL_STARS)
                .leadOwner("John")
                .firm("Company A")
                .build();

        var leadDto1 = LeadDto.builder()
                .leadStatus("NEW")
                .quadrant("Superstars")
                .leadOwner("John")
                .firm("Company A")
                .build();

        var leadDto2 = LeadDto.builder()
                .leadStatus("IN_PROGRESS")
                .quadrant("Potential stars")
                .leadOwner("John")
                .firm("Company A")
                .build();

        var pageRequest = PageRequest.of(0, 10);
        var leadPage = new PageImpl<>(List.of(lead1, lead2), pageRequest, 2);

        when(service.getLeadPage(any(LeadSpecification.class), any(PageRequest.class)))
                .thenReturn(leadPage);
        when(mapper.toLeadDto(lead1)).thenReturn(leadDto1);
        when(mapper.toLeadDto(lead2)).thenReturn(leadDto2);

        var result = handler.getAllPageable(filterRequest, 0, 10);

        assertEquals(2, result.getTotalElements());
        assertEquals(1, result.getTotalPages());
        assertEquals(2, result.getLeadDtos().size());

        assertEquals(leadDto1.getLeadStatus(), result.getLeadDtos().get(0).getLeadStatus());
        assertEquals(leadDto1.getQuadrant(), result.getLeadDtos().get(0).getQuadrant());
        assertEquals(leadDto1.getLeadOwner(), result.getLeadDtos().get(0).getLeadOwner());
        assertEquals(leadDto1.getFirm(), result.getLeadDtos().get(0).getFirm());

        assertEquals(leadDto2.getLeadStatus(), result.getLeadDtos().get(1).getLeadStatus());
        assertEquals(leadDto2.getQuadrant(), result.getLeadDtos().get(1).getQuadrant());
        assertEquals(leadDto2.getLeadOwner(), result.getLeadDtos().get(1).getLeadOwner());
        assertEquals(leadDto2.getFirm(), result.getLeadDtos().get(1).getFirm());

        verify(service).getLeadPage(Mockito.any(LeadSpecification.class), Mockito.eq(pageRequest));
        verify(mapper, times(2)).toLeadDto(Mockito.any(Lead.class));
    }

    @Test
    public void givenInvalidPageRequest_whenGetAllPageable_thenThrowException() {
        var filterRequest = LeadFilterRequest.builder().build();

        Mockito.when(service.getLeadPage(
                Mockito.any(LeadSpecification.class),
                Mockito.any(PageRequest.class)
        )).thenAnswer(invocation -> {
            PageRequest pageRequest = invocation.getArgument(1);
            if (pageRequest.getPageNumber() < 0 || pageRequest.getPageSize() <= 0) {
                throw new IllegalArgumentException("Page index must not be less than zero!");
            }
            return null;
        });

        assertThrows(IllegalArgumentException.class, () ->
                handler.getAllPageable(filterRequest, -1, 0)
        );
    }

    @Test
    public void givenEmptyFilterRequest_whenGetAllPageable_thenReturnEmptyPage() {
        var filterRequest = LeadFilterRequest.builder().build();
        var pageRequest = PageRequest.of(0, 10);
        var emptyPage = new PageImpl<Lead>(Collections.emptyList(), pageRequest, 0);

        Mockito.when(service.getLeadPage(Mockito.any(LeadSpecification.class), Mockito.any(PageRequest.class)))
                .thenReturn(emptyPage);

         var result = handler.getAllPageable(filterRequest, 0, 10);

        assertEquals(0, result.getTotalElements());
        assertEquals(0, result.getTotalPages());
        assertTrue(result.getLeadDtos().isEmpty());

        verify(service).getLeadPage(Mockito.any(LeadSpecification.class), Mockito.eq(pageRequest));
        verifyNoInteractions(mapper);
    }

    @Test
    public void givenValidLeadIdRequest_whenGetById_thenReturnLeadDto() {
        var lead = Lead.builder()
                .id(1L)
                .leadStatus("NEW")
                .leadOwner("John")
                .firm("Company A")
                .build();

        var leadDto = LeadDto.builder()
                .leadStatus("NEW")
                .leadOwner("John")
                .firm("Company A")
                .build();

        when(service.getById(123L)).thenReturn(lead);
        when(mapper.toLeadDto(lead)).thenReturn(leadDto);

        var result = handler.getById(123L);

        assertEquals(leadDto, result);
    }
}