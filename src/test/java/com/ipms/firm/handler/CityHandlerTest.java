package com.ipms.firm.handler;

import com.ipms.firm.dto.CityDto;
import com.ipms.firm.handler.impl.CityHandlerImpl;
import com.ipms.firm.mapper.CityMapper;
import com.ipms.firm.model.City;
import com.ipms.firm.service.CityService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;

@RunWith(SpringRunner.class)
public class CityHandlerTest {

    @MockBean
    private CityHandler handler;

    @Mock
    private CityService service;

    @Mock
    private CityMapper mapper;

    @Before
    public void setUp() {
        handler = new CityHandlerImpl(service, mapper);
    }

    @Test
    public void givenCountryCode_whenFindByCountry_thenReturnCityList() {
        List<CityDto> cityDtoList = new ArrayList<>();
        cityDtoList.add(CityDto.builder().id(1L).name("Istanbul").countryCode("TR").iso2("34").build());
        cityDtoList.add(CityDto.builder().id(2L).name("Ankara").countryCode("TR").iso2("06").build());
        Mockito.when(handler.getByCountry("TR"))
                .thenReturn(cityDtoList);
        assertThat(cityDtoList).hasSize(2);
    }

    @Test
    public void givenCountryCodeAndStateCode_whenFindByCountryAndState_thenReturnCityList() {
        ArrayList<CityDto> cityDtoList = new ArrayList<>();
        cityDtoList.add(CityDto.builder().id(1L).countryCode("US").name("Bethpage").stateCode("NY").build());
        cityDtoList.add(CityDto.builder().id(2L).countryCode("US").name("Big Flats").stateCode("NY").build());
        Mockito.when(handler.getByCountryAndState("US","NY"))
                .thenReturn(cityDtoList);
        assertThat(cityDtoList).hasSize(2);
    }

    @Test
    public void givenCountryCodeAndIso2_whenFindByCountryAndIso2_thenReturnCity() {
        CityDto cityDto = CityDto.builder().id(1L).countryCode("US").name("Bethpage").stateCode("NY").iso2("112293").build();
        Mockito.when(handler.getByCountryAndIso2("US","112293"))
                .thenReturn(cityDto);
        assertThat(cityDto.getIso2()).isEqualTo("112293");
    }

    @Test
    public void givenCityDto_whenSave_thenReturnCityDto() {
        CityDto cityDto = CityDto
                .builder()
                .countryCode("US")
                .name("Bethpage")
                .stateCode("NY")
                .build();
        City city = City
                .builder()
                .countryCode("US")
                .name("Bethpage")
                .id(112293L)
                .stateCode("NY")
                .build();
        Mockito.when(mapper.toCity(Mockito.any(CityDto.class)))
                        .thenReturn(city);
        Mockito.when(service.getByIso2AndCountryCode(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(Collections.emptyList());
        Mockito.when(service.save(Mockito.any(City.class)))
                .thenReturn(city);
        Mockito.when(mapper.toCityDto(Mockito.any(City.class)))
                .thenReturn(cityDto);
        assertThat(handler.save(cityDto).getName())
                .isEqualTo("Bethpage");
    }

    @Test
    public void givenCityDto_whenSaveAndNewIso2_thenReturnCityDto() {
        CityDto cityDto = CityDto
                .builder()
                .countryCode("US")
                .name("Bethpage")
                .stateCode("NY")
                .build();
        City city = City
                .builder()
                .countryCode("US")
                .name("Bethpage")
                .id(112293L)
                .stateCode("NY")
                .build();
        Mockito.when(mapper.toCity(Mockito.any(CityDto.class)))
                .thenReturn(city);
        Mockito.when(service.getByIso2AndCountryCode(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(Collections.emptyList());
        Mockito.when(service.save(Mockito.any(City.class)))
                .thenReturn(city);
        Mockito.when(mapper.toCityDto(Mockito.any(City.class)))
                .thenReturn(cityDto);
        assertThat(handler.save(cityDto).getName())
                .isEqualTo("Bethpage");
    }

    @Test
    public void givenIdAndVersion_whenDelete_thenDoesNotThrowAnyException() {
        Mockito.doNothing().when(service)
                .delete(Mockito.anyLong());
        assertThatCode(() -> handler.delete(1L))
                .doesNotThrowAnyException();
    }
}
