package com.ipms.firm.service;

import com.ipms.firm.model.AgentInfo;
import com.ipms.firm.repository.AgentInfoRepository;
import com.ipms.firm.service.impl.AgentInfoServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

@RunWith(SpringRunner.class)
public class AgentServiceTest {

    @MockBean
    private AgentInfoService service;

    @Mock
    private AgentInfoRepository repository;

    @Before
    public void setUp() {
        service = new AgentInfoServiceImpl(repository);
    }

    @Test
    public void givenId_whenGetById_thenReturnAgent() {
        var agent = AgentInfo.builder()
                .id(1L)
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(agent));
        assertThat(service.getById(1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenSave_thenReturnAgent() {
        var agent = AgentInfo.builder()
                .id(1L)
                .build();
        Mockito.when(repository.save(Mockito.any(AgentInfo.class)))
                .thenReturn(agent);
        assertThat(service.save(agent).getId())
                .isEqualTo(1L);
    }
}
