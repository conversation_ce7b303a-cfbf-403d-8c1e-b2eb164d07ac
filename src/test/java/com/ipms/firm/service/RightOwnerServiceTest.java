package com.ipms.firm.service;

import com.ipms.firm.model.RightOwnerInfo;
import com.ipms.firm.repository.RightOwnerRepository;
import com.ipms.firm.service.impl.RightOwnerInfoServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

@RunWith(SpringRunner.class)
public class RightOwnerServiceTest {

    @MockBean
    private RightOwnerService service;

    @Mock
    private RightOwnerRepository repository;

    @Before
    public void setUp() {
        service = new RightOwnerInfoServiceImpl(repository);
    }

    @Test
    public void givenId_whenGetById_thenReturnAgent() {
        var rightOwnerInfo = RightOwnerInfo.builder()
                .id(1L)
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(rightOwnerInfo));
        assertThat(service.getById(1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenSave_thenReturnAgent() {
        var rightOwnerInfo = RightOwnerInfo.builder()
                .id(1L)
                .build();
        Mockito.when(repository.save(Mockito.any(RightOwnerInfo.class)))
                .thenReturn(rightOwnerInfo);
        assertThat(service.save(rightOwnerInfo).getId())
                .isEqualTo(1L);
    }
}
