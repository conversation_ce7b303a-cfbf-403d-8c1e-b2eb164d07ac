package com.ipms.firm.service;

import com.ipms.firm.exception.CountryNotFoundException;
import com.ipms.firm.model.Country;
import com.ipms.firm.repository.CountryRepository;
import com.ipms.firm.service.impl.CountryServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;

@RunWith(SpringRunner.class)
public class CountryServiceTest {

    @MockBean
    private CountryService service;

    @Mock
    private CountryRepository repository;

    @Before
    public void setUp() {
        service = new CountryServiceImpl(repository);
    }

    @Test
    public void givenId_whenGetById_thenReturnCountry() {
        Country country = Country.builder()
                .id(1L)
                .name("Turkey")
                .iso2("TR")
                .iso3("TUR")
                .build();
        
        Mockito.when(repository.findById(1L))
                .thenReturn(Optional.of(country));
        
        Country result = service.getById(1L);
        
        assertThat(result).isNotNull();
        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getName()).isEqualTo("Turkey");
        assertThat(result.getIso2()).isEqualTo("TR");
    }

    @Test
    public void givenNonExistentId_whenGetById_thenThrowCountryNotFoundException() {
        Mockito.when(repository.findById(999L))
                .thenReturn(Optional.empty());
        
        assertThatThrownBy(() -> service.getById(999L))
                .isInstanceOf(CountryNotFoundException.class);
    }

    @Test
    public void givenIso2_whenGetByIso2_thenReturnCountry() {
        Country country = Country.builder()
                .id(1L)
                .name("Turkey")
                .iso2("TR")
                .iso3("TUR")
                .build();
        
        Mockito.when(repository.findByIso2("TR"))
                .thenReturn(Optional.of(country));
        
        Country result = service.getByIso2("TR");
        
        assertThat(result).isNotNull();
        assertThat(result.getIso2()).isEqualTo("TR");
        assertThat(result.getName()).isEqualTo("Turkey");
    }
}