package com.ipms.firm.service;

import com.ipms.firm.enums.LeadResponseCode;
import com.ipms.firm.exception.LeadException;
import com.ipms.firm.model.Lead;
import com.ipms.firm.repository.LeadRepository;
import com.ipms.firm.service.impl.LeadServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.assertThatThrownBy;
import static org.assertj.core.api.AssertionsForInterfaceTypes.assertThat;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class LeadServiceTest {
    @MockBean
    private LeadService service;

    @Mock
    LeadRepository repository;

    @Before
    public void setUp() {
        service = new LeadServiceImpl(repository);
    }

    @Test
    public void givenValidData_whenGetAll_thenReturnsListOfLeads() {
        var lead1 = Lead.builder().id(1L).nameSurname("Lead1").phoneNumber("123456").build();
        var lead2 = Lead.builder().id(2L).nameSurname("Lead2").phoneNumber("654321").build();
        List<Lead> leads = List.of(lead1, lead2);
        when(repository.findAll()).thenReturn(leads);

        List<Lead> result = service.getAll();

        assertThat(result).hasSize(2);
        verify(repository, times(1)).findAll();
    }

    @Test
    public void givenValidId_whenGetById_thenReturnsLead() {
        Long id = 1L;
        var lead = Lead.builder().id(id).nameSurname("Lead1").phoneNumber("123456").build();
        when(repository.findById(id)).thenReturn(Optional.of(lead));

        Lead result = service.getById(id);

        assertThat(result).isNotNull();
        verify(repository, times(1)).findById(id);
    }

    @Test
    public void givenInvalidId_whenGetById_thenThrowsLeadException() {
        var id = 1L;

        when(repository.findById(id))
                .thenReturn(Optional.empty());

        assertThatThrownBy(() -> service.getById(id))
                .isInstanceOf(LeadException.class)
                .extracting("code")
                .isEqualTo(LeadResponseCode.LEAD_NOT_FOUND);

        verify(repository, times(1)).findById(id);
    }

    @Test
    public void givenValidLead_whenSave_thenReturnsSavedLead() {
        var lead = Lead.builder().id(1L).nameSurname("Lead1").phoneNumber("123456").build();
        when(repository.save(lead)).thenReturn(lead);

        Lead result = service.save(lead);

        assertThat(result).isNotNull();
        verify(repository, times(1)).save(lead);
    }

    @Test
    public void givenValidId_whenDelete_thenMarksLeadAsDeleted() {
        Long id = 1L;
        Lead lead = Lead.builder()
                .id(id)
                .isDeleted(false)
                .build();

        Lead expectedLead = lead.toBuilder()
                .isDeleted(true)
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(lead));
        when(repository.save(expectedLead)).thenReturn(expectedLead);

        service.delete(id);

        verify(repository).findById(id);
        verify(repository).save(expectedLead);
    }

    @Test
    public void givenInvalidId_whenDelete_thenThrowsLeadException() {
        var id = 1L;
        when(repository.findById(id)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> service.delete(id))
                .isInstanceOf(LeadException.class)
                .extracting("code")
                .isEqualTo(LeadResponseCode.LEAD_NOT_FOUND);
        verify(repository, never()).save(any());
    }

    @Test
    public void givenExistingNameAndPhone_whenCheckByNameAndPhoneNumber_thenThrowsLeadException() {
        var nameSurname = "Lead1";
        var phoneNumber = "1234567890";

        var lead = Lead.builder()
                .id(1L)
                .nameSurname(nameSurname)
                .phoneNumber(phoneNumber)
                .build();

        when(repository.findByNameSurnameAndPhoneNumber(nameSurname, phoneNumber))
                .thenReturn(Optional.of(lead));

        assertThatThrownBy(() -> service.checkByNameAndPhoneNumber(nameSurname, phoneNumber))
                .isInstanceOf(LeadException.class)
                .extracting("code")
                .isEqualTo(LeadResponseCode.LEAD_ALREADY_EXIST);

        verify(repository, times(1))
                .findByNameSurnameAndPhoneNumber(nameSurname, phoneNumber);
    }

    @Test
    public void givenNonExistingNameAndPhone_whenCheckByNameAndPhoneNumber_thenDoesNotThrowException() {
        String name = "Lead1";
        String phoneNumber = "1234567890";
        when(repository.findByNameSurnameAndPhoneNumber(name, phoneNumber)).thenReturn(Optional.empty());

        service.checkByNameAndPhoneNumber(name, phoneNumber);

        verify(repository, times(1)).findByNameSurnameAndPhoneNumber(name, phoneNumber);
    }

}
