package com.ipms.firm.service;

import com.ipms.firm.exception.FirmValidationException;
import com.ipms.firm.exception.GroupCompanyNotFound;
import com.ipms.firm.model.GroupCompany;
import com.ipms.firm.repository.GroupCompanyRepository;
import com.ipms.firm.service.impl.GroupCompanyServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.HashSet;
import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.*;

@RunWith(SpringRunner.class)
public class GroupCompanyServiceTest {

    @MockBean
    private GroupCompanyService service;

    @Mock
    private GroupCompanyRepository repository;

    @Before
    public void setUp() {
        service = new GroupCompanyServiceImpl(repository);
    }

    @Test
    public void givenId_whenGetById_thenReturnGroupCompany() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(new GroupCompany()));
        assertThat(service.getById(Mockito.anyLong()))
                .isNotNull();
    }

    @Test
    public void givenId_whenGetById_throwGroupCompanyNotFoundException() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.empty());
        Throwable thrown = catchThrowable(() -> service.getById(Mockito.anyLong()));
        assertThat(thrown).isInstanceOf(GroupCompanyNotFound.class);
    }

    @Test
    public void whenGetAll_thenReturnGroupCompanyList() {
        Set<GroupCompany> groupCompanySet = new HashSet<>();
        var groupCompany = GroupCompany
                .builder().name("testName")
                .id(1L)
                .build();
        groupCompanySet.add(groupCompany);
        Mockito.when(repository.findAll())
                .thenReturn(groupCompanySet);
        assertThat(service.getAll())
                .isNotNull();
    }

    @Test
    public void givenNameAndId_whenCheckByName_thenDoesNotThrowAnyException() {
        Mockito.when(repository.findByNameIgnoreCase(Mockito.anyString()))
                .thenReturn(Optional.empty());
        assertThatCode(() -> service.checkByName("test", 1L))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenNameAndId_whenCheckByNameAndIdsAreEquals_thenDoesNotThrowAnyException() {
        var groupCompany = GroupCompany.builder()
                .id(1L)
                .name("tst")
                .build();
        Mockito.when(repository.findByNameIgnoreCase(Mockito.anyString()))
                .thenReturn(Optional.of(groupCompany));
        assertThatCode(() -> service.checkByName("test", 1L))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenNameAndId_whenCheckByNameAndIdsAreNotEquals_throwFirmValidationException() {
        var groupCompany = GroupCompany.builder()
                .id(2L)
                .name("tst")
                .build();
        Mockito.when(repository.findByNameIgnoreCase(Mockito.anyString()))
                .thenReturn(Optional.of(groupCompany));
        Throwable thrown = catchThrowable(() -> service.checkByName("tst", 1L));
        assertThat(thrown).isInstanceOf(FirmValidationException.class);
    }

    @Test
    public void whenGetByName_thenReturnGroupCompanyList() {
        var groupCompany = GroupCompany
                .builder().name("testName")
                .id(1L)
                .build();
        Mockito.when(repository.findByNameContainsOrderByName(Mockito.anyString()))
                .thenReturn(List.of(groupCompany));
        assertThat(service.getByName("test").get(0).getName())
                .isEqualTo("testName");
    }

}