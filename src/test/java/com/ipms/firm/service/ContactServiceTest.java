package com.ipms.firm.service;

import com.ipms.firm.mapper.ContactMapper;
import com.ipms.firm.model.Contact;
import com.ipms.firm.model.Firm;
import com.ipms.firm.repository.ContactRepository;
import com.ipms.firm.service.impl.ContactServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.List;
import java.util.Optional;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;

@RunWith(SpringRunner.class)
public class ContactServiceTest {

    @MockBean
    private ContactService service;

    @Mock
    private ContactRepository repository;

    @Mock
    private FirmService firmService;

    @Mock
    private ContactMapper mapper;

    @Before
    public void setUp() {
        service = new ContactServiceImpl(repository, firmService, mapper);
    }

    @Test
    public void whenGetOptionalByMailAddress_thenReturnEmptyOptional(){
        Mockito.when(repository.findFirstByMailAddressIgnoreCase(Mockito.anyString()))
                .thenReturn(Optional.empty());
        assertThat(service.getOptionalByMailAddress("test")).isNotPresent();
    }

    @Test
    public void givenMailAddress_whenGetOptionalByMailAddress_thenReturnOptional(){
        Mockito.when(repository.findFirstByMailAddressIgnoreCase(Mockito.anyString()))
                .thenReturn(Optional.of(Contact.builder()
                        .id(1L)
                        .build()));
        assertThat(service.getOptionalByMailAddress("test")).isPresent();
    }

    @Test
    public void givenId_whenDelete_thenDoesNotThrowAnyException() {
        var contact = Contact
                .builder()
                .id(1L)
                .firms(Set.of())
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(contact));
        Mockito.doNothing().when(repository)
                .delete(Mockito.any(Contact.class));
        assertThatCode(() -> service.delete(1L))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenIdAndFirmId_whenContactFirmsEmptyDelete_thenDoesNotThrowAnyException() {
        var contact = Contact
                .builder()
                .id(1L)
                .firms(Set.of())
                .build();
        var contact2 = Contact
                .builder()
                .id(2L)
                .firms(Set.of())
                .build();
        var firm = Firm.builder().id(1L).contacts(Set.of(contact, contact2)).build();
        Mockito.when(firmService.getFirmById(Mockito.anyLong()))
                .thenReturn(firm);
        Mockito.when(firmService.save(Mockito.any(Firm.class)))
                .thenReturn(firm);
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(contact));
        Mockito.when(repository.save(Mockito.any(Contact.class)))
                .thenReturn(contact);
        assertThatCode(() -> service.delete(1L,1L))
                .doesNotThrowAnyException();
    }
    @Test
    public void givenIdAndFirmId_whenContactFirmsNotEmptyDelete_thenDoesNotThrowAnyException() {
        var contact = Contact
                .builder()
                .id(1L)
                .firms(Set.of())
                .build();
        var contact2 = Contact
                .builder()
                .id(2L)
                .firms(Set.of())
                .build();
        var firm = Firm.builder().id(1L).contacts(Set.of(contact, contact2)).build();
        contact.setFirms(Set.of(firm));
        Mockito.when(firmService.getFirmById(Mockito.anyLong()))
                .thenReturn(firm);
        Mockito.when(firmService.save(Mockito.any(Firm.class)))
                .thenReturn(firm);
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(contact));
        Mockito.when(repository.save(Mockito.any(Contact.class)))
                .thenReturn(contact);
        assertThatCode(() -> service.delete(1L,1L))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenFirmIdPageSize_whenGetContactListByFirm_thenReturnContactPageDto() {
        var contact = Contact
                .builder()
                .id(1L)
                .firms(Set.of())
                .build();
        Page<Contact> pageContact = new PageImpl<>(List.of(contact), Pageable.ofSize(1), 1);
        var firm = Firm.builder().id(1L).contacts(Set.of(contact)).build();
        Mockito.when(firmService.getFirmById(Mockito.anyLong()))
                .thenReturn(firm);
        Mockito.when(repository.findByFirmsInOrderByCreatedAtDesc(Mockito.anySet(), Mockito.any(Pageable.class)))
                .thenReturn(pageContact);
        assertThat(service.getContactListByFirm(1L, 0, 15))
                .isNotNull();
    }

    @Test
    public void givenIds_whenGetContactListByIds_thenReturnContactSet() {
        var contact = Contact
                .builder()
                .id(1L)
                .firms(Set.of())
                .build();
        Mockito.when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(Set.of(contact));
        assertThat(service.getContactListByIds(List.of(1L,2L)))
                .isNotNull();
    }

    @Test
    public void givenMailAddress_whenGetContactListByMailAddress_thenReturnContactList() {
        var contact = Contact
                .builder()
                .id(1L)
                .firms(Set.of())
                .build();
        Mockito.when(repository.findTop15ByMailAddressContainsOrderByMailAddress(Mockito.anyString()))
                .thenReturn(List.of(contact));
        assertThat(service.getContactListByMailAddress("<EMAIL>").get(0).getId())
                .isEqualTo(1L);
    }
}