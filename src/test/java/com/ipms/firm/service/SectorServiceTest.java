package com.ipms.firm.service;

import com.ipms.firm.model.Sector;
import com.ipms.firm.repository.SectorRepository;
import com.ipms.firm.service.impl.SectorServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.HashSet;
import java.util.List;
import java.util.Set;

import static org.assertj.core.api.AssertionsForClassTypes.assertThat;

@RunWith(SpringRunner.class)
public class SectorServiceTest {

    @MockBean
    private SectorService service;

    @Mock
    private SectorRepository repository;

    @Before
    public void setUp() {
        service = new SectorServiceImpl(repository);
    }

    @Test
    public void givenIdList_whenGetByIdList_thenReturnSectorSet() {
        List<Long> idList = new ArrayList<>(List.of(1L, 2L));
        Set<Sector> sectorSet = new HashSet<>();
        var sector = Sector
                .builder()
                .name("testName")
                .nameTr("testTrName")
                .build();
        sectorSet.add(sector);
        Mockito.when(repository.findByIdIn(idList))
                .thenReturn(sectorSet);
        assertThat(service.getByIdList(idList))
                .isNotNull();
    }

    @Test
    public void whenGetAll_thenReturnSectorList() {
        Set<Sector> sectorSet = new HashSet<>();
        var sector = Sector
                .builder().name("testName")
                .nameTr("testTrName")
                .build();
        sectorSet.add(sector);
        Mockito.when(repository.findAll())
                .thenReturn(sectorSet);
        assertThat(service.getAll())
                .isNotNull();
    }
}