package com.ipms.firm.service;

import com.ipms.firm.client.ActivityClient;
import com.ipms.firm.client.MatterClient;
import com.ipms.firm.dto.*;
import com.ipms.firm.enums.FirmRole;
import com.ipms.firm.exception.FirmNotFoundException;
import com.ipms.firm.exception.FirmValidationException;
import com.ipms.firm.model.*;
import com.ipms.firm.repository.FirmRepository;
import com.ipms.firm.service.impl.FirmServiceImpl;
import com.ipms.firm.specification.FirmSpecification;
import org.assertj.core.api.Assertions;
import org.junit.Assert;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.PageRequest;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.*;

import static org.assertj.core.api.AssertionsForClassTypes.*;
import static org.mockito.Mockito.verify;


@RunWith(SpringRunner.class)
public class FirmServiceTest {

    @MockBean
    private FirmService service;

    @Mock
    private FirmRepository repository;

    @Mock
    private GroupCompanyService groupCompanyService;

    @Mock
    private ActivityClient activityClient;

    @Mock
    private AgentInfoService agentInfoService;

    @Mock
    private RightOwnerService rightOwnerService;

    @Mock
    private MatterClient matterClient;

    @Before
    public void setUp() {
        service = new FirmServiceImpl(repository, groupCompanyService, activityClient, agentInfoService, rightOwnerService, matterClient);
    }


    @Test
    public void givenFirmDto_whenCheckByTitleAndCountryAndCity_thenDoesNotThrowAnyException() {
        var firmDto = FirmDto
                .builder()
                .title("Test")
                .city("34")
                .country("TR")
                .id(1L)
                .build();
        Mockito.when(repository.findByTitleIgnoreCaseAndCountryAndCity("Test", "TR", "34"))
                .thenReturn(Optional.empty());
        assertThatCode(() -> service.checkByTitleAndCountryAndCity(firmDto))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenFirmDto_whenCheckByTitleAndCountryAndCityAndIdsAreEquals_thenDoesNotThrowAnyException() {
        var firmDto = FirmDto
                .builder()
                .title("Test")
                .city("34")
                .country("TR")
                .id(1L)
                .build();
        var firm = Firm.
                builder()
                .id(1L)
                .build();
        Mockito.when(repository.findByTitleIgnoreCaseAndCountryAndCity("Test", "TR", "34"))
                .thenReturn(Optional.of(firm));
        assertThatCode(() -> service.checkByTitleAndCountryAndCity(firmDto))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenFirmDto_whenCheckByTitleAndCountryAndCity_thenThrowFirmValidationException() {
        var firmDto = FirmDto
                .builder()
                .title("Test")
                .city("34")
                .country("TR")
                .id(1L)
                .build();
        var firm = Firm.
                builder()
                .id(2L)
                .build();
        Mockito.when(repository.findByTitleIgnoreCaseAndCountryAndCity("Test", "TR", "34"))
                .thenReturn(Optional.of(firm));
        Throwable thrown = catchThrowable(() -> service.checkByTitleAndCountryAndCity(firmDto));
        assertThat(thrown).isInstanceOf(FirmValidationException.class);
    }

    @Test
    public void givenTitle_whenGetFirmListByTitle_thenReturnFirmList() {
        var firm = Firm
                .builder()
                .id(1L)
                .country("TR")
                .city("06")
                .build();
        List<Firm> firmList = new ArrayList<>(List.of(firm));
        List<String> iso2List = new ArrayList<>(List.of("06"));
        var city = City.builder()
                .id(1L)
                .countryCode("TR")
                .iso2("06")
                .name("Ankara")
                .build();
        var cityMap = new HashMap<String, City>();
        cityMap.put("TR06", city);
        Mockito.when(repository.findTop30ByTitleContainsIgnoreCaseOrderByTitle(Mockito.anyString()))
                .thenReturn(firmList);
        assertThat(service.getFirmListByTitle(Mockito.anyString()).get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenId_whenGetById_throwFirmNotFoundException() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.empty());
        Throwable thrown = catchThrowable(() -> service.getFirmById(Mockito.anyLong()));
        assertThat(thrown).isInstanceOf(FirmNotFoundException.class);
    }

    @Test
    public void givenId_whenGetById_thenReturnFirm() {
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(new Firm()));
        assertThat(service.getFirmById(Mockito.anyLong()))
                .isNotNull();
    }

    @Test
    public void givenFirm_whenSave_thenReturnFirm() {
        var firm = Firm
                .builder()
                .id(1L)
                .build();
        Mockito.when(repository.save(Mockito.any(Firm.class)))
                .thenReturn(firm);
        assertThat(service.save(firm))
                .isNotNull();
    }

    @Test
    public void givenGroupCompanyId_whenGetFirmListByGroupCompany_thenReturnFirmList(){
        var firm = Firm
                .builder()
                .id(1L)
                .build();
        Mockito.when(repository.findByGroupCompanyIdOrderByTitle(Mockito.anyLong()))
                .thenReturn(List.of(firm));
        assertThat(service.getFirmListByGroupCompany(1L).get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenTitleAndRole_whenGetFirmListByTitleAndRole_thenReturnFirmList(){
        var firm = Firm
                .builder()
                .id(1L)
                .build();
        Mockito.when(repository.findTop30ByTitleContainsIgnoreCaseAndRoleInOrderByTitle(Mockito.anyString(), Mockito.anyList()))
                .thenReturn(List.of(firm));
        assertThat(service.getFirmListByTitleAndRole("testTitle", List.of(FirmRole.AGENT)).get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenGetByIdIn_thenReturnFirmList() {
        var firm = Firm.builder()
                .id(1L)
                .title("testTitle")
                .city("34")
                .country("TR")
                .province("beykoz")
                .address("testAddress")
                .build();
        Map<String, City> cityMap = new HashMap();
        cityMap.put("TR34", City.builder()
                .name("Istanbul")
                .build());

        Mockito.when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(firm));
        Assertions.assertThat(service.getByIdIn(List.of(1L,2L)).get(0).getCity())
                .isEqualTo("34");
    }

    @Test
    public void whenUpdateGroupCompany_thenReturnFirm() {
        var groupCompany = GroupCompany
                .builder()
                .id(1L)
                .name("test-group-company")
                .build();
        var firm = Firm.builder()
                .version(0L)
                .id(1L)
                .groupCompany(groupCompany)
                .build();
        Mockito.when(groupCompanyService.getById(groupCompany.getId()))
                .thenReturn(groupCompany);
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));
        Mockito.when(repository.save(Mockito.any(Firm.class)))
                .thenReturn(firm);
        assertThat(service.updateGroupCompany(1L, groupCompany, 1L).getGroupCompany().getName())
                .isEqualTo("test-group-company");
    }

    @Test
    public void whenUpdateGroupCompanyNameAndIdIsNull_thenReturnFirm() {
        var groupCompany = GroupCompany
                .builder()
                .name(null)
                .build();
        var firm = Firm.builder()
                .version(0L)
                .id(1L)
                .groupCompany(groupCompany)
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));
        Mockito.when(repository.save(Mockito.any(Firm.class)))
                .thenReturn(firm);
        assertThat(service.updateGroupCompany(1L, groupCompany, 1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenUpdateGroupCompanyNameIsNotNullAndIdIsNull_thenReturnFirm() {
        var groupCompany = GroupCompany
                .builder()
                .name("test-group-company")
                .build();
        var firm = Firm.builder()
                .version(0L)
                .id(1L)
                .groupCompany(groupCompany)
                .build();
        Mockito.doNothing().when(groupCompanyService).checkByName(groupCompany.getName(), groupCompany.getId());
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));
        Mockito.when(repository.save(Mockito.any(Firm.class)))
                .thenReturn(firm);
        assertThat(service.updateGroupCompany(1L, groupCompany, 1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenCheckTitleForMigrationForUS_thenThrowFirmValidationException() {
        var firmDto = FirmDto.builder()
                .country("US")
                .title("test")
                .id(2L)
                .build();
        var firm = Firm.builder()
                .country("US")
                .title("test")
                .id(1L)
                .build();
        Mockito.when(repository.findByTitle(Mockito.anyString()))
                .thenReturn(List.of(firm));
        Throwable thrown = catchThrowable(() -> service.checkTitleForMigration(firmDto));
        assertThat(thrown).isInstanceOf(FirmValidationException.class);
    }

    @Test
    public void whenCheckTitleForMigrationExceptUS_thenThrowFirmValidationException() {
        var firmDto = FirmDto.builder()
                .country("TR")
                .title("test")
                .id(2L)
                .build();
        var firm = Firm.builder()
                .country("TR")
                .title("test")
                .id(1L)
                .build();
        Mockito.when(repository.findByTitle(Mockito.anyString()))
                .thenReturn(List.of(firm));
        Throwable thrown = catchThrowable(() -> service.checkTitleForMigration(firmDto));
        assertThat(thrown).isInstanceOf(FirmValidationException.class);
    }

    @Test
    public void whenDelete_thenDoesNotThrowAnyException(){
        var firm = Firm.builder()
                .id(1L)
                .city("test-city")
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));
        assertThatCode(() -> service.delete(1L, 1L)).doesNotThrowAnyException();
    }

    @Test
    public void whenDeleteAgent_thenDoesNotThrowAnyException(){
        var activityResponse = ActivityResponse.builder().payload(List.of()).code(1).build();
        var rightOwner = RightOwnerInfo.builder().id(1L).build();
        var agentInfo = AgentInfo.builder().id(1L).build();
        var firm = Firm.builder()
                .id(1L)
                .city("test-city")
                .rightOwnerInfo(rightOwner)
                .build();
        Mockito.when(repository.findByAgentInfo_Id(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));
        Mockito.when(activityClient.getByFirmId(Mockito.anyLong()))
                .thenReturn(activityResponse);
        Mockito.when(agentInfoService.getById(Mockito.anyLong()))
                .thenReturn(agentInfo);
        Mockito.when(repository.save(Mockito.any(Firm.class)))
                .thenReturn(firm);
        Mockito.when(agentInfoService.save(Mockito.any(AgentInfo.class)))
                .thenReturn(agentInfo);
        assertThatCode(() -> service.deleteAgent(1L, 1L))
                .doesNotThrowAnyException();
    }

    @Test
    public void whenDeleteAgentAndNoRoleControl_thenThrowFirmValidationException(){
        var firm = Firm.builder()
                .id(1L)
                .city("test-city")
                .build();
        Mockito.when(repository.findByAgentInfo_Id(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));
        Throwable thrown = catchThrowable(() -> service.deleteAgent(1L, 1L));
        assertThat(thrown).isInstanceOf(FirmValidationException.class);
    }

    @Test
    public void whenDeleteAgentAndFirmIncludedActivityControl_thenThrowFirmValidationException(){
        var activityResponse = ActivityResponse.builder().payload(List.of(ActivityResponse.Payload.builder()
                .build())).code(1).build();
        var rightOwner = RightOwnerInfo.builder().id(1L).build();
        var agentInfo = AgentInfo.builder().id(1L).build();
        var firm = Firm.builder()
                .id(1L)
                .city("test-city")
                .rightOwnerInfo(rightOwner)
                .build();
        Mockito.when(repository.findByAgentInfo_Id(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));
        Mockito.when(activityClient.getByFirmId(Mockito.anyLong()))
                .thenReturn(activityResponse);
        Throwable thrown = catchThrowable(() -> service.deleteAgent(1L, 1L));
        assertThat(thrown).isInstanceOf(FirmValidationException.class);
    }

    @Test
    public void whenDeleteRightOwner_thenDoesNotThrowAnyException(){
        var matterResponse = MatterResponse.builder().payload(List.of()).code(1).build();
        var rightOwner = RightOwnerInfo.builder().id(1L).build();
        var agentInfo = AgentInfo.builder().id(1L).build();
        var firm = Firm.builder()
                .id(1L)
                .city("test-city")
                .agentInfo(agentInfo)
                .build();
        Mockito.when(repository.findByRightOwnerInfo_Id(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));
        Mockito.when(matterClient.getByFirmId(Mockito.anyLong()))
                .thenReturn(matterResponse);
        Mockito.when(rightOwnerService.getById(Mockito.anyLong()))
                .thenReturn(rightOwner);
        Mockito.when(repository.save(Mockito.any(Firm.class)))
                .thenReturn(firm);
        Mockito.when(rightOwnerService.save(Mockito.any(RightOwnerInfo.class)))
                .thenReturn(rightOwner);
        assertThatCode(() -> service.deleteRightOwner(1L, 1L))
                .doesNotThrowAnyException();
    }

    @Test
    public void whenDeleteRightOwnerAndNoRoleControl_thenThrowFirmValidationException(){
        var firm = Firm.builder()
                .id(1L)
                .city("test-city")
                .build();
        Mockito.when(repository.findByRightOwnerInfo_Id(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));
        Throwable thrown = catchThrowable(() -> service.deleteRightOwner(1L, 1L));
        assertThat(thrown).isInstanceOf(FirmValidationException.class);
    }

    @Test
    public void whenDeleteRightOwnerAndFirmIncludedMatterControl_thenThrowFirmValidationException(){
        var matterResponse = MatterResponse.builder().payload(List.of(MatterResponse.Payload.builder()
                .build())).code(1).build();
        var agentInfo = AgentInfo.builder().id(1L).build();
        var firm = Firm.builder()
                .id(1L)
                .city("test-city")
                .agentInfo(agentInfo)
                .build();
        Mockito.when(repository.findByRightOwnerInfo_Id(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));
        Mockito.when(matterClient.getByFirmId(Mockito.anyLong()))
                .thenReturn(matterResponse);
        Throwable thrown = catchThrowable(() -> service.deleteRightOwner(1L, 1L));
        assertThat(thrown).isInstanceOf(FirmValidationException.class);
    }

    @Test
    public void givenCityAndCountry_whenGetByCityAndCountry_thenReturnFirmList(){
        var firm = Firm
                .builder()
                .id(1L)
                .build();
        Mockito.when(repository.findByCityAndCountry(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(firm));
        assertThat(service.getByCityAndCountry("test-city","test-country").get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenProcessAssociatedEvent_thenDoesNotThrowAnyException() {
        var firmIds = List.of(1L, 2L);
        var firmEvent = FirmEvent.builder()
                .ids(firmIds)
                .build();
        var firm1 = Firm.builder()
                .id(1L)
                .prospect(true)
                .build();
        var firm2 = Firm.builder()
                .id(2L)
                .prospect(true)
                .build();

        Mockito.when(repository.findByIdIn(firmIds)).thenReturn(List.of(firm1, firm2));
        ArgumentCaptor<List<Firm>> firmCaptor = ArgumentCaptor.forClass(List.class);

        service.processAssociatedEvent(firmEvent);

        verify(repository).saveAll(firmCaptor.capture());
        firmCaptor.getValue().forEach(firm -> Assert.assertFalse(firm.getProspect()));
    }

    @Test
    public void whenGetFirmPage_thenReturnFirmPage() {
        Page<Firm> page = new PageImpl<>(List.of(new Firm(), new Firm()));
        Mockito.when(repository.findAll(Mockito.any(FirmSpecification.class), Mockito.any(PageRequest.class)))
                .thenReturn(page);
        var firmPage = service.getFirmPage(new FirmFilterRequest(), 1, 1);
        assertThat(firmPage.getTotalElements()).isEqualTo(2);
    }

    @Test
    public void givenIdAndBillingAccountId_whenLinkBillingAccount_thenReturnsFirm() {
        var firm = Firm.builder()
                .id(1L)
                .billingAccountIds(new ArrayList<>(List.of(1L, 2L)))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(firm));
        Mockito.when(repository.save(Mockito.any(Firm.class)))
                .thenReturn(firm);
        assertThat(service.linkBillingAccount(1L, 3L).getBillingAccountIds())
                .isEqualTo(List.of(1L, 2L, 3L));
    }
}