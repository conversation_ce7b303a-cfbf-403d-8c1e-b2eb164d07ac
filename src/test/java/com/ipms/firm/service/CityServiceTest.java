package com.ipms.firm.service;

import com.ipms.firm.exception.CityInUseException;
import com.ipms.firm.model.City;
import com.ipms.firm.model.Firm;
import com.ipms.firm.repository.CityRepository;
import com.ipms.firm.service.impl.CityServiceImpl;
import org.assertj.core.api.Assertions;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.AssertionsForClassTypes.*;

@RunWith(SpringRunner.class)
public class CityServiceTest {

    @MockBean
    private CityService service;

    @Mock
    private CityRepository repository;

    @Mock
    private FirmService firmService;

    @Before
    public void setUp() {
        service = new CityServiceImpl(repository, firmService);
    }

    @Test
    public void givenIso2List_whenGetByIso2List_thenReturnCityList() {
        var iso2List = new ArrayList<>(List.of("34"));
        var city = City.builder()
                .id(1L)
                .build();
        var cityList = new ArrayList<>(List.of(city));
        Mockito.when(repository.findByIso2In(iso2List))
                .thenReturn(cityList);
        assertThat(service.getByIso2List(iso2List))
                .isNotNull();
    }

    @Test
    public void givenIso2List_whenGetByIso2Map_thenReturnCityMap() {
        var iso2List = new ArrayList<>(List.of("34"));
        assertThat(service.getByIso2Map(iso2List))
                .isNotNull();
    }

    @Test
    public void givenCity_whenSave_thenReturnCity() {
        City city = City
                .builder()
                .countryCode("US")
                .name("Bethpage")
                .id(112293L)
                .stateCode("NY")
                .build();
        Mockito.when(service.save(Mockito.any(City.class)))
                .thenReturn(city);
        Assertions.assertThat(service.save(city).getName())
                .isEqualTo("Bethpage");
    }

    @Test
    public void givenIdAndVersion_whenDelete_thenDoesNotThrowAnyException() {
        City city = City
                .builder()
                .countryCode("US")
                .name("Bethpage")
                .id(112293L)
                .stateCode("NY")
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(city));
        Mockito.when(firmService.getByCityAndCountry(Mockito.anyString(), Mockito.anyString()))
                        .thenReturn(Collections.emptyList());
        assertThatCode(() -> service.delete(1L))
                .doesNotThrowAnyException();
    }

    @Test
    public void givenIdAndVersion_whenDelete_thenThrowCityInUseException() {
        City city = City
                .builder()
                .countryCode("US")
                .name("Bethpage")
                .iso2("test-iso2")
                .countryCode("test-country-code")
                .id(1L)
                .stateCode("NY")
                .build();
        var firm = Firm
                .builder()
                .id(1L)
                .country("TR")
                .title("test-title")
                .city("06")
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(city));
        Mockito.when(firmService.getByCityAndCountry(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(firm));
        Throwable thrown = catchThrowable(() -> service.delete(1L));
        assertThat(thrown).isInstanceOf(CityInUseException.class);
    }

    @Test
    public void givenIso2AndCountryCode_whenGetByIso2AndCountryCode_thenReturnCityList() {
        City city = City
                .builder()
                .countryCode("US")
                .name("Bethpage")
                .id(112293L)
                .stateCode("NY")
                .build();
        Mockito.when(repository.findByIso2AndCountryCode(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(city));
        Assertions.assertThat(service.getByIso2AndCountryCode("test-iso2", "test-code").get(0).getName())
                .isEqualTo("Bethpage");
    }
}