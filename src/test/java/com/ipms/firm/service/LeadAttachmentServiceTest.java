package com.ipms.firm.service;

import com.ipms.firm.model.LeadAttachment;
import com.ipms.firm.repository.LeadAttachmentRepository;
import com.ipms.firm.service.impl.LeadAttachmentServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;

import static org.junit.Assert.assertEquals;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class LeadAttachmentServiceTest {
    @MockBean
    private LeadAttachmentService service;

    @Mock
    LeadAttachmentRepository repository;

    @Before
    public void setUp() {
        service = new LeadAttachmentServiceImpl(repository);
    }

    @Test
    public void givenLeadAttachmentToSave_whenSave_thenReturnSavedLeadAttachment() {
        LeadAttachment attachment = new LeadAttachment();
        LeadAttachment savedAttachment = new LeadAttachment();
        savedAttachment.setId(1L);

        when(repository.save(attachment)).thenReturn(savedAttachment);
        LeadAttachment result = service.save(attachment);

        assertEquals(savedAttachment, result);
        verify(repository, times(1)).save(attachment);
    }
}
