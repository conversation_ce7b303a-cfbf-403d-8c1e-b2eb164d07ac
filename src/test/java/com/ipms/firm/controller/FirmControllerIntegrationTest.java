package com.ipms.firm.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ipms.firm.dto.*;
import com.ipms.firm.handler.FirmHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-firm/application.yml")
public class FirmControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private FirmHandler handler;

    ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void givenFirmDto_whenSave_thenReturnBaseResponse() throws Exception {
        var rightOwnerInfoDto = RightOwnerInfoDto
                .builder()
                .sectorIdList(List.of(1L))
                .taxOffice("Test office")
                .build();
        var firmDto = FirmDto
                .builder()
                .address("test_address")
                .city("114990")
                .country("US")
                .state("TX")
                .postalCode("23422")
                .role("RIGHT_OWNER")
                .title("test firma")
                .type("LEGAL_ENTITY")
                .rightOwnerInfo(rightOwnerInfoDto)
                .build();
        Mockito.when(handler.save(Mockito.any(FirmDto.class))).thenReturn(firmDto);
        mvc.perform(post("/")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(firmDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenTitle_whenGetFirmListByTitle_thenReturnBaseResponse() throws Exception {
        List<FirmSummaryDto> firmSummaryDtoList = new ArrayList<>();
        var firmSummaryDto = FirmSummaryDto
                .builder()
                .id(1L)
                .title("test firm")
                .build();
        firmSummaryDtoList.add(firmSummaryDto);
        Mockito.when(handler.listFirmSummaryDtoList(Mockito.anyString())).thenReturn(firmSummaryDtoList);
        mvc.perform(get("/summary?title=testFirmName")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenFirmDtoAndId_whenUpdate_thenReturnBaseResponse() throws Exception {
        var rightOwnerInfoDto = RightOwnerInfoDto
                .builder()
                .sectorIdList(List.of(1L))
                .taxOffice("Test office")
                .build();
        var firmDto = FirmDto
                .builder()
                .id(1L)
                .address("test_address")
                .city("114990")
                .country("US")
                .state("TX")
                .postalCode("23422")
                .role("RIGHT_OWNER")
                .title("test firma")
                .type("LEGAL_ENTITY")
                .rightOwnerInfo(rightOwnerInfoDto)
                .version(1L)
                .build();
        Mockito.when(handler.update(Mockito.any(FirmDto.class), Mockito.anyLong())).thenReturn(firmDto);
        mvc.perform(put("/5")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(firmDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenId_whenGetFirmById_thenReturnBaseResponse() throws Exception {
        var rightOwnerInfoDto = RightOwnerInfoDto
                .builder()
                .sectorIdList(List.of(1L))
                .taxOffice("Test office")
                .build();
        var firmDto = FirmDto
                .builder()
                .id(1L)
                .address("test_address")
                .city("114990")
                .country("US")
                .state("TX")
                .postalCode("23422")
                .role("RIGHT_OWNER")
                .title("test firma")
                .type("LEGAL_ENTITY")
                .rightOwnerInfo(rightOwnerInfoDto)
                .version(1L)
                .build();
        Mockito.when(handler.getById(Mockito.anyLong())).thenReturn(firmDto);
        mvc.perform(get("/3")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

    }

    @Test
    public void givenFirmDto_whenRequestAssignment_thenReturnBaseResponse() throws Exception {
        var contactDto = ContactDto
                .builder()
                .mailAddress("<EMAIL>")
                .mailName("testName")
                .build();
        var firmDto = FirmDto
                .builder()
                .id(1L)
                .title("testTitle")
                .country("testCountry")
                .city("testCity")
                .type("ALL")
                .contact(contactDto)
                .build();
        Mockito.when(handler.saveWithContact(Mockito.any(FirmDto.class)))
                .thenReturn(firmDto);
        mvc.perform(post("/request-assignment")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(firmDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenGroupCompanyId_whenGetFirmListByGroupCompany_thenReturnBaseResponse() throws Exception {
        var rightOwnerInfoDto = RightOwnerInfoDto
                .builder()
                .sectorIdList(List.of(1L))
                .taxOffice("Test office")
                .build();
        var firmDto = FirmDto
                .builder()
                .id(1L)
                .address("test_address")
                .city("114990")
                .country("US")
                .state("TX")
                .postalCode("23422")
                .role("RIGHT_OWNER")
                .title("test firma")
                .type("LEGAL_ENTITY")
                .rightOwnerInfo(rightOwnerInfoDto)
                .version(1L)
                .build();
        Mockito.when(handler.getFirmListByGroupCompany(Mockito.anyLong())).thenReturn(List.of(firmDto));
        mvc.perform(get("/group-companies/3")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

    }

    @Test
    public void givenTitleAndRole_whenGetFirmListByTitleAndRole_thenReturnBaseResponse() throws Exception {
        var firmSummaryDto = FirmSummaryDto
                .builder()
                .id(1L)
                .title("test firm")
                .build();
        Mockito.when(handler.getFirmListByTitleAndRole(Mockito.anyString(), Mockito.anyString()))
                .thenReturn(List.of(firmSummaryDto));
        mvc.perform(get("/summary/test/AGENT")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

    }

    @Test
    public void whenGetList_thenReturnBaseResponse() throws Exception {
        var firmDto = FirmDto
                .builder()
                .id(1L)
                .title("test firm")
                .build();

        Mockito.when(handler.getByIdIn(Mockito.anyList()))
                .thenReturn(List.of(firmDto));

        mvc.perform(get("/ids/1231")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUpdateGroupCompanyFromFirm_thenReturnBaseResponse() throws Exception {
        var firmDto = FirmDto.builder().build();
        Mockito.when(handler.updateGroupCompany(Mockito.anyLong(), Mockito.any(GroupCompanyDto.class), Mockito.anyLong()))
                .thenReturn(firmDto);
        mvc.perform(put("/1000/0")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(firmDto))
                        .accept(MediaType.APPLICATION_JSON))
                        .andExpect(status().isOk());
    }

    @Test
    public void whenDelete_thenDoesNotThrowAnything() throws Exception {
        Mockito.doNothing().when(handler).delete(Mockito.anyLong(), Mockito.anyLong());
        mvc.perform(delete("/1000/0")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                        .andExpect(status().isOk());
    }

    @Test
    public void whenDeleteAgent_thenDoesNotThrowAnything() throws Exception {
        Mockito.doNothing().when(handler).deleteAgent(Mockito.anyLong(), Mockito.anyLong());
        mvc.perform(delete("/agent/1000/0")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenDeleteRightOwner_thenDoesNotThrowAnything() throws Exception {
        Mockito.doNothing().when(handler).deleteRightOwner(Mockito.anyLong(), Mockito.anyLong());
        mvc.perform(delete("/right-owner/1000/0")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenFirmFilterRequest_whenGetFirmSummaryPage_thenReturnBaseResponse() throws Exception {
        var firmFilterRequest = FirmFilterRequest
                .builder()
                .billingAccountId(1L)
                .build();
        PageData<FirmSummaryDto> firmSummaryDtoPage = new PageData<>(List.of(FirmSummaryDto.builder().build()), 1, 1);
        Mockito.when(handler.getFirmSummaryPage(Mockito.any(FirmFilterRequest.class), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(firmSummaryDtoPage);

        mvc.perform(get("/firms/summary/0/10")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(firmFilterRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenIdAndBillingAccountId_whenLinkBillingAccount_thenReturnsBaseResponse() throws Exception {
        var firmDto = FirmDto
                .builder()
                .id(1L)
                .version(1L)
                .billingAccountIds(List.of(1L, 2L))
                .build();
        Mockito.when(handler.linkBillingAccount(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(firmDto);
        mvc.perform(post("/1/billing-account/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}