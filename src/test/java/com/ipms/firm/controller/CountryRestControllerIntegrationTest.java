package com.ipms.firm.controller;

import com.ipms.firm.dto.CountryDto;
import com.ipms.firm.handler.CountryHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-firm/application.yml")
class CountryRestControllerIntegrationTest {
    @Autowired
    private MockMvc mvc;

    @MockBean
    private CountryHandler countryHandler;

    @Test
    public void givenCountryList_whenGetAll_thenReturnCountryList() throws Exception {
        List<CountryDto> countryList = new ArrayList<>();
        countryList.add(CountryDto.builder().id(1L).name("Turkey").iso2("TR").iso3("TUR").build());
        countryList.add(CountryDto.builder().id(2L).name("USA").iso2("US").iso3("USA").build());
        countryList.add(CountryDto.builder().id(3L).name("Canada").iso2("CA").iso3("CAN").build());

        Mockito.when(countryHandler.getAll())
                .thenReturn(countryList);

        mvc.perform(get("/country")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenIso2_whenGetByIso2_thenReturnCountry() throws Exception {
        Mockito.when(countryHandler.getByIso2("TR"))
                .thenReturn(CountryDto.builder().id(1L).name("Turkey").iso2("TR").iso3("TUR").build());

        mvc.perform(get("/country/iso2/TR")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenId_whenGetIso2ById_thenReturnCountry() throws Exception {
        Mockito.when(countryHandler.getIso2ById(1L))
                .thenReturn(CountryDto.builder().id(1L).name("Turkey").iso2("TR").iso3("TUR").build());

        mvc.perform(get("/country/1/iso2")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
