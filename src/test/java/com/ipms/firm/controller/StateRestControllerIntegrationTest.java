package com.ipms.firm.controller;

import com.ipms.firm.dto.StateDto;
import com.ipms.firm.handler.StateHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-firm/application.yml")
class StateRestControllerIntegrationTest {
    @Autowired
    private MockMvc mvc;

    @MockBean
    private StateHandler stateHandler;

    @Test
    public void givenCountryCode_whenGetByCountry_thenReturnStateList() throws Exception {
        List<StateDto> stateList = new ArrayList<>();
        stateList.add(StateDto.builder().id(1L).name("Texas").countryCode("US").iso2("TX").build());
        stateList.add(StateDto.builder().id(2L).name("Alaska").countryCode("US").iso2("AK").build());
        stateList.add(StateDto.builder().id(3L).name("Maryland").countryCode("US").iso2("MD").build());

        Mockito.when(stateHandler.getByCountry("US"))
                .thenReturn(stateList);

        mvc.perform(get("/state/US")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenCountryCodeAndIso2_whenGetByCountryAndIso2_thenReturnState() throws Exception {
        Mockito.when(stateHandler.getByCountryAndIso2("US","TX"))
                        .thenReturn(StateDto.builder().id(1L).name("Texas").countryCode("US").iso2("TX").build());

        mvc.perform(get("/state/countryAndIso2/US/TX")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

}
