package com.ipms.firm.controller;

import com.ipms.firm.dto.SectorDto;
import com.ipms.firm.handler.SectorHandler;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-firm/application.yml")
class SectorControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private SectorHandler handler;

    @Test
    void whenSectorList_thenReturnBaseResponse() throws Exception {
        var sectorDto = SectorDto
                .builder()
                .id(1L)
                .name("Test sector")
                .nameTr("test sector tr")
                .build();
        List<SectorDto> sectorDtoList = new ArrayList<>(List.of(sectorDto));
        Mockito.when(handler.getAll()).thenReturn(sectorDtoList);
        mvc.perform(get("/sector")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

}