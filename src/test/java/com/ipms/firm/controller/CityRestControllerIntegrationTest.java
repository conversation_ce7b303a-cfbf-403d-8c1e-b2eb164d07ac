package com.ipms.firm.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.firm.dto.CityDto;
import com.ipms.firm.handler.CityHandler;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-firm/application.yml")
class CityRestControllerIntegrationTest {
    @Autowired
    private MockMvc mvc;

    @MockBean
    private CityHandler cityHandler;

    ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule());

    @Test
    void givenCountryCode_whenGetByCountry_thenReturnCityList() throws Exception {
        ArrayList<CityDto> cityList = new ArrayList<>();
        cityList.add(CityDto.builder().id(1L).name("Istanbul").countryCode("TR").iso2("34").build());
        cityList.add(CityDto.builder().id(2L).name("Ankara").countryCode("TR").iso2("06").build());
        cityList.add(CityDto.builder().id(3L).name("Izmir").countryCode("TR").iso2("35").build());

        Mockito.when(cityHandler.getByCountry("TR"))
                .thenReturn(cityList);

        mvc.perform(get("/city/TR")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void givenCountryCodeAndStateCode_whenGetByCountryAndState_thenReturnCityList() throws Exception {
        ArrayList<CityDto> cityList = new ArrayList<>();
        cityList.add(CityDto.builder().id(1L).countryCode("US").name("Bethpage").stateCode("NY").build());
        cityList.add(CityDto.builder().id(2L).countryCode("US").name("Big Flats").stateCode("NY").build());
        cityList.add(CityDto.builder().id(3L).countryCode("US").name("Billington Heights").stateCode("NY").build());

        Mockito.when(cityHandler.getByCountryAndState("US","NY"))
                .thenReturn(cityList);

        mvc.perform(get("/city/US/NY")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void givenCountryCodeAndIso2_whenGetByCountryAndIso2_thenReturnCity() throws Exception {

        Mockito.when(cityHandler.getByCountryAndIso2("US","112293"))
                .thenReturn(CityDto.builder().id(1L).countryCode("US").name("Bethpage").stateCode("NY").build());

        mvc.perform(get("/city/countryAndIso2/US/112293")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void givenCityDto_whenSave_thenReturnBaseResponse() throws Exception {
        var cityDto = CityDto
                .builder()
                .countryCode("US")
                .name("Bethpage")
                .stateCode("NY")
                .build();
        Mockito.when(cityHandler.save(Mockito.any(CityDto.class)))
                .thenReturn(cityDto);

        mvc.perform(post("/city/")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(cityDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void givenId_whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(cityHandler)
                .delete(Mockito.anyLong());

        mvc.perform(delete("/city/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
