package com.ipms.firm.controller;

import com.ipms.firm.dto.ProvinceDto;
import com.ipms.firm.handler.ProvinceHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-firm/application.yml")
class ProvinceRestControllerIntegrationTest {
    @Autowired
    private MockMvc mvc;

    @MockBean
    private ProvinceHandler provinceHandler;

    @Test
    public void givenCountryCodeAndCityCode_whenGetByCountryAndCity_thenReturnProvinceList() throws Exception {
        List<ProvinceDto> provinceList = new ArrayList<>();
        provinceList.add(ProvinceDto.builder().id(1L).name("Adalar").countryCode("TR").cityCode("34").build());
        provinceList.add(ProvinceDto.builder().id(2L).name("Beykoz").countryCode("TR").cityCode("34").build());
        provinceList.add(ProvinceDto.builder().id(3L).name("Maltepe").countryCode("TR").cityCode("34").build());

        Mockito.when(provinceHandler.getByCountryAndCity("TR","34"))
                .thenReturn(provinceList);

        mvc.perform(get("/province/TR/34")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenCountryCodeAndCityCodeAndProvinceName_whenGetByCountryAndCityAndProvince_thenReturnProvince() throws Exception {
        Mockito.when(provinceHandler.getByCountryAndCityAndProvince("TR","34","Adalar"))
                .thenReturn(ProvinceDto.builder().id(1L).name("Adalar").countryCode("TR").cityCode("34").build());

        mvc.perform(get("/province/TR/34/Adalar")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

}
