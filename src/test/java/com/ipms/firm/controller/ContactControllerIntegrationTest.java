package com.ipms.firm.controller;

import com.ipms.firm.dto.ContactDto;
import com.ipms.firm.dto.ContactPageDto;
import com.ipms.firm.handler.ContactHandler;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-firm/application.yml")
class ContactControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private ContactHandler handler;

    @Test
    void givenId_whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(handler).delete(Mockito.anyLong());
        mvc.perform(delete("/contact/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void givenMailAddress_whenCheckByMailAddress_thenReturnBaseResponse() throws Exception {
        Mockito.when(handler.checkByMailAddress(Mockito.anyString())).thenReturn(Boolean.TRUE);
        mvc.perform(get("/contact/check/test-mail-address")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void givenId_whenDeleteByEmail_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(handler).deleteByEmail(Mockito.anyString());
        mvc.perform(delete("/contact/email/<EMAIL>")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void givenFirmIdPageSize_whenGetByFirm_thenReturnBaseResponse() throws Exception {
        Mockito.when(handler.getContactListByFirm(Mockito.anyLong(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(ContactPageDto.builder().build());
        mvc.perform(get("/contact/1/0/15")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void givenMailAddress_whenGetByMailAddress_thenReturnBaseResponse() throws Exception {
        Mockito.when(handler.getContactListByMailAddress(Mockito.anyString()))
                .thenReturn(List.of(ContactDto.builder().build()));
        mvc.perform(get("/contact/<EMAIL>")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
