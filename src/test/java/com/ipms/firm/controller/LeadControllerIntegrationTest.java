package com.ipms.firm.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ipms.firm.dto.*;
import com.ipms.firm.enums.Quadrant;
import com.ipms.firm.handler.LeadHandler;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-firm/application.yml")
public class LeadControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private LeadHandler handler;

    ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void givenLeadDto_whenSave_thenReturnBaseResponse() throws Exception {
        var leadDto = LeadDto.builder()
                .nameSurname("Test Lead")
                .firm("Test Firm")
                .email("Test Email")
                .leadOwner("Test Lead Owner")
                .leadStatus("Test Lead Status")
                .phoneNumber("1234567890")
                .build();

        when(handler.save(any(LeadDto.class))).thenReturn(leadDto);

        mvc.perform(post("/lead")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(leadDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetLeadList_thenReturnBaseResponse() throws Exception {
        var leadDto = LeadDto.builder()
                .nameSurname("Test Lead")
                .phoneNumber("1234567890")
                .build();

        Mockito.when(handler.getAll()).thenReturn(List.of(leadDto));

        mvc.perform(get("/lead")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenLeadDtoAndId_whenUpdate_thenReturnBaseResponse() throws Exception {
        var leadDto = LeadDto.builder()
                .nameSurname("Test Lead")
                .firm("Test Firm")
                .email("Test Email")
                .leadOwner("Test Lead Owner")
                .leadStatus("Test Lead Status")
                .phoneNumber("1234567890")
                .build();

        Mockito.when(handler.update(Mockito.any(LeadDto.class), Mockito.anyLong())).thenReturn(leadDto);

        mvc.perform(put("/lead/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(leadDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenId_whenDelete_thenDoesNotThrowAnything() throws Exception {
        Mockito.doNothing().when(handler).delete(Mockito.anyLong());

        mvc.perform(delete("/lead/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenLeadFilterRequest_whenGetAllPageable_thenReturnBaseResponse() throws Exception {
        var leadFilterRequest = LeadFilterRequest
                .builder()
                .idList(List.of(1L, 2L))
                .statusList(List.of("NEW", "IN_PROGRESS"))
                .quadrantList(List.of(Quadrant.SUPERSTARS))
                .leadOwnerList(List.of("Test Lead Owner"))
                .firmList(List.of("Test Firm"))
                .sortField("createdAt")
                .sortDirection("DESC")
                .build();

        var leadDto = LeadDto.builder()
                .nameSurname("Test Lead")
                .build();

        var leadPageDto = LeadPageDto.builder()
                .totalPages(1)
                .totalElements(1L)
                .leadDtos(List.of(leadDto))
                .build();

        Mockito.when(handler.getAllPageable(Mockito.any(LeadFilterRequest.class), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(leadPageDto);

        mvc.perform(get("/lead/{page}/{size}", 0, 10)
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(leadFilterRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenLeadId_whenGetLeadById_thenReturnBaseResponse() throws Exception {
        var leadDto = LeadDto.builder()
                .nameSurname("Test Lead")
                .firm("Test Firm")
                .email("Test Email")
                .leadOwner("Test Lead Owner")
                .leadStatus("Test Lead Status")
                .phoneNumber("1234567890")
                .build();

        when(handler.save(any(LeadDto.class))).thenReturn(leadDto);

        mvc.perform(post("/lead/{id}", leadDto.getId())
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(leadDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

}
