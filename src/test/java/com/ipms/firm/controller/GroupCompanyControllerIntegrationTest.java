package com.ipms.firm.controller;

import com.ipms.firm.dto.GroupCompanyDto;
import com.ipms.firm.handler.GroupCompanyHandler;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.ArrayList;
import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-firm/application.yml")
class GroupCompanyControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private GroupCompanyHandler handler;

    @Test
    void whenGroupCompanyList_thenReturnBaseResponse() throws Exception {
        var groupCompanyDto = GroupCompanyDto.builder()
                .id(1L)
                .name("testName")
                .build();
        var groupCompanyDtoList = new ArrayList<>(List.of(groupCompanyDto));
        Mockito.when(handler.getAll()).thenReturn(groupCompanyDtoList);
        mvc.perform(get("/group-company")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void whenGetByName_thenReturnBaseResponse() throws Exception {
        var groupCompanyDto = GroupCompanyDto.builder()
                .id(1L)
                .name("testName")
                .build();
        Mockito.when(handler.getByName(Mockito.anyString())).thenReturn(List.of(groupCompanyDto));
        mvc.perform(get("/group-company/search?name=test")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}