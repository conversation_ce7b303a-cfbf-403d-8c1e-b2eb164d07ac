package com.ipms.billing.service.impl;

import com.ipms.billing.client.ParameterClient;
import com.ipms.billing.service.OrderNumberService;
import com.ipms.billing.dto.ParameterResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.jdbc.core.JdbcTemplate;

import java.math.BigInteger;
import java.time.Year;
import java.util.List;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class OrderNumberServiceImplTest {

    @Mock
    private ParameterClient parameterClient;

    @Mock
    private OrderNumberSequenceRepository sequenceRepository;

    private OrderNumberService orderNumberService;

    private static final String TEST_PREFIX = "BO";
    private static final String CURRENT_YEAR_SUFFIX = String.format("%02d", Year.now().getValue() % 100);

    @BeforeEach
    void setUp() {
        orderNumberService = new OrderNumberServiceImpl(parameterClient, sequenceRepository);
        
        // Setup common mocks
        var payload = ParameterResponse.Payload.builder()
                .value(TEST_PREFIX)
                .build();
        var parameterResponse = ParameterResponse.builder()
                .payload(payload)
                .build();
        
        when(parameterClient.getByKey("ORDER_NUMBER_PREFIX")).thenReturn(parameterResponse);
    }

    @Test
    void generateOrderNumber_ShouldReturnValidFormat() {
        // Given
        when(sequenceRepository.getNextSequenceValue())
                .thenReturn(1L);

        // When
        String orderNumber = orderNumberService.generateOrderNumber();

        // Then
        assertThat(orderNumber)
                .isNotNull()
                .matches("^BO\\d{8}$")
                .startsWith(TEST_PREFIX + CURRENT_YEAR_SUFFIX)
                .endsWith("000001");

        verify(parameterClient).getByKey("ORDER_NUMBER_PREFIX");
        verify(sequenceRepository).getNextSequenceValue();
    }

    @Test
    void generateOrderNumber_ShouldFormatSequenceWithLeadingZeros() {
        // Given
        when(sequenceRepository.getNextSequenceValue())
                .thenReturn(42L);

        // When
        String orderNumber = orderNumberService.generateOrderNumber();

        // Then
        assertThat(orderNumber).endsWith("000042");
    }

    @Test
    void generateOrderNumber_ShouldHandleLargeSequenceNumbers() {
        // Given
        when(sequenceRepository.getNextSequenceValue())
                .thenReturn(999999L);

        // When
        String orderNumber = orderNumberService.generateOrderNumber();

        // Then
        assertThat(orderNumber).endsWith("999999");
    }

    @Test
    void generateOrderNumbers_ShouldReturnMultipleUniqueNumbers() {
        // Given
        int count = 5;
        when(sequenceRepository.getNextSequenceValues(count))
                .thenReturn(List.of(
                    BigInteger.valueOf(1L),
                    BigInteger.valueOf(2L),
                    BigInteger.valueOf(3L),
                    BigInteger.valueOf(4L),
                    BigInteger.valueOf(5L)
                ));

        // When
        List<String> orderNumbers = orderNumberService.generateOrderNumbers(count);

        // Then
        assertThat(orderNumbers)
                .hasSize(count)
                .allMatch(orderNumber -> orderNumber.matches("^BO\\d{8}$"))
                .doesNotHaveDuplicates();

        // Verify sequential numbering
        assertThat(orderNumbers.get(0)).endsWith("000001");
        assertThat(orderNumbers.get(1)).endsWith("000002");
        assertThat(orderNumbers.get(2)).endsWith("000003");
        assertThat(orderNumbers.get(3)).endsWith("000004");
        assertThat(orderNumbers.get(4)).endsWith("000005");

        verify(sequenceRepository).getNextSequenceValues(count);
    }

    @Test
    void generateOrderNumbers_WithInvalidCount_ShouldThrowException() {
        // When & Then
        assertThatThrownBy(() -> orderNumberService.generateOrderNumbers(0))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Count must be at least 1");

        assertThatThrownBy(() -> orderNumberService.generateOrderNumbers(-1))
                .isInstanceOf(IllegalArgumentException.class)
                .hasMessage("Count must be at least 1");
    }

    @Test
    void generateOrderNumber_WhenParameterServiceFails_ShouldThrowException() {
        // Given
        when(parameterClient.getByKey("ORDER_NUMBER_PREFIX"))
                .thenThrow(new RuntimeException("Parameter service unavailable"));

        // When & Then
        assertThatThrownBy(() -> orderNumberService.generateOrderNumber())
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Unable to retrieve order number prefix")
                .hasCauseInstanceOf(RuntimeException.class);
    }

    @Test
    void generateOrderNumber_WhenDatabaseFails_ShouldThrowException() {
        // Given
        when(sequenceRepository.getNextSequenceValue())
                .thenThrow(new RuntimeException("Database unavailable"));

        // When & Then
        assertThatThrownBy(() -> orderNumberService.generateOrderNumber())
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Unable to generate order number sequence")
                .hasCauseInstanceOf(RuntimeException.class);
    }

    @Test
    void isValidOrderNumberFormat_ShouldValidateCorrectFormat() {
        // Valid formats
        assertThat(orderNumberService.isValidOrderNumberFormat("BO24000001")).isTrue();
        assertThat(orderNumberService.isValidOrderNumberFormat("ABC25123456")).isTrue();
        assertThat(orderNumberService.isValidOrderNumberFormat("BILL24999999")).isTrue();

        // Invalid formats
        assertThat(orderNumberService.isValidOrderNumberFormat(null)).isFalse();
        assertThat(orderNumberService.isValidOrderNumberFormat("")).isFalse();
        assertThat(orderNumberService.isValidOrderNumberFormat("   ")).isFalse();
        assertThat(orderNumberService.isValidOrderNumberFormat("BO2400001")).isFalse(); // Too short
        assertThat(orderNumberService.isValidOrderNumberFormat("BO240000001")).isFalse(); // Too long
        assertThat(orderNumberService.isValidOrderNumberFormat("B24000001")).isFalse(); // Too short prefix
        assertThat(orderNumberService.isValidOrderNumberFormat("BO2A000001")).isFalse(); // Invalid characters
        assertThat(orderNumberService.isValidOrderNumberFormat("bo24000001")).isFalse(); // Lowercase prefix
    }

    @Test
    void generateOrderNumbers_WithSingleCount_ShouldReturnOneNumber() {
        // Given
        when(sequenceRepository.getNextSequenceValues(1))
                .thenReturn(List.of(BigInteger.valueOf(10L)));

        // When
        List<String> orderNumbers = orderNumberService.generateOrderNumbers(1);

        // Then
        assertThat(orderNumbers)
                .hasSize(1)
                .contains(TEST_PREFIX + CURRENT_YEAR_SUFFIX + "000010");
    }

    @Test
    void generateOrderNumbers_WhenDatabaseFails_ShouldThrowException() {
        // Given
        when(sequenceRepository.getNextSequenceValues(5))
                .thenThrow(new RuntimeException("Database unavailable"));

        // When & Then
        assertThatThrownBy(() -> orderNumberService.generateOrderNumbers(5))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Unable to generate bulk order number sequences")
                .hasCauseInstanceOf(RuntimeException.class);
    }

    @Test
    void generateOrderNumber_ShouldUseCurrentYear() {
        // Given
        when(sequenceRepository.getNextSequenceValue())
                .thenReturn(1L);

        // When
        String orderNumber = orderNumberService.generateOrderNumber();

        // Then
        String expectedYearPart = String.format("%02d", Year.now().getValue() % 100);
        assertThat(orderNumber).contains(expectedYearPart);
    }
}