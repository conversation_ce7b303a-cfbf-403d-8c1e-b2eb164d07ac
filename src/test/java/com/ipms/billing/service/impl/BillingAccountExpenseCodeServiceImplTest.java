package com.ipms.billing.service.impl;

import com.ipms.billing.dto.BillingAccountExpenseCodeFilterRequest;
import com.ipms.billing.mapper.BillingAccountExpenseCodeMapper;
import com.ipms.billing.model.BillingAccountExpenseCode;
import com.ipms.billing.repository.BillingAccountExpenseCodeRepository;
import com.ipms.billing.specification.BillingAccountExpenseCodeSpecification;
import org.junit.jupiter.api.Assertions;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
public class BillingAccountExpenseCodeServiceImplTest {
    @InjectMocks
    private BillingAccountExpenseCodeServiceImpl service;

    @Mock
    private BillingAccountExpenseCodeRepository repository;

    @Mock
    BillingAccountExpenseCodeMapper mapper;

    @Test
    void whenGetAll_thenReturnListOfBillingAccountExpenseCodeDto() {
        when(repository.findAll(any(BillingAccountExpenseCodeSpecification.class)))
                .thenReturn(List.of(mock(BillingAccountExpenseCode.class), mock(BillingAccountExpenseCode.class)));

        var billingAccountExpenseCodeDtos = service.getAll(mock(BillingAccountExpenseCodeFilterRequest.class));

        verify(repository, times(1)).findAll(any(BillingAccountExpenseCodeSpecification.class));
        Assertions.assertEquals(2, billingAccountExpenseCodeDtos.size());
    }
}
