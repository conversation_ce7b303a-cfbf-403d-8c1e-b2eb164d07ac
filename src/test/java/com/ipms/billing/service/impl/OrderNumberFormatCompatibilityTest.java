package com.ipms.billing.service.impl;

import com.ipms.billing.client.ParameterClient;
import com.ipms.billing.dto.ParameterResponse;
import com.ipms.billing.repository.OrderNumberSequenceRepository;
import com.ipms.billing.service.OrderNumberService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.Mock;
import org.mockito.junit.jupiter.MockitoExtension;

import java.time.Year;
import java.util.List;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Test class to verify that the new OrderNumberService implementation
 * maintains full compatibility with the existing order number format.
 * 
 * Original format: PREFIX + YY + NNNNNN
 * - PREFIX: From parameter "ORDER_NUMBER_PREFIX" (e.g., "BO")
 * - YY: Current year last 2 digits (e.g., "24" for 2024)
 * - NNNNNN: 6-digit zero-padded sequence number
 */
@ExtendWith(MockitoExtension.class)
class OrderNumberFormatCompatibilityTest {

    @Mock
    private ParameterClient parameterClient;

    @Mock
    private OrderNumberSequenceRepository sequenceRepository;

    private OrderNumberService orderNumberService;

    @BeforeEach
    void setUp() {
        orderNumberService = new OrderNumberServiceImpl(parameterClient, sequenceRepository);
    }

    @Test
    void generateOrderNumber_ShouldMatchOriginalFormat_WithStandardPrefix() {
        // Given - Standard prefix "BO"
        setupParameterClient("BO");
        when(sequenceRepository.getNextSequenceValue())
                .thenReturn(1L);

        // When
        String orderNumber = orderNumberService.generateOrderNumber();

        // Then
        String expectedYear = String.format("%02d", Year.now().getValue() % 100);
        String expectedOrderNumber = "BO" + expectedYear + "000001";
        
        assertThat(orderNumber)
                .isEqualTo(expectedOrderNumber)
                .hasSize(10) // BO(2) + YY(2) + NNNNNN(6) = 10
                .matches("^BO\\d{8}$");
    }

    @Test
    void generateOrderNumber_ShouldMatchOriginalFormat_WithDifferentPrefix() {
        // Given - Different prefix "BILL"
        setupParameterClient("BILL");
        when(sequenceRepository.getNextSequenceValue())
                .thenReturn(123L);

        // When
        String orderNumber = orderNumberService.generateOrderNumber();

        // Then
        String expectedYear = String.format("%02d", Year.now().getValue() % 100);
        String expectedOrderNumber = "BILL" + expectedYear + "000123";
        
        assertThat(orderNumber)
                .isEqualTo(expectedOrderNumber)
                .hasSize(12) // BILL(4) + YY(2) + NNNNNN(6) = 12
                .matches("^BILL\\d{8}$");
    }

    @Test
    void generateOrderNumber_ShouldPadSequenceNumbers_Correctly() {
        // Given
        setupParameterClient("BO");
        
        // Test various sequence numbers to verify padding
        var testCases = List.of(
                new TestCase(1L, "000001"),
                new TestCase(42L, "000042"),
                new TestCase(999L, "000999"),
                new TestCase(1000L, "001000"),
                new TestCase(12345L, "012345"),
                new TestCase(999999L, "999999")
        );

        String expectedYear = String.format("%02d", Year.now().getValue() % 100);

        for (TestCase testCase : testCases) {
            // When
            when(sequenceRepository.getNextSequenceValue())
                    .thenReturn(testCase.sequenceValue);
            
            String orderNumber = orderNumberService.generateOrderNumber();

            // Then
            String expectedOrderNumber = "BO" + expectedYear + testCase.expectedPadding;
            assertThat(orderNumber)
                    .describedAs("Sequence %d should be padded as %s", testCase.sequenceValue, testCase.expectedPadding)
                    .isEqualTo(expectedOrderNumber);
        }
    }

    @Test
    void generateOrderNumber_ShouldUseCurrentYear_Consistently() {
        // Given
        setupParameterClient("BO");
        when(sequenceRepository.getNextSequenceValue())
                .thenReturn(1L);

        // When - Generate multiple numbers
        String orderNumber1 = orderNumberService.generateOrderNumber();
        String orderNumber2 = orderNumberService.generateOrderNumber();

        // Then - Both should use same year
        String currentYear = String.format("%02d", Year.now().getValue() % 100);
        assertThat(orderNumber1.substring(2, 4)).isEqualTo(currentYear);
        assertThat(orderNumber2.substring(2, 4)).isEqualTo(currentYear);
    }

    @Test
    void generateOrderNumbers_BulkGeneration_ShouldMaintainFormatConsistency() {
        // Given
        setupParameterClient("BO");
        when(sequenceRepository.getNextSequenceValues(5))
                .thenReturn(List.of(
                    BigInteger.valueOf(1L),
                    BigInteger.valueOf(2L),
                    BigInteger.valueOf(3L),
                    BigInteger.valueOf(4L),
                    BigInteger.valueOf(5L)
                ));

        // When
        List<String> orderNumbers = orderNumberService.generateOrderNumbers(5);

        // Then
        String expectedYear = String.format("%02d", Year.now().getValue() % 100);
        String expectedPrefix = "BO" + expectedYear;
        
        assertThat(orderNumbers)
                .hasSize(5)
                .allMatch(orderNumber -> orderNumber.startsWith(expectedPrefix))
                .allMatch(orderNumber -> orderNumber.matches("^BO\\d{8}$"))
                .containsExactly(
                        expectedPrefix + "000001",
                        expectedPrefix + "000002", 
                        expectedPrefix + "000003",
                        expectedPrefix + "000004",
                        expectedPrefix + "000005"
                );
    }

    @Test
    void generateOrderNumber_ShouldHandleYearTransition_Correctly() {
        // This test verifies that the year component is calculated correctly
        // Note: This test will pass regardless of when it's run because it uses Year.now()
        
        // Given
        setupParameterClient("BO");  
        when(sequenceRepository.getNextSequenceValue())
                .thenReturn(1L);

        // When
        String orderNumber = orderNumberService.generateOrderNumber();

        // Then - Verify year component matches current year % 100
        int currentYear = Year.now().getValue();
        String expectedYearSuffix = String.format("%02d", currentYear % 100);
        String actualYearSuffix = orderNumber.substring(2, 4);
        
        assertThat(actualYearSuffix)
                .describedAs("Year suffix should match current year %d -> %s", currentYear, expectedYearSuffix)
                .isEqualTo(expectedYearSuffix);
    }

    @Test
    void isValidOrderNumberFormat_ShouldRecognizeExistingFormats() {
        // Test various existing order number formats that should be valid
        var validFormats = List.of(
                "**********",   // Standard format
                "BILL25123456", // Longer prefix
                "AB24999999",   // Different prefix
                "XYZ99000001"   // Edge case year
        );

        for (String format : validFormats) {
            assertThat(orderNumberService.isValidOrderNumberFormat(format))
                    .describedAs("Format '%s' should be valid", format)
                    .isTrue();
        }
    }

    @Test
    void compareWithOriginalImplementation_LogicalEquivalence() {
        // This test simulates the original implementation logic to ensure equivalence
        
        // Given - Simulate original parameters
        setupParameterClient("BO");
        String orderNumberPrefix = "BO";
        String initialNumber = "000000";  // Original fallback
        int yy = Year.now().getValue() % 100;
        
        // Simulate original logic: if this was the first order, it would use 000000 + 1 = 000001
        int expectedFirstNumber = Integer.parseInt(initialNumber) + 1;
        String expectedFirstOrderNumber = String.format("%s%02d%06d", orderNumberPrefix, yy, expectedFirstNumber);
        
        // When - Generate using new implementation
        when(sequenceRepository.getNextSequenceValue())
                .thenReturn(1L); // First sequence value
        
        String actualOrderNumber = orderNumberService.generateOrderNumber();
        
        // Then - Should match original logic
        assertThat(actualOrderNumber)
                .describedAs("New implementation should match original logic for first order number")
                .isEqualTo(expectedFirstOrderNumber);
    }

    private void setupParameterClient(String prefix) {
        var payload = ParameterResponse.Payload.builder()
                .value(prefix)
                .build();
        var parameterResponse = ParameterResponse.builder()
                .payload(payload)
                .build();
        when(parameterClient.getByKey("ORDER_NUMBER_PREFIX")).thenReturn(parameterResponse);
    }

    private static class TestCase {
        final Long sequenceValue;
        final String expectedPadding;

        TestCase(Long sequenceValue, String expectedPadding) {
            this.sequenceValue = sequenceValue;
            this.expectedPadding = expectedPadding;
        }
    }
}