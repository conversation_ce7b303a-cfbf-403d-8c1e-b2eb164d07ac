package com.ipms.billing.service.impl;

import com.ipms.billing.client.ActivityClient;
import com.ipms.billing.client.ParamCommandClient;
import com.ipms.billing.dto.*;
import com.ipms.billing.enums.ExpenseCodeType;
import com.ipms.billing.exception.BillingAccountNotSameException;
import com.ipms.billing.exception.ExpenseNotFoundException;
import com.ipms.billing.mapper.ExpenseMapper;
import com.ipms.billing.mapper.ExpenseMapperImpl;
import com.ipms.billing.model.Expense;
import com.ipms.billing.repository.ExpenseRepository;
import com.ipms.billing.service.BillingAccountService;
import com.ipms.billing.specification.ExpenseToBeBilledSpecification;
import com.ipms.billing.validator.ExpenseValidator;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.enums.TransferType;
import com.ipms.core.common.model.TransferEvent;
import com.ipms.core.common.utils.NumberUtils;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class ExpenseServiceTest {

    @InjectMocks
    private ExpenseServiceImpl service;

    @Mock
    private ExpenseRepository repository;

    @Mock
    private ActivityClient activityClient;

    @Mock
    private ProducerService producerService;

    @Mock
    private ParamCommandClient paramCommandClient;

    @Spy
    private ExpenseMapper mapper = new ExpenseMapperImpl();

    @Captor
    private ArgumentCaptor<Expense> expenseCaptor;

    @Captor
    private ArgumentCaptor<TransferEvent> transferEventCaptor;

    @Mock
    private BillingAccountService billingAccountService;

    @Mock
    private ExpenseValidator validator;


    @Test
    void whenSave_thenReturnExpenseDto() {
        var expense = Expense.builder()
                .id(1L)
                .expenseCode("1234")
                .expenseDate(LocalDate.now())
                .currency("USD")
                .description("description")
                .unitPrice(BigDecimal.TEN)
                .quantity(10L)
                .build();
        var expenseDto = ExpenseDto.builder()
                .id(1L)
                .activityId(1L)
                .expenseCode("1234")
                .expenseDate(LocalDate.now())
                .currency("USD")
                .description("description")
                .unitPrice(BigDecimal.TEN)
                .quantity(10L)
                .accrualNumber("ACC-123")
                .build();

        when(mapper.toExpense(any(ExpenseDto.class))).thenReturn(expense);
        when(repository.save(any(Expense.class))).thenReturn(expense);
        when(mapper.toExpenseDto(any(Expense.class))).thenReturn(expenseDto);
        when(activityClient.addExpense(anyLong(), anyLong()))
                .thenReturn(ActivityResponse.builder().code(200).build());
        when(paramCommandClient.getExpenseCode(anyString(), anyString()))
                .thenReturn(ExpenseCodeResponse.builder()
                        .payload(ExpenseCodeResponse.Payload.builder()
                                .type(ExpenseCodeType.SERVICE_FEE)
                                .build())
                        .build());

        var result = service.save(expenseDto);

        assertThat(result.getId()).isEqualTo(expense.getId());
        verify(repository).save(any(Expense.class));
    }

    @Test
    void givenIdAndPageAndSize_whenGetByActivity_thenReturnExpensePageDto() {
        var expense = Expense.builder()
                .id(1L)
                .expenseCode("1234")
                .expenseDate(LocalDate.now())
                .currency("USD")
                .description("description")
                .unitPrice(BigDecimal.TEN)
                .quantity(10L)
                .build();
        Page<Expense> page = new PageImpl<>(List.of(expense), Pageable.ofSize(1), 1);
        when(repository.findByIdInOrderByExpenseDateDesc(Mockito.anyList(), any(Pageable.class)))
                .thenReturn(page);
        assertThat(service.getByIds(List.of(1L,2L), 2, 3).getTotalElements())
                .isEqualTo(1L);
    }

    @Test
    void whenDelete_thenDoesNotThrowAnyException() {
        var expense = Expense.builder()
                .id(1L)
                .expenseCode("1234")
                .expenseDate(LocalDate.now())
                .currency("USD")
                .description("description")
                .unitPrice(BigDecimal.TEN)
                .quantity(10L)
                .build();
        when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(expense));

        assertThatCode(() -> service.delete(1L, 0L)).doesNotThrowAnyException();
    }

    @Test
    void whenDelete_thenThrowExpenseNotFoundException() {
        when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.empty());
        Throwable thrown = catchThrowable(()->service.delete(1L, 0L));
        assertThat(thrown).isInstanceOf(ExpenseNotFoundException.class);
    }

    @Test
    void whenGetByIdIn_thenReturnExpenseDtoList() {
        var expense = Expense.builder()
                .id(1L)
                .expenseCode("1234")
                .expenseDate(LocalDate.now())
                .currency("USD")
                .description("description")
                .unitPrice(BigDecimal.TEN)
                .quantity(10L)
                .build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(expense));
        assertThat(service.getByIdIn(List.of(1L))).isNotEmpty();
    }

    @Test
    void whenApproveBillingBulk_thenReturnDisbursementDtoList() {
        var expense1 = Expense.builder().id(1L).build();
        var expense2 = Expense.builder().id(2L).build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(expense1, expense2));
        when(repository.saveAll(Mockito.anyList()))
                .thenReturn(List.of(expense1, expense2));

        assertThat(service.approveBillingBulk(List.of(1L, 2L)))
                .isNotEmpty();

        assertThat(expense1.getIsBillingApproved()).isTrue();
        assertThat(expense2.getIsBillingApproved()).isTrue();
    }

    @Test
    void whenCancelApproveBillingBulk_thenReturnDisbursementDtoList() {
        var expense1 = Expense.builder().id(1L).build();
        var expense2 = Expense.builder().id(2L).build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(expense1, expense2));
        when(repository.saveAll(Mockito.anyList()))
                .thenReturn(List.of(expense1, expense2));

        assertThat(service.cancelApprovelBillingBulk(List.of(1L, 2L)))
                .isNotEmpty();

        assertThat(expense1.getIsBillingApproved()).isFalse();
        assertThat(expense2.getIsBillingApproved()).isFalse();
    }

    @Test
    void whenDeleteBulk_thenDoesNotThrowAnyException() {
        var expense1 = Expense.builder().id(1L).build();
        var expense2 = Expense.builder().id(2L).build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(expense1, expense2));

        assertThatCode(() -> service.deleteBulk(List.of(1L, 2L))).doesNotThrowAnyException();
    }

    @Test
    void whenGetToBeBilledExpensePage_thenReturnExpensePageDto() {
        var expense = Expense.builder()
                .id(1L)
                .expenseCode("1234")
                .expenseDate(LocalDate.now())
                .currency("USD")
                .description("description")
                .unitPrice(BigDecimal.TEN)
                .quantity(10L)
                .build();
        Page<Expense> page = new PageImpl<>(List.of(expense), Pageable.ofSize(1), 1);
        when(repository.findAll(any(), any(Pageable.class)))
                .thenReturn(page);
        var expenseFilterRequest = ExpenseFilterRequest.builder()
                .ids(List.of(1L))
                .billingPeriodEnds(LocalDate.now())
                .build();
        assertThat(service.getToBeBilledExpensePage(expenseFilterRequest, 2, 3).getTotalElements())
                .isEqualTo(1L);
    }

    @Test
    void whenGetBilledExpenses_thenReturnExpensePageDto() {
        var expense = Expense.builder()
                .id(1L)
                .expenseCode("1234")
                .expenseDate(LocalDate.now())
                .currency("USD")
                .description("description")
                .unitPrice(BigDecimal.TEN)
                .quantity(10L)
                .build();
        Page<Expense> page = new PageImpl<>(List.of(expense), Pageable.ofSize(1), 1);
        when(repository.findByIdInAndOrderDateBeforeOrderByOrderDateDesc(Mockito.anyList(), any(),
                        any(Pageable.class)))
                .thenReturn(page);
        var expenseFilterRequest = ExpenseFilterRequest.builder()
                .ids(List.of(1L))
                .billingPeriodEnds(LocalDate.now())
                .build();
        assertThat(service.getBilledExpenses(expenseFilterRequest, 2, 3).getTotalElements())
                .isEqualTo(1L);
    }

    @Test
    void whenIsBillable_thenReturnTrue() {
        var expense = Expense.builder()
                .id(1L)
                .expenseCode("1234")
                .expenseDate(LocalDate.now())
                .currency("USD")
                .description("description")
                .unitPrice(BigDecimal.TEN)
                .quantity(10L)
                .build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(expense));
        assertThat(service.isBillable(List.of(1L))).isTrue();
    }

    @Test
    void whenDeleteByDisbursementIds_thenDoesNotThrowAnyException() {
        assertThatCode(() -> service.deleteByDisbursementIds(List.of(1L, 2L))).doesNotThrowAnyException();
    }

    @Test
    void whenDeleteByTimesheetIds_thenDoesNotThrowAnyException() {
        assertThatCode(() -> service.deleteByTimesheetIds(List.of(1L, 2L))).doesNotThrowAnyException();
    }

    @Test
    void whenGetExpensesByOrderNo_thenReturnExpenseDtos() {
        var expense = Expense.builder()
                .id(1L)
                .expenseCode("1234")
                .expenseDate(LocalDate.now())
                .currency("USD")
                .description("description")
                .unitPrice(BigDecimal.TEN)
                .quantity(10L)
                .build();
        when(repository.findByOrderNumber(Mockito.anyString()))
                .thenReturn(List.of(expense));

        var expenseDtos = service.getExpensesByOrderNo("1234");

        assertThat(expenseDtos).hasSize(1);
    }

    @Test
    void whenIsInvoiceCompleted_thenReturnTrue() {
        var expense1 = Expense.builder()
                .invoiceNumber("1")
                .orderNumber("order-number-1")
                .build();
        var expense2 = Expense.builder()
                .orderNumber("order-number-1")
                .invoiceNumber("2")
                .build();

        when(repository.findByIdInAndExpenseDateLessThanEqual(Mockito.anyList(), any(LocalDate.class)))
                .thenReturn(List.of(expense1, expense2));

        assertThat(service.isInvoiceCompleted(LocalDate.now(), List.of(1L))).isTrue();
    }

    @Test
    void whenIsInvoiceCompleted_thenReturnFalse() {
        var expense1 = Expense.builder()
                .orderNumber("order-number-1")
                .invoiceNumber("1")
                .build();
        var expense2 = Expense.builder()
                .orderNumber("order-number-1")
                .build();

        when(repository.findByIdInAndExpenseDateLessThanEqual(Mockito.anyList(), any(LocalDate.class)))
                .thenReturn(List.of(expense1, expense2));

        assertThat(service.isInvoiceCompleted(LocalDate.now(), List.of(1L))).isFalse();
    }

    @Test
    void whenProcessInvoiceFetchedEvent_thenDoesReturnNothing() {
        var invoiceLine1 = InvoiceFetchedEvent.InvoiceLine.builder()
                .billingId("1")
                .expenseIds(List.of(1L, 2L))
                .priceAmount("134,9718")
                .taxRate("2,45")
                .build();
        var invoiceLine2 = InvoiceFetchedEvent.InvoiceLine.builder()
                .billingId("2")
                .expenseIds(List.of(3L, 4L))
                .priceAmount("78,7766")
                .build();
        var invoice = InvoiceFetchedEvent.Invoice.builder()
                .invoiceNo("1")
                .erpDocumentNo("3")
                .invoiceDate("2024-11-26")
                .invoiceLines(List.of(invoiceLine1, invoiceLine2))
                .build();
        var expense1 = Expense.builder()
                .id(1L)
                .build();
        var expense2 = Expense.builder()
                .id(2L)
                .build();
        var expense3 = Expense.builder()
                .id(3L)
                .build();

        when(repository.findByIdIn(invoiceLine1.getExpenseIds())).thenReturn(List.of(expense1, expense2));
        when(repository.findByIdIn(invoiceLine2.getExpenseIds())).thenReturn(List.of(expense3));

        service.updateInvoiceFields(invoice);

        assertThat(LocalDate.parse(invoice.getInvoiceDate())).isEqualTo(expense1.getInvoiceDate());
        assertThat(LocalDate.parse(invoice.getInvoiceDate())).isEqualTo(expense2.getInvoiceDate());
        assertThat(LocalDate.parse(invoice.getInvoiceDate())).isEqualTo(expense3.getInvoiceDate());
        assertThat(NumberUtils.parseBigDecimal(invoiceLine2.getPriceAmount())).isEqualTo(expense3.getDiscountPrice());
    }

    @Test
    void whenGetOrderCreationExpenses_thenThrowBillingAccountNotSameException() {
        var assignee = "test";
        var payload1 = ActivityListResponse.Payload.builder()
                .id(1L)
                .billingAccountId(1L)
                .build();
        var payload2 = ActivityListResponse.Payload.builder()
                .id(2L)
                .billingAccountId(2L)
                .build();
        var activityListResponse = ActivityListResponse.builder()
                .payload(List.of(payload1, payload2))
                .build();

        when(activityClient.getBillingCartActivities(assignee)).thenReturn(activityListResponse);
        assertThatThrownBy(() -> service.getOrderCreationExpenses(assignee, 0, 10))
                .isInstanceOf(BillingAccountNotSameException.class);
    }

    @Test
    void whenGetOrderCreationExpenses_thenReturnsEmptyExpenseSummaryPageDto() {
        var assignee = "test";
        var payload1 = ActivityListResponse.Payload.builder()
                .id(1L)
                .billingAccountId(1L)
                .build();
        var payload2 = ActivityListResponse.Payload.builder()
                .id(2L)
                .billingAccountId(1L)
                .build();
        var activityListResponse = ActivityListResponse.builder()
                .payload(List.of(payload1, payload2))
                .build();

        when(activityClient.getBillingCartActivities(assignee)).thenReturn(activityListResponse);
        var expense1 = Expense.builder()
                .expenseCode("1")
                .orderDate(LocalDate.now())
                .isBillingApproved(true)
                .build();
        when(repository.findByIdIn(anyList())).thenReturn(List.of(expense1));
        var orderCreationExpenses = service.getOrderCreationExpenses(assignee, 0, 10);

        assertThat(orderCreationExpenses.getExpenseSummaryDtos()).isEmpty();
    }

    @Test
    void whenGetOrderCreationExpenses_thenReturnsExpenseSummaryPageDto() {
        var assignee = "test";
        var payload1 = ActivityListResponse.Payload.builder()
                .billingAccountId(1L)
                .build();
        var payload2 = ActivityListResponse.Payload.builder()
                .billingAccountId(1L)
                .build();
        var activityListResponse = ActivityListResponse.builder()
                .payload(List.of(payload1, payload2))
                .build();

        when(activityClient.getBillingCartActivities(assignee)).thenReturn(activityListResponse);
        var expense1 = Expense.builder()
                .expenseCode("1")
                .currency("TL")
                .unitPrice(BigDecimal.TEN)
                .quantity(1L)
                .isBillingApproved(true)
                .build();
        var expense2 = Expense.builder()
                .expenseCode("1")
                .currency("TL")
                .unitPrice(BigDecimal.TEN)
                .quantity(2L)
                .isBillingApproved(true)
                .build();
        var expense3 = Expense.builder()
                .expenseCode("2")
                .currency("TL")
                .unitPrice(BigDecimal.ONE)
                .quantity(3L)
                .isBillingApproved(true)
                .build();
        var expense4 = Expense.builder()
                .expenseCode("2")
                .currency("TL")
                .unitPrice(BigDecimal.TEN)
                .quantity(4L)
                .isBillingApproved(true)
                .build();
        when(repository.findByIdIn(anyList())).thenReturn(List.of(expense1, expense2, expense3, expense4));
        when(paramCommandClient.getExpenseCode(anyString(), anyString()))
                .thenReturn(ExpenseCodeResponse.builder()
                        .payload(ExpenseCodeResponse.Payload.builder().build())
                        .build());

        var orderCreationExpenses = service.getOrderCreationExpenses(assignee, 0, 10);

        assertThat(orderCreationExpenses.getExpenseSummaryDtos()).hasSize(3);
        assertThat(orderCreationExpenses.getExpenseSummaryDtos().stream()
                .filter(expenseSummaryDto -> expenseSummaryDto.getExpenseCode().equals("1"))
                .toList()
        ).hasSize(1);
        assertThat(orderCreationExpenses.getExpenseSummaryDtos().stream()
                .filter(expenseSummaryDto -> expenseSummaryDto.getExpenseCode().equals("1"))
                .mapToLong(ExpenseSummaryDto::getQuantity)
                .sum()
        ).isEqualTo(3);
        assertThat(orderCreationExpenses.getExpenseSummaryDtos().stream()
                .filter(expenseSummaryDto -> expenseSummaryDto.getExpenseCode().equals("1"))
                .map(ExpenseSummaryDto::getUnitPrice)
                .reduce(BigDecimal.ZERO, BigDecimal::add)
        ).isEqualTo(BigDecimal.TEN);
        assertThat(orderCreationExpenses.getExpenseSummaryDtos().stream()
                .filter(expenseSummaryDto -> expenseSummaryDto.getExpenseCode().equals("2"))
                .toList()
        ).hasSize(2);
    }


    @Test
    void applyBillingDiscount_WhenExpensesAreEligible_ThenUpdatesAndSavesExpenses() {
        var expenseIds = List.of(1L, 2L, 3L);
        var eligibleIds = List.of(1L, 3L);
        var discountRate = BigDecimal.valueOf(10);
        var discountRequest = DiscountRequest.builder()
                .ids(expenseIds)
                .discountRate(discountRate)
                .build();

        var expense1 = new Expense();
        expense1.setId(1L);
        var expense2 = new Expense();
        expense2.setId(2L);
        var expense3 = new Expense();
        expense3.setId(3L);

        var expenses = List.of(expense1, expense2, expense3);

        var eligibleResponse = ListLongResponse.builder()
                .payload(eligibleIds)
                .build();
        when(repository.findByIdIn(expenseIds)).thenReturn(expenses);
        when(paramCommandClient.getDiscountEligibleExpenseIds(anyList())).thenReturn(eligibleResponse);

        service.applyBillingDiscount(discountRequest);

        verify(repository).save(expense1);
        verify(repository).save(expense3);
        verify(repository, never()).save(expense2);

        assertThat(expense1.getDiscountRate()).isEqualByComparingTo(discountRate);
        assertThat(expense3.getDiscountRate()).isEqualByComparingTo(discountRate);
        assertThat(expense2.getDiscountRate()).isNull();
    }

    @Test
    void applyBillingDiscount_WhenNoExpensesAreEligible_ThenDoesNotSaveAnyExpense() {
        var expenseIds = List.of(1L, 2L);
        List<Long> eligibleIds = Collections.emptyList();
        var discountRate = new BigDecimal("20.0");

        var discountRequest = new DiscountRequest();
        discountRequest.setIds(expenseIds);
        discountRequest.setDiscountRate(discountRate);

        var expense1 = new Expense();
        expense1.setId(1L);
        var expense2 = new Expense();
        expense2.setId(2L);

        var expenses = List.of(expense1, expense2);


        var eligibleResponse = ListLongResponse.builder()
                .payload(eligibleIds)
                .build();

        when(repository.findByIdIn(expenseIds)).thenReturn(expenses);
        when(paramCommandClient.getDiscountEligibleExpenseIds(anyList())).thenReturn(eligibleResponse);

        service.applyBillingDiscount(discountRequest);

        verify(repository, never()).save(any(Expense.class));
        assertThat(expense1.getDiscountRate()).isNull();
        assertThat(expense2.getDiscountRate()).isNull();
    }

    @Test
    void applyBillingDiscount_WhenEmptyExpenseList_ThenDoesNotCallClientOrSave() {
        var expenseIds = List.of(1L, 2L);
        var discountRate = new BigDecimal("15.0");

        var discountRequest = new DiscountRequest();
        discountRequest.setIds(expenseIds);
        discountRequest.setDiscountRate(discountRate);

        when(repository.findByIdIn(expenseIds)).thenReturn(Collections.emptyList());

        service.applyBillingDiscount(discountRequest);

        verify(repository, never()).save(any(Expense.class));
        verify(paramCommandClient, never()).getDiscountEligibleExpenseIds(anyList());
    }

    @Test
    void clearDiscountRate_WithValidExpenses_ShouldResetDiscountRatesAndSendEvents() {
        var testBillingPeriodEnds = LocalDate.of(2025, 4, 30);
        var expenseIds = Arrays.asList(1L, 2L);

        var expense1 = Expense.builder().id(1L).discountRate(BigDecimal.valueOf(10.5)).build();
        var expense2 = Expense.builder().id(2L).discountRate(BigDecimal.valueOf(20.0)).build();
        var mockExpenses = List.of(expense1, expense2);

        when(repository.findAll(any(ExpenseToBeBilledSpecification.class))).thenReturn(mockExpenses);
        when(repository.save(any(Expense.class))).thenAnswer(invocation -> invocation.getArgument(0));
        doNothing().when(producerService).sendTransfer(any(TransferEvent.class));

        service.clearDiscountRate(expenseIds, testBillingPeriodEnds);

        ArgumentCaptor<ExpenseToBeBilledSpecification> specCaptor = ArgumentCaptor.forClass(ExpenseToBeBilledSpecification.class);
        verify(repository).findAll(specCaptor.capture());

        verify(repository, times(2)).save(expenseCaptor.capture());
        var savedExpenses = expenseCaptor.getAllValues();
        assertThat(savedExpenses.get(0).getDiscountRate()).isNull();
        assertThat(savedExpenses.get(1).getDiscountRate()).isNull();

        verify(producerService, times(2)).sendTransfer(transferEventCaptor.capture());
        var events = transferEventCaptor.getAllValues();
        assertThat(events.get(0).getType()).isEqualTo(TransferType.UPDATE);
        assertThat(events.get(0).getDomainObjectId()).isEqualTo(1L);
        assertThat(events.get(1).getType()).isEqualTo(TransferType.UPDATE);
        assertThat(events.get(1).getDomainObjectId()).isEqualTo(2L);
    }

    @Test
    void clearDiscountRate_WithEmptyExpenseIds_ShouldNotProcessAnyExpenses() {
        var testBillingPeriodEnds = LocalDate.of(2025, 4, 30);
        List<Long> emptyExpenseIds = Collections.emptyList();

        when(repository.findAll(any(ExpenseToBeBilledSpecification.class))).thenReturn(Collections.emptyList());

        service.clearDiscountRate(emptyExpenseIds, testBillingPeriodEnds);

        verify(repository).findAll(any(ExpenseToBeBilledSpecification.class));
        verify(repository, never()).save(any(Expense.class));
        verify(producerService, never()).sendTransfer(any());
    }

    @Test
    void clearDiscountRate_WithNoMatchingExpenses_ShouldNotSaveAnyExpenses() {
        var testBillingPeriodEnds = LocalDate.of(2025, 4, 30);
        var expenseIds = Arrays.asList(3L, 4L);

        when(repository.findAll(any(ExpenseToBeBilledSpecification.class))).thenReturn(Collections.emptyList());

        service.clearDiscountRate(expenseIds, testBillingPeriodEnds);

        verify(repository).findAll(any(ExpenseToBeBilledSpecification.class));
        verify(repository, never()).save(any(Expense.class));
        verify(producerService, never()).sendTransfer(any());
    }

    @Test
    void getTotalAmountForDiscount_whenInputListIsEmpty_shouldReturnZeroAmounts() {
        List<Long> ids = Collections.emptyList();
        List<Expense> expenses = Collections.emptyList();

        when(repository.findByIdIn(anyList())).thenReturn(expenses);

        var totalAmount = service.getTotalAmountForDiscount(ids);

        assertThat(totalAmount).isNotNull();
        assertThat(totalAmount.getEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(totalAmount.getEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo(BigDecimal.ZERO);
        verify(paramCommandClient, never()).getDiscountEligibleExpenseIds(Collections.emptyList());
    }

    @Test
    void getTotalAmountForDiscount_withMixedExpenses_shouldCalculateCorrectly() {
        var ids = List.of(1L, 2L, 3L, 4L);

        var allExpenses = List.of(
                getExpenseDisbursementEligible(),
                getExpenseDisbursementNotEligible(),
                getExpenseServiceFeeEligible(),
                getExpenseServiceFeeNotEligible()
        );
        var eligibleIds = List.of(1L, 3L);

        when(repository.findByIdIn(ids)).thenReturn(allExpenses);
        when(paramCommandClient.getDiscountEligibleExpenseIds(anyList()))
                .thenReturn(ListLongResponse.builder().payload(eligibleIds).build());

        var totalAmount = service.getTotalAmountForDiscount(ids);

        assertThat(totalAmount).isNotNull();
        assertThat(totalAmount.getEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo("20.00");
        assertThat(totalAmount.getEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo("20.00");
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo("15.00");
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo("30.00");
    }

    @Test
    void getTotalAmountForDiscount_withOnlyDisbursements_shouldCalculateCorrectly() {
        var ids = List.of(1L, 2L);
        var expenseDisbursementEligible = getExpenseDisbursementEligible();
        var expenseDisbursementNotEligible = getExpenseDisbursementNotEligible();

        var disbursementExpenses = List.of(expenseDisbursementEligible, expenseDisbursementNotEligible);
        var eligibleIds = List.of(1L);

        when(repository.findByIdIn(ids)).thenReturn(disbursementExpenses);
        when(paramCommandClient.getDiscountEligibleExpenseIds(anyList()))
                .thenReturn(ListLongResponse.builder().payload(eligibleIds).build());

        var totalAmount = service.getTotalAmountForDiscount(ids);

        assertThat(totalAmount).isNotNull();
        assertThat(totalAmount.getEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo("20.00");
        assertThat(totalAmount.getEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo("0.00");
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo("15.00");
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo("0.00");
    }

    @Test
    void getTotalAmountForDiscount_withOnlyServiceFees_shouldCalculateCorrectly() {
        var ids = List.of(3L, 4L);
        var expenseServiceFeeEligible = getExpenseServiceFeeEligible();
        var expenseServiceFeeNotEligible = getExpenseServiceFeeNotEligible();

        List<Expense> serviceFeeExpenses = List.of(expenseServiceFeeEligible, expenseServiceFeeNotEligible);
        List<Long> eligibleIds = List.of(3L);

        when(repository.findByIdIn(ids)).thenReturn(serviceFeeExpenses);
        when(paramCommandClient.getDiscountEligibleExpenseIds(anyList()))
                .thenReturn(ListLongResponse.builder().payload(eligibleIds).build());

        TotalAmount totalAmount = service.getTotalAmountForDiscount(ids);

        assertThat(totalAmount).isNotNull();
        assertThat(totalAmount.getEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo("0.00");
        assertThat(totalAmount.getEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo("20.00");
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo("0.00");
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo("30.00");
    }

    @Test
    void getTotalAmountForDiscount_withAllEligible_shouldCalculateCorrectly() {
        List<Long> ids = List.of(1L, 3L);
        var expenseDisbursementEligible = getExpenseDisbursementEligible();
        var expenseServiceFeeEligible = getExpenseServiceFeeEligible();

        List<Expense> eligibleExpenses = List.of(expenseDisbursementEligible, expenseServiceFeeEligible);
        List<Long> eligibleIds = List.of(1L, 3L);

        when(repository.findByIdIn(ids)).thenReturn(eligibleExpenses);
        when(paramCommandClient.getDiscountEligibleExpenseIds(anyList()))
                .thenReturn(ListLongResponse.builder().payload(eligibleIds).build());

        TotalAmount totalAmount = service.getTotalAmountForDiscount(ids);

        assertThat(totalAmount).isNotNull();
        assertThat(totalAmount.getEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo("20.00");
        assertThat(totalAmount.getEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo("20.00");
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo("0.00");
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo("0.00");
    }

    @Test
    void getTotalAmountForDiscount_withAllNotEligible_shouldCalculateCorrectly() {
        List<Long> ids = List.of(2L, 4L);
        var expenseDisbursementNotEligible = getExpenseDisbursementNotEligible();
        var expenseServiceFeeNotEligible = getExpenseServiceFeeNotEligible();

        List<Expense> notEligibleExpenses = List.of(expenseDisbursementNotEligible, expenseServiceFeeNotEligible);
        List<Long> eligibleIds = Collections.emptyList();

        when(repository.findByIdIn(ids)).thenReturn(notEligibleExpenses);
        when(paramCommandClient.getDiscountEligibleExpenseIds(anyList()))
                .thenReturn(ListLongResponse.builder().payload(eligibleIds).build());

        TotalAmount totalAmount = service.getTotalAmountForDiscount(ids);

        assertThat(totalAmount).isNotNull();
        assertThat(totalAmount.getEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo("0.00");
        assertThat(totalAmount.getEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo("0.00");
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo("15.00");
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo("30.00");
    }

    @Test
    void getTotalAmountForDiscount_whenRepositoryReturnsEmpty_shouldReturnZeroAmounts() {
        List<Long> ids = List.of(1L, 2L);
        List<Expense> expenses = Collections.emptyList();

        when(repository.findByIdIn(ids)).thenReturn(expenses);

        TotalAmount totalAmount = service.getTotalAmountForDiscount(ids);

        assertThat(totalAmount).isNotNull();
        assertThat(totalAmount.getEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(totalAmount.getEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalDisbursements()).isEqualByComparingTo(BigDecimal.ZERO);
        assertThat(totalAmount.getNotEligibleForDiscount().getTotalServiceFees()).isEqualByComparingTo(BigDecimal.ZERO);
        verify(paramCommandClient, never()).getDiscountEligibleExpenseIds(Collections.emptyList());
    }

    private static Expense getExpenseServiceFeeNotEligible() {
        return Expense.builder()
                .id(4L)
                .unitPrice(new BigDecimal("15.00"))
                .quantity(2L)
                .disbursements(new ArrayList<>())
                .timesheets(List.of(202L))
                .build();
    }

    private static Expense getExpenseServiceFeeEligible() {
        return Expense.builder()
                .id(3L)
                .unitPrice(new BigDecimal("20.00"))
                .quantity(1L).disbursements(new ArrayList<>())
                .timesheets(List.of(201L))
                .build();
    }

    private static Expense getExpenseDisbursementNotEligible() {
        return Expense.builder()
                .id(2L)
                .unitPrice(new BigDecimal("5.00"))
                .quantity(3L)
                .disbursements(List.of(102L))
                .timesheets(new ArrayList<>())
                .build();
    }

    private static Expense getExpenseDisbursementEligible() {
        return Expense.builder()
                .id(1L)
                .unitPrice(new BigDecimal("10.00"))
                .quantity(2L)
                .disbursements(List.of(101L))
                .timesheets(new ArrayList<>())
                .build();
    }

    @Test
    void givenOfficialFeeWithAccrualNumber_whenSave_thenPaymentIsCreatedAndLinked() {
        var expenseDto = ExpenseDto.builder()
                .id(1L)
                .activityId(1L)
                .expenseCode("1234")
                .expenseDate(LocalDate.now())
                .currency("USD")
                .description("Official fee expense")
                .unitPrice(BigDecimal.TEN)
                .quantity(2L)
                .accrualNumber("ACC-123")
                .build();

        var expense = Expense.builder()
                .id(1L)
                .build();

        var savedExpense = Expense.builder()
                .id(1L)
                .build();

        var activityPayload = ActivityResponse.Payload.builder()
                .id(1L)
                .matterId(100L)
                .billingAccountId(200L)
                .build();

        var activityResponse = ActivityResponse.builder()
                .code(200)
                .payload(activityPayload)
                .build();

        var billingAccount = BillingAccountDto.builder()
                .issuerId(50L)
                .build();

        when(paramCommandClient.getExpenseCode("1234", "USD"))
                .thenReturn(ExpenseCodeResponse.builder()
                        .payload(ExpenseCodeResponse.Payload.builder()
                                .type(ExpenseCodeType.OFFICIAL_FEE)
                                .build())
                        .build());

        when(activityClient.getActivity(1L)).thenReturn(activityResponse);
        when(billingAccountService.getById(200L)).thenReturn(billingAccount);
        when(mapper.toExpense(any(ExpenseDto.class))).thenReturn(expense);
        when(repository.save(any(Expense.class))).thenReturn(savedExpense);
        when(mapper.toExpenseDto(any(Expense.class))).thenReturn(expenseDto);
        when(activityClient.addExpense(anyLong(), anyLong()))
                .thenReturn(ActivityResponse.builder().code(200).build());

        var result = service.save(expenseDto);

        assertThat(result.getId()).isEqualTo(1L);

        verify(paramCommandClient).getExpenseCode("1234", "USD");
        verify(activityClient).getActivity(1L);
        verify(billingAccountService).getById(200L);
        verify(repository).save(any(Expense.class));
        verify(activityClient).addExpense(1L, 1L);
    }

    @Test
    void givenValidUpdateData_whenUpdate_thenReturnsUpdatedExpenseDto() {
        var id = 1L;

        var existingExpense = Expense.builder()
                .id(id)
                .expenseCode("1234")
                .currency("USD")
                .description("initial")
                .build();

        var updateDto = ExpenseDto.builder()
                .id(id)
                .expenseCode("5678")
                .currency("USD")
                .description("updated")
                .build();

        var mappedExpense = Expense.builder()
                .id(id)
                .expenseCode("5678")
                .currency("USD")
                .description("updated")
                .build();

        var updatedExpense = Expense.builder()
                .id(id)
                .expenseCode("5678")
                .currency("USD")
                .description("updated")
                .build();

        var updatedDto = ExpenseDto.builder()
                .id(id)
                .expenseCode("5678")
                .currency("USD")
                .description("updated")
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(existingExpense));
        when(mapper.toExpenseFromDto(updateDto, existingExpense)).thenReturn(mappedExpense);
        when(repository.save(mappedExpense)).thenReturn(updatedExpense);
        when(mapper.toExpenseDto(updatedExpense)).thenReturn(updatedDto);

        var result = service.update(id, updateDto);

        assertThat(result.getId()).isEqualTo(id);
        assertThat(result.getExpenseCode()).isEqualTo("5678");

        verify(validator).validateUpdate(existingExpense, updateDto);
        verify(repository).save(mappedExpense);
    }

    @Test
    void givenDifferentFields_whenUpdate_thenValidationIsTriggered() {
        var id = 2L;

        var existing = Expense.builder()
                .id(id)
                .expenseCode("AAA")
                .currency("USD")
                .build();

        var updateDto = ExpenseDto.builder()
                .id(id)
                .expenseCode("BBB")
                .currency("EUR")
                .build();

        var mapped = Expense.builder().id(id).expenseCode("BBB").currency("EUR").build();
        var updated = Expense.builder().id(id).expenseCode("BBB").currency("EUR").build();

        when(repository.findById(id)).thenReturn(Optional.of(existing));
        when(mapper.toExpenseFromDto(updateDto, existing)).thenReturn(mapped);
        when(repository.save(mapped)).thenReturn(updated);
        when(mapper.toExpenseDto(updated)).thenReturn(updateDto);

        var result = service.update(id, updateDto);

        assertThat(result.getExpenseCode()).isEqualTo("BBB");
        assertThat(result.getCurrency()).isEqualTo("EUR");

        verify(validator).validateUpdate(existing, updateDto);
        verify(repository).save(mapped);
    }
}
