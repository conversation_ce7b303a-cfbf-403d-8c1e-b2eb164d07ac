package com.ipms.billing.service.impl;

import com.ipms.billing.client.ParameterClient;
import com.ipms.billing.service.OrderNumberService;
import com.ipms.billing.dto.ParameterResponse;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.test.context.TestPropertySource;

import javax.sql.DataSource;
import java.util.ArrayList;
import java.util.Collections;
import java.util.List;
import java.util.Set;
import java.util.concurrent.*;
import java.util.stream.IntStream;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.Mockito.*;

/**
 * Integration tests to verify OrderNumberService behavior under concurrent load.
 * 
 * These tests use an in-memory H2 database to simulate real database conditions
 * while ensuring thread-safety and uniqueness guarantees.
 */
@SpringBootTest
@TestPropertySource(properties = {
    "spring.datasource.url=jdbc:h2:mem:testdb;DB_CLOSE_DELAY=-1;DB_CLOSE_ON_EXIT=FALSE",
    "spring.datasource.driver-class-name=org.h2.Driver",
    "spring.jpa.hibernate.ddl-auto=create-drop"
})
class OrderNumberServiceConcurrencyTest {

    @MockBean
    private ParameterClient parameterClient;

    private OrderNumberService orderNumberService;
    private JdbcTemplate jdbcTemplate;

    @BeforeEach
    void setUp(DataSource dataSource) {
        // Setup JdbcTemplate with test datasource
        jdbcTemplate = new JdbcTemplate(dataSource);
        
        // Create the sequence in H2 (using H2 syntax)
        jdbcTemplate.execute("CREATE SEQUENCE IF NOT EXISTS billing_order_number_seq START WITH 1 INCREMENT BY 1");
        
        // Setup parameter client mock
        var payload = ParameterResponse.Payload.builder()
                .value("BO")
                .build();
        var parameterResponse = ParameterResponse.builder()
                .payload(payload)
                .build();
        when(parameterClient.getByKey("ORDER_NUMBER_PREFIX")).thenReturn(parameterResponse);
        
        // Create service instance
        orderNumberService = new OrderNumberServiceImpl(parameterClient, jdbcTemplate);
    }

    @Test
    void generateOrderNumber_UnderConcurrentLoad_ShouldProduceUniqueNumbers() throws InterruptedException {
        // Given
        int threadCount = 20;
        int operationsPerThread = 10;
        int expectedTotalOperations = threadCount * operationsPerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        List<String> allGeneratedNumbers = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch latch = new CountDownLatch(threadCount);

        // When - Execute concurrent order number generation
        IntStream.range(0, threadCount).forEach(i -> {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String orderNumber = orderNumberService.generateOrderNumber();
                        allGeneratedNumbers.add(orderNumber);
                    }
                } finally {
                    latch.countDown();
                }
            });
        });

        // Wait for all threads to complete
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();

        // Then - Verify results
        assertThat(completed).isTrue();
        assertThat(allGeneratedNumbers).hasSize(expectedTotalOperations);
        
        // Verify all numbers are unique
        Set<String> uniqueNumbers = Set.copyOf(allGeneratedNumbers);
        assertThat(uniqueNumbers)
                .hasSize(expectedTotalOperations)
                .describedAs("All generated order numbers should be unique");

        // Verify all numbers follow correct format
        assertThat(allGeneratedNumbers)
                .allMatch(orderNumber -> orderNumber.matches("^BO\\d{8}$"))
                .describedAs("All order numbers should follow the correct format");
    }

    @Test
    void generateOrderNumbers_BulkOperationUnderConcurrency_ShouldProduceUniqueNumbers() throws InterruptedException {
        // Given
        int threadCount = 10;
        int bulkSizePerThread = 5;
        int expectedTotalOperations = threadCount * bulkSizePerThread;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        List<String> allGeneratedNumbers = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch latch = new CountDownLatch(threadCount);

        // When - Execute concurrent bulk order number generation
        IntStream.range(0, threadCount).forEach(i -> {
            executor.submit(() -> {
                try {
                    List<String> bulkNumbers = orderNumberService.generateOrderNumbers(bulkSizePerThread);
                    allGeneratedNumbers.addAll(bulkNumbers);
                } finally {
                    latch.countDown();
                }
            });
        });

        // Wait for all threads to complete
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();

        // Then - Verify results
        assertThat(completed).isTrue();
        assertThat(allGeneratedNumbers).hasSize(expectedTotalOperations);
        
        // Verify all numbers are unique
        Set<String> uniqueNumbers = Set.copyOf(allGeneratedNumbers);
        assertThat(uniqueNumbers)
                .hasSize(expectedTotalOperations)
                .describedAs("All generated order numbers should be unique");
    }

    @Test
    void mixedOperations_SingleAndBulkGeneration_ShouldProduceUniqueNumbers() throws InterruptedException {
        // Given
        int singleThreadCount = 5;
        int bulkThreadCount = 5;
        int operationsPerSingleThread = 8;
        int bulkSizePerThread = 4;
        int expectedTotal = (singleThreadCount * operationsPerSingleThread) + (bulkThreadCount * bulkSizePerThread);
        
        ExecutorService executor = Executors.newFixedThreadPool(singleThreadCount + bulkThreadCount);
        List<String> allGeneratedNumbers = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch latch = new CountDownLatch(singleThreadCount + bulkThreadCount);

        // Single number generation threads
        IntStream.range(0, singleThreadCount).forEach(i -> {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerSingleThread; j++) {
                        String orderNumber = orderNumberService.generateOrderNumber();
                        allGeneratedNumbers.add(orderNumber);
                    }
                } finally {
                    latch.countDown();
                }
            });
        });

        // Bulk number generation threads
        IntStream.range(0, bulkThreadCount).forEach(i -> {
            executor.submit(() -> {
                try {
                    List<String> bulkNumbers = orderNumberService.generateOrderNumbers(bulkSizePerThread);
                    allGeneratedNumbers.addAll(bulkNumbers);
                } finally {
                    latch.countDown();
                }
            });
        });

        // Wait for all threads to complete
        boolean completed = latch.await(30, TimeUnit.SECONDS);
        executor.shutdown();

        // Then - Verify results
        assertThat(completed).isTrue();
        assertThat(allGeneratedNumbers).hasSize(expectedTotal);
        
        // Verify all numbers are unique
        Set<String> uniqueNumbers = Set.copyOf(allGeneratedNumbers);
        assertThat(uniqueNumbers)
                .hasSize(expectedTotal)
                .describedAs("All generated order numbers should be unique across mixed operations");
    }

    @Test
    void sequentialOrdering_ShouldBeMonotonicallyIncreasing() {
        // Given - Generate numbers sequentially
        List<String> sequentialNumbers = new ArrayList<>();
        for (int i = 0; i < 10; i++) {
            sequentialNumbers.add(orderNumberService.generateOrderNumber());
        }

        // When - Extract numeric parts
        List<Integer> numericParts = sequentialNumbers.stream()
                .map(orderNumber -> Integer.parseInt(orderNumber.substring(orderNumber.length() - 6)))
                .toList();

        // Then - Verify sequential ordering
        for (int i = 1; i < numericParts.size(); i++) {
            assertThat(numericParts.get(i))
                    .isGreaterThan(numericParts.get(i - 1))
                    .describedAs("Sequential order numbers should be monotonically increasing");
        }
    }

    @Test
    void bulkGeneration_NumbersShouldBeSequential() {
        // Given
        int bulkSize = 10;

        // When
        List<String> bulkNumbers = orderNumberService.generateOrderNumbers(bulkSize);

        // Then - Extract numeric parts and verify they're sequential
        List<Integer> numericParts = bulkNumbers.stream()
                .map(orderNumber -> Integer.parseInt(orderNumber.substring(orderNumber.length() - 6)))
                .toList();

        for (int i = 1; i < numericParts.size(); i++) {
            assertThat(numericParts.get(i))
                    .isEqualTo(numericParts.get(i - 1) + 1)
                    .describedAs("Bulk generated numbers should be strictly sequential");
        }
    }

    @Test
    void highVolumeGeneration_ShouldMaintainPerformance() throws InterruptedException {
        // Given
        int threadCount = 50;
        int operationsPerThread = 20;
        long maxTimeoutSeconds = 60;
        
        ExecutorService executor = Executors.newFixedThreadPool(threadCount);
        List<String> allGeneratedNumbers = Collections.synchronizedList(new ArrayList<>());
        CountDownLatch latch = new CountDownLatch(threadCount);
        long startTime = System.currentTimeMillis();

        // When - High volume concurrent generation
        IntStream.range(0, threadCount).forEach(i -> {
            executor.submit(() -> {
                try {
                    for (int j = 0; j < operationsPerThread; j++) {
                        String orderNumber = orderNumberService.generateOrderNumber();
                        allGeneratedNumbers.add(orderNumber);
                    }
                } finally {
                    latch.countDown();
                }
            });
        });

        // Wait for completion with timeout
        boolean completed = latch.await(maxTimeoutSeconds, TimeUnit.SECONDS);
        long endTime = System.currentTimeMillis();
        executor.shutdown();

        // Then
        assertThat(completed).isTrue();
        assertThat(allGeneratedNumbers).hasSize(threadCount * operationsPerThread);
        
        // Performance assertion - should complete within reasonable time
        long elapsedSeconds = (endTime - startTime) / 1000;
        assertThat(elapsedSeconds)
                .isLessThan(maxTimeoutSeconds)
                .describedAs("High volume generation should complete within %d seconds", maxTimeoutSeconds);
        
        // Uniqueness verification
        Set<String> uniqueNumbers = Set.copyOf(allGeneratedNumbers);
        assertThat(uniqueNumbers).hasSize(allGeneratedNumbers.size());
    }
}