package com.ipms.billing.service.impl;

import com.ipms.billing.client.*;
import com.ipms.billing.dto.*;
import com.ipms.billing.enums.BillingOrderStatus;
import com.ipms.billing.enums.CoreBusinessBusinessUnit;
import com.ipms.billing.exception.BillingOrderCancelException;
import com.ipms.billing.exception.BillingOrderNotFoundException;
import com.ipms.billing.mapper.BillingOrderMapper;
import com.ipms.billing.model.BillingAccount;
import com.ipms.billing.model.BillingOrder;
import com.ipms.billing.model.BillingOrderDetail;
import com.ipms.billing.model.Expense;
import com.ipms.billing.repository.BillingOrderRepository;
import com.ipms.billing.service.BillingAccountService;
import com.ipms.billing.service.DisbursementService;
import com.ipms.billing.service.ExpenseService;
import com.ipms.billing.service.TimesheetService;
import com.ipms.config.kafka.service.ProducerService;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.ArgumentCaptor;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.mockito.junit.jupiter.MockitoExtension;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class BillingOrderServiceImplTest {

    @InjectMocks
    private BillingOrderServiceImpl service;

    @Mock
    private BillingOrderRepository repository;

    @Mock
    private BillingOrderMapper mapper;

    @Mock
    private ParameterClient parameterClient;

    @Mock
    private ProducerService producerService;

    @Mock
    private BillingAccountService billingAccountService;

    @Mock
    private MatterClient matterClient;

    @Mock
    private FirmClient firmClient;

    @Mock
    private ParamCommandClient paramCommandClient;

    @Mock
    private ActivityClient activityClient;

    @Mock
    private ExpenseService expenseService;

    @Mock
    private TimesheetService timesheetService;

    @Mock
    private DisbursementService disbursementService;

    @Mock
    private IntegrationClient integrationClient;

    @Test
    void whenGetById_thenReturnBillingOrderDto() {
        // Arrange
        Long id = 1L;
        BillingOrderDto dto = new BillingOrderDto();
        BillingOrder billingOrder = mock(BillingOrder.class);

        when(repository.findById(id)).thenReturn(Optional.of(billingOrder));
        when(mapper.toBillingOrderDto(billingOrder)).thenReturn(dto);

        // Act
        BillingOrderDto actualDto = service.getById(id);

        // Assert
        verify(repository, times(1)).findById(id);
        assertThat(actualDto).isEqualTo(dto);
    }

    @Test
    void whenSave_thenReturnBillingOrderDto() {
        // Arrange
        BillingOrderDto dto = new BillingOrderDto();
        BillingOrder billingOrder = mock(BillingOrder.class);

        when(mapper.toBillingOrder(dto)).thenReturn(billingOrder);
        when(repository.save(billingOrder)).thenReturn(billingOrder);
        when(mapper.toBillingOrderDto(billingOrder)).thenReturn(dto);
        mockParameterClient();
        when(repository.findFirstByOrderByIdDesc()).thenReturn(Optional.empty());

        // Act
        BillingOrderDto savedDto = service.save(dto);

        // Assert
        verify(mapper, times(1)).toBillingOrder(dto);
        verify(repository, times(1)).save(billingOrder);

        assertThat(savedDto).isEqualTo(dto);
    }

    private void mockParameterClient() {
        var parameterResponse = ParameterResponse.builder()
                .payload(ParameterResponse.Payload.builder().value("DRS").build())
                .build();
        when(parameterClient.getByKey("ORDER_NUMBER_PREFIX")).thenReturn(parameterResponse);
    }

    @Test
    void whenDelete_thenReturnBaseResponse() {
        Long id = 1L;

        var billingOrder = BillingOrder.builder()
                .id(id)
                .orderDetails(List.of())
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(billingOrder));

        ArgumentCaptor<BillingOrder> captor = ArgumentCaptor.forClass(BillingOrder.class);

        service.delete(id);

        verify(repository).findById(id);
        verify(repository).save(captor.capture());
        assertThat(captor.getValue().isDeleted()).isTrue();
    }

    @Test
    void whenGetByIdIn_thenReturnBillingOrderDtoList() {
        var billingOrder1 = BillingOrder.builder()
                .build();
        var billingOrder2 = BillingOrder.builder()
                .build();
        when(repository.findByIdIn(anyList()))
                .thenReturn(List.of(billingOrder1, billingOrder2));

        var billingOrderDtos = service.getByIdIn(List.of(1L, 2L));

        assertThat(billingOrderDtos).hasSize(2);
    }

    @Test
    void whenUpdateExternalOrderNumber_thenReturnNothing() {
        var billingIntegrationUpdateEvent = BillingIntegrationUpdateEvent.builder()
                .id(1L)
                .externalOrderNumber("externalOrderNumber-1")
                .build();
        var billingOrder = BillingOrder.builder()
                .createdBy("test")
                .build();
        when(repository.findById(anyLong())).thenReturn(Optional.of(billingOrder));

        service.updateExternalOrderNumber(billingIntegrationUpdateEvent);

        assertThat(billingOrder.getExternalOrderNumber()).isEqualTo("externalOrderNumber-1");

    }

    @Test
    void whenProcessInvoiceFetchedEvent_thenReturnNothing() {
        var invoiceLine1 = InvoiceFetchedEvent.InvoiceLine.builder()
                .billingId("1")
                .priceAmount("134,9718")
                .taxRate("2,45")
                .build();
        var invoiceLine2 = InvoiceFetchedEvent.InvoiceLine.builder()
                .billingId("2")
                .priceAmount("78,7766")
                .build();
        var invoice1 = InvoiceFetchedEvent.Invoice.builder()
                .erpOrderNumber("1")
                .customerorSupplierNumber("2")
                .erpDocumentNo("3")
                .invoiceDate("2024-11-26")
                .invoiceLines(List.of(invoiceLine1, invoiceLine2))
                .build();
        var invoice2 = InvoiceFetchedEvent.Invoice.builder()
                .erpOrderNumber("4")
                .customerorSupplierNumber("5")
                .erpDocumentNo("6")
                .invoiceDate("2024-11-27")
                .build();
        var invoice3 = InvoiceFetchedEvent.Invoice.builder()
                .erpOrderNumber("5")
                .customerorSupplierNumber("6")
                .erpDocumentNo("6")
                .invoiceDate("2024-11-27")
                .invoiceLines(List.of())
                .build();
        var invoiceFetchedEvent = InvoiceFetchedEvent.builder()
                .invoices(List.of(invoice1, invoice2, invoice3))
                .build();

        var orderDetail1 = BillingOrderDetail.builder()
                .id(1L)
                .expenses(List.of(1L, 2L))
                .build();
        var orderDetail2 = BillingOrderDetail.builder()
                .id(2L)
                .expenses(List.of(3L, 4L))
                .build();
        var orderDetail3 = BillingOrderDetail.builder()
                .id(3L)
                .expenses(List.of(5L, 6L))
                .build();

        var billingOrder1 = BillingOrder.builder()
                .orderNumber("orderNumber-1")
                .orderDetails(List.of(orderDetail1, orderDetail2))
                .build();
        var billingOrder2 = BillingOrder.builder()
                .orderNumber("orderNumber-2")
                .status(BillingOrderStatus.ORDER_INTEGRATED)
                .invoiceDate(LocalDate.parse("2024-11-20"))
                .invoiceNumber("12")
                .orderDetails(List.of(orderDetail3))
                .build();
        var billingOrder3 = BillingOrder.builder()
                .orderNumber("orderNumber-3")
                .build();

        when(repository.findByExternalOrderNumberAndBillingAccountNo(
                invoice1.getErpOrderNumber(), invoice1.getCustomerorSupplierNumber())
        ).thenReturn(Optional.of(billingOrder1));
        when(repository.findByExternalOrderNumberAndBillingAccountNo(
                invoice2.getErpOrderNumber(), invoice2.getCustomerorSupplierNumber())
        ).thenReturn(Optional.of(billingOrder2));
        when(repository.findByExternalOrderNumberAndBillingAccountNo(
                invoice3.getErpOrderNumber(), invoice3.getCustomerorSupplierNumber())
        ).thenReturn(Optional.of(billingOrder3));
        when(expenseService.isInvoiceCompleted(billingOrder1.getOrderNumber())).thenReturn(true);
        when(expenseService.isInvoiceCompleted(billingOrder3.getOrderNumber())).thenReturn(false);

        invoiceFetchedEvent.getInvoices().forEach(invoice -> service.processInvoiceFetchedEvent(invoice));

        assertThat(billingOrder1.getInvoiceNumber()).isEqualTo("3");
        assertThat(billingOrder3.getInvoiceNumber()).isNull();
        assertThat(billingOrder1.getInvoiceDate()).isEqualTo(LocalDate.parse("2024-11-26"));
        assertThat(billingOrder1.getStatus()).isEqualTo(BillingOrderStatus.INVOICE_IS_READY);
        assertThat(invoiceLine1.getExpenseIds()).isEqualTo(List.of(1L, 2L));
        assertThat(invoiceLine2.getExpenseIds()).isEqualTo(List.of(3L, 4L));
        assertThat(billingOrder2.getInvoiceNumber()).isEqualTo("12");
        assertThat(billingOrder2.getInvoiceDate()).isEqualTo(LocalDate.parse("2024-11-20"));
        assertThat(billingOrder2.getStatus()).isEqualTo(BillingOrderStatus.ORDER_INTEGRATED);
    }

    @Test
    void whenProcessBillingApprovedEvent_withCreateAutomaticallyFalse_thenReturnsNothing() {
        var billingApprovedEvent = BillingApprovedEvent.builder()
                .billingAccountId(1L)
                .matterId(1L)
                .build();
        var billingAccount = BillingAccount.builder()
                .createOrderAutomatically(false)
                .build();
        when(billingAccountService.getBillingAccountById(1L)).thenReturn(billingAccount);
        var matterDto = MatterDto.builder()
                .id(1L)
                .paralegal("test")
                .build();
        var matterResponse = MatterResponse.builder()
                .payload(matterDto)
                .build();
        when(matterClient.getById(Mockito.anyLong())).thenReturn(matterResponse);

        service.processBillingApprovedEvent(billingApprovedEvent);

        verify(repository, never()).save(any());
    }

    @Test
    void whenProcessBillingApprovedEvent_withCreateAutomaticallyTrue_thenReturnsNothing() {
        var billingApprovedEvent = BillingApprovedEvent.builder()
                .billingAccountId(1L)
                .matterId(1L)
                .billingPeriodEnds("2024-12-19")
                .build();
        var billingAccount = BillingAccount.builder()
                .createOrderAutomatically(true)
                .build();
        when(billingAccountService.getBillingAccountById(1L)).thenReturn(billingAccount);
        var matterResponse = MatterResponse.builder()
                .payload(MatterDto.builder()
                        .firms(List.of(1L, 2L))
                        .coreBusiness(CoreBusinessBusinessUnit.PARALLEL_IMPORT.name())
                        .build())
                .build();
        when(matterClient.getById(anyLong())).thenReturn(matterResponse);
        var firmResponse = FirmResponse.builder()
                .payload(FirmResponse.Payload.builder()
                        .title("test firm")
                        .build())
                .build();
        when(firmClient.getById(anyLong())).thenReturn(firmResponse);
        when(paramCommandClient.isUnitPriceDefined(anyString(), anyString()))
                .thenReturn(BooleanResponse.builder().payload(true).build());
        when(repository.save(any())).thenReturn(BillingOrder.builder().build());
        var expenseSummary1 = ExpenseSummaryDto.builder()
                .expenseCode("expense-code-1")
                .currency("TL")
                .unitPrice(BigDecimal.TEN)
                .build();
        var expenseSummary2 = ExpenseSummaryDto.builder()
                .expenseCode("expense-code-2")
                .currency("USD")
                .unitPrice(BigDecimal.ONE)
                .build();
        when(expenseService.processOrderCreate(any())).thenReturn(List.of(expenseSummary1, expenseSummary2));
        mockParameterClient();
        var activityResponse = ActivityListResponse.Payload.builder()
                .build();
        var activityListResponse = ActivityListResponse.builder()
                .payload(List.of(activityResponse))
                .build();
        when(activityClient.getActivitiesByExpenses(any())).thenReturn(activityListResponse);

        service.processBillingApprovedEvent(billingApprovedEvent);

        verify(repository).save(any());
    }

    @Test
    void whenProcessOrderIntegrationErrorEvent_thenThrowBillingOrderNotFoundException() {
        assertThatThrownBy(() -> service.processOrderIntegrationErrorEvent("123"))
                .isInstanceOf(BillingOrderNotFoundException.class);
    }

    @Test
    void whenProcessOrderIntegrationErrorEvent_thenUpdateStatus() {
        var orderNumber = "123";
        var billingOrder = BillingOrder.builder().build();
        when(repository.findByOrderNumber(orderNumber)).thenReturn(Optional.of(billingOrder));

        service.processOrderIntegrationErrorEvent(orderNumber);

        assertThat(billingOrder.getStatus()).isEqualTo(BillingOrderStatus.ERROR_IN_ORDER);
    }

    @Test
    void whenProcessOrderIntegrationSuccessEvent_thenThrowBillingOrderNotFoundException() {
        assertThatThrownBy(() -> service.processOrderIntegrationSuccessEvent("123"))
                .isInstanceOf(BillingOrderNotFoundException.class);
    }

    @Test
    void whenProcessOrderIntegrationSuccessEvent_thenUpdateStatus() {
        var orderNumber = "123";
        var billingOrder = BillingOrder.builder().build();
        when(repository.findByOrderNumber(orderNumber)).thenReturn(Optional.of(billingOrder));

        service.processOrderIntegrationSuccessEvent(orderNumber);

        assertThat(billingOrder.getStatus()).isEqualTo(BillingOrderStatus.ORDER_INTEGRATED);
    }

    @Test
    void whenCancel_thenCancelBillingOrderAndSendMessage() {
        Long id = 1L;
        var billingOrder = BillingOrder.builder()
                .id(id)
                .status(null)
                .orderNumber("123")
                .orderDetails(List.of())
                .build();
         var expense = Expense.builder()
                .id(1L)
                .timesheets(List.of(2L))
                .disbursements(List.of(3L))
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(billingOrder));
        when(expenseService.getExpenseIdsByOrderNo(billingOrder.getOrderNumber())).thenReturn(List.of(expense.getId()));
        var activity = ActivityListResponse.Payload.builder()
                .id(1L)
                .billingPeriodEnds(LocalDate.parse("2020-01-01"))
                .billingStatus(BillingOrderStatus.ORDER_INTEGRATED.name())
                .build();
        var activityListResponse = ActivityListResponse.builder()
                .payload(List.of(activity))
                .build();
        when(activityClient.getActivitiesByExpenses(List.of(expense.getId()))).thenReturn(activityListResponse);
        when(expenseService.getByActivityId(activity.getId())).thenReturn(List.of(ExpenseDto.builder().id(expense.getId()).build()));
        when(expenseService.getExpensesByIdsAndOrderNo(List.of(expense.getId()), billingOrder.getOrderNumber()))
                .thenReturn(List.of(expense));
        mockTimesheetServiceHasUnapproved();

        service.cancel(id);

        verify(repository).save(billingOrder);
        verify(activityClient).cancelBillingOrder(1L);
        verify(expenseService).deleteExpensesRelatedWithTimesheetAndDisbursement(List.of(expense));
        verify(timesheetService).cancelBillingOrder(List.of(2L));
        verify(disbursementService).cancelBillingOrder(List.of(3L));
        verify(producerService).send(any(), anyString());
    }

    @Test
    void whenCancel_withUnapprovedRecords_thenDeleteExpenses() {
        Long id = 1L;
        var billingOrder = BillingOrder.builder()
                .id(id)
                .status(null)
                .orderNumber("123")
                .orderDetails(List.of())
                .build();
        var expense = Expense.builder()
                .id(1L)
                .timesheets(List.of(2L))
                .disbursements(List.of(3L))
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(billingOrder));
        when(expenseService.getExpenseIdsByOrderNo(billingOrder.getOrderNumber())).thenReturn(List.of(expense.getId()));
        var activity = ActivityListResponse.Payload.builder()
                .id(1L)
                .billingPeriodEnds(LocalDate.parse("2020-01-01"))
                .billingStatus(BillingOrderStatus.ORDER_INTEGRATED.name())
                .build();
        var activityListResponse = ActivityListResponse.builder()
                .payload(List.of(activity))
                .build();
        when(activityClient.getActivitiesByExpenses(List.of(expense.getId()))).thenReturn(activityListResponse);
        when(expenseService.getByActivityId(activity.getId())).thenReturn(List.of(ExpenseDto.builder().id(expense.getId()).build()));
        when(expenseService.getExpensesByIdsAndOrderNo(List.of(expense.getId()), billingOrder.getOrderNumber()))
                .thenReturn(List.of(expense));
        mockTimesheetServiceHasUnapproved();
        ArgumentCaptor<BillingOrder> captor = ArgumentCaptor.forClass(BillingOrder.class);

        service.cancel(id);

        verify(activityClient).cancelBillingOrder(1L);
        verify(expenseService).deleteExpensesRelatedWithTimesheetAndDisbursement(List.of(expense));
        verify(timesheetService).cancelBillingOrder(List.of(2L));
        verify(disbursementService).cancelBillingOrder(List.of(3L));
        verify(producerService).send(any(), anyString());
        verify(repository, times(1)).save(captor.capture());
        assertThat(captor.getValue().isDeleted()).isTrue();
    }


    @Test
    void whenCancel_withNoExpenses_thenSkipActivityProcessing() {
        Long id = 1L;
        var billingOrder = BillingOrder.builder()
                .id(id)
                .status(BillingOrderStatus.ORDER_INTEGRATED)
                .orderNumber("123")
                .orderDetails(List.of())
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(billingOrder));
        when(expenseService.getExpenseIdsByOrderNo(billingOrder.getOrderNumber())).thenReturn(List.of());

        ArgumentCaptor<BillingOrder> captor = ArgumentCaptor.forClass(BillingOrder.class);

        service.cancel(id);

        verify(repository).save(billingOrder);
        verify(activityClient, never()).getActivitiesByExpenses(anyList());
        verify(activityClient, never()).cancelBillingOrder(anyLong());
        verify(expenseService, never()).deleteExpensesRelatedWithTimesheetAndDisbursement(anyList());
        verify(timesheetService, never()).cancelBillingOrder(anyList());
        verify(disbursementService, never()).cancelBillingOrder(anyList());
        verify(producerService).send(any(), anyString());
        verify(repository, times(1)).save(captor.capture());
        assertThat(captor.getValue().isDeleted()).isTrue();
    }

    @Test
    void whenCancel_withBillingOrderNotFound_thenThrowException() {
        Long id = 1L;
        when(repository.findById(id)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> service.cancel(id))
                .isInstanceOf(BillingOrderNotFoundException.class);
    }

    @Test
    void whenCancel_withOrderIntegratedStatus_andJdeNotCancelled_thenThrowBillingOrderCancelException() {
        // Arrange
        Long id = 1L;
        var billingOrder = BillingOrder.builder()
                .id(id)
                .status(BillingOrderStatus.ORDER_INTEGRATED)
                .orderNumber("123")
                .externalOrderNumber("EXT123")
                .issuerId(100L)
                .billingAccountNo("ACCOUNT001")
                .orderDetails(List.of())
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(billingOrder));
        when(integrationClient.isOrderCancelled(100L, "EXT123", "ACCOUNT001"))
                .thenReturn(BooleanResponse.builder().payload(false).build());

        // Act & Assert
        assertThatThrownBy(() -> service.cancel(id))
                .isInstanceOf(BillingOrderCancelException.class);
    }

    @Test
    void whenCancel_withInvoiceIsReadyStatus_andJdeNotCancelled_thenThrowBillingOrderCancelException() {
        // Arrange
        Long id = 1L;
        var billingOrder = BillingOrder.builder()
                .id(id)
                .status(BillingOrderStatus.INVOICE_IS_READY)
                .orderNumber("123")
                .externalOrderNumber("EXT123")
                .issuerId(100L)
                .billingAccountNo("ACCOUNT001")
                .orderDetails(List.of())
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(billingOrder));
        when(integrationClient.isOrderCancelled(100L, "EXT123", "ACCOUNT001"))
                .thenReturn(BooleanResponse.builder().payload(false).build());

        // Act & Assert
        assertThatThrownBy(() -> service.cancel(id))
                .isInstanceOf(BillingOrderCancelException.class);
    }

    @Test
    void whenCancel_withOrderIntegratedStatus_andJdeCancelled_thenCancelSuccessfully() {
        // Arrange
        Long id = 1L;
        var billingOrder = BillingOrder.builder()
                .id(id)
                .status(BillingOrderStatus.ORDER_INTEGRATED)
                .orderNumber("123")
                .externalOrderNumber("EXT123")
                .issuerId(100L)
                .billingAccountNo("ACCOUNT001")
                .orderDetails(List.of())
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(billingOrder));
        when(expenseService.getExpenseIdsByOrderNo(billingOrder.getOrderNumber())).thenReturn(List.of());
        when(integrationClient.isOrderCancelled(100L, "EXT123", "ACCOUNT001"))
                .thenReturn(BooleanResponse.builder().payload(true).build());

        ArgumentCaptor<BillingOrder> captor = ArgumentCaptor.forClass(BillingOrder.class);

        // Act
        service.cancel(id);

        // Assert
        verify(integrationClient).isOrderCancelled(100L, "EXT123", "ACCOUNT001");
        verify(repository, times(1)).save(captor.capture());
        assertThat(captor.getValue().isDeleted()).isTrue();
        verify(producerService).send(any(), anyString());
    }

    @Test
    void whenCancel_withOtherStatus_thenSkipJdeValidation() {
        // Arrange
        Long id = 1L;
        var billingOrder = BillingOrder.builder()
                .id(id)
                .status(BillingOrderStatus.ERROR_IN_ORDER)
                .orderNumber("123")
                .externalOrderNumber("EXT123")
                .issuerId(100L)
                .billingAccountNo("ACCOUNT001")
                .orderDetails(List.of())
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(billingOrder));
        when(expenseService.getExpenseIdsByOrderNo(billingOrder.getOrderNumber())).thenReturn(List.of());

        ArgumentCaptor<BillingOrder> captor = ArgumentCaptor.forClass(BillingOrder.class);

        // Act
        service.cancel(id);

        // Assert
        verify(integrationClient, never()).isOrderCancelled(anyLong(), anyString(), anyString());
        verify(repository, times(1)).save(captor.capture());
        assertThat(captor.getValue().isDeleted()).isTrue();
        verify(producerService).send(any(), anyString());
    }

    @Test
    void whenCancel_withOrderIntegratedStatus_andNoExternalOrderNumber_thenSkipJdeValidation() {
        // Arrange
        Long id = 1L;
        var billingOrder = BillingOrder.builder()
                .id(id)
                .status(BillingOrderStatus.ORDER_INTEGRATED)
                .orderNumber("123")
                .externalOrderNumber(null) // External order number yok
                .issuerId(100L)
                .billingAccountNo("ACCOUNT001")
                .orderDetails(List.of())
                .build();

        when(repository.findById(id)).thenReturn(Optional.of(billingOrder));
        when(expenseService.getExpenseIdsByOrderNo(billingOrder.getOrderNumber())).thenReturn(List.of());

        ArgumentCaptor<BillingOrder> captor = ArgumentCaptor.forClass(BillingOrder.class);

        // Act
        service.cancel(id);

        // Assert
        verify(integrationClient, never()).isOrderCancelled(anyLong(), anyString(), anyString());
        verify(repository, times(1)).save(captor.capture());
        assertThat(captor.getValue().isDeleted()).isTrue();
        verify(producerService).send(any(), anyString());
    }

    private void mockTimesheetServiceHasUnapproved() {
        when(timesheetService.hasUnapproved(Mockito.anyLong(), Mockito.any(LocalDate.class)))
                .thenReturn(true);
    }
}
