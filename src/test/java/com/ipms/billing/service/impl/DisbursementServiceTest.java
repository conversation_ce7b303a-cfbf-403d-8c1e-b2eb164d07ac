package com.ipms.billing.service.impl;


import com.ipms.billing.client.ActivityClient;
import com.ipms.billing.client.ParamCommandClient;
import com.ipms.billing.client.ParameterClient;
import com.ipms.billing.dto.*;
import com.ipms.billing.enums.DisbursementType;
import com.ipms.billing.enums.ToBeBilledChoice;
import com.ipms.billing.exception.DisbursementBillingApproveException;
import com.ipms.billing.exception.DisbursementNotFoundException;
import com.ipms.billing.mapper.DisbursementMapper;
import com.ipms.billing.mapper.DisbursementMapperImpl;
import com.ipms.billing.model.BillingAccount;
import com.ipms.billing.model.Disbursement;
import com.ipms.billing.repository.DisbursementRepository;
import com.ipms.billing.service.BillingAccountService;
import com.ipms.billing.service.ExpenseService;
import com.ipms.billing.specification.DisbursementToBeBilledSpecification;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.config.storage.service.StorageService;
import com.ipms.core.common.model.TransferEvent;
import org.assertj.core.api.Assertions;
import org.assertj.core.api.AssertionsForClassTypes;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.jupiter.api.extension.ExtendWith;
import org.mockito.*;
import org.mockito.junit.jupiter.MockitoExtension;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageImpl;
import org.springframework.data.domain.Pageable;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.*;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.assertj.core.api.AssertionsForClassTypes.assertThatCode;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;
import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.*;

@ExtendWith(MockitoExtension.class)
class DisbursementServiceTest {

    @InjectMocks
    private DisbursementServiceImpl service;

    @Mock
    private DisbursementRepository repository;

    @Mock
    private ActivityClient activityClient;

    @Mock
    private StorageService storageService;

    @Mock
    private ProducerService producerService;

    @Mock
    private ExpenseService expenseService;

    @Mock
    private ParamCommandClient paramCommandClient;

    @Mock
    private ParameterClient parameterClient;

    @Mock
    private BillingAccountService billingAccountService;

    @Spy
    private final DisbursementMapper mapper = new DisbursementMapperImpl();

    @Captor
    private ArgumentCaptor<List<Disbursement>> disbursementListCaptor;

    private final String DEFAULT_CURRENCY_PARAM = "DEFAULT_CURRENCY";
    private final String AUTO_BILLING_AMOUNT_PARAM = "AUTO_BILLING_AMOUNT";

    @BeforeEach
    void setUp() {
        ReflectionTestUtils.setField(service, "defaultCurrencyParam",
                DEFAULT_CURRENCY_PARAM);
        ReflectionTestUtils.setField(service, "autoBillingAmountParam",
                AUTO_BILLING_AMOUNT_PARAM);
    }

    @Test
    void whenSave_thenReturnDisbursementDto() {
        var disbursement = Disbursement.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType(DisbursementType.CALLING_USA)
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .build();
        var disbursementDto = DisbursementDto.builder()
                .id(1L)
                .activityId(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType("CALLING_USA")
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .build();
        when(repository.save(Mockito.any(Disbursement.class)))
                .thenReturn(disbursement);
        when(activityClient.addDisbursement(Mockito.anyLong(), Mockito.anyLong()))
                .thenReturn(ActivityResponse.builder().code(200).build());
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        assertThat(service.save(disbursementDto).getId())
                .isEqualTo(disbursement.getId());
    }

    @Test
    void givenIdAndPageAndSize_whenGetByIds_thenReturnDisbursementPageDto() {
        var disbursement = Disbursement.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType(DisbursementType.CALLING_USA)
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .build();
        Page<Disbursement> page = new PageImpl<>(List.of(disbursement), Pageable.ofSize(1), 1);
        when(repository.findByIdInOrderByDisbursementDateDesc(Mockito.anyList(), Mockito.any(Pageable.class)))
                .thenReturn(page);
        AssertionsForClassTypes.assertThat(service.getByIds(List.of(1L,2L), 2, 3).getTotalElements())
                .isEqualTo(1L);
    }

    @Test
    void whenGetByIdIn_thenReturnDocumentDtoList() {
        var disbursement = Disbursement.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType(DisbursementType.CALLING_USA)
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(disbursement));
        assertThat(service.getByIdIn(List.of(1L))).isNotEmpty();
    }

    @Test
    void whenDelete_thenDoesNotThrowAnyException() {
        var disbursement = Disbursement.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType(DisbursementType.CALLING_USA)
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .build();
        when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(disbursement));
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        assertThatCode(() -> service.delete(1L, 0L)).doesNotThrowAnyException();
    }

    @Test
    void whenDelete_thenThrowDisbursementNotFoundException() {
        when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.empty());
        Throwable thrown = catchThrowable(()->service.delete(1L, 0L));
        assertThat(thrown).isInstanceOf(DisbursementNotFoundException.class);
    }

    @Test
    void whenApproveBillingBulk_thenReturnDisbursementDtoList() {
        var disbursement1 = Disbursement.builder().id(1L).build();
        var disbursement2 = Disbursement.builder().id(2L).build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(disbursement1, disbursement2));
        when(repository.saveAll(Mockito.anyList()))
                .thenReturn(List.of(disbursement1, disbursement2));
        Assertions.assertThat(service.approveBillingBulk(List.of(1L, 2L)))
                .isNotEmpty();
        Assertions.assertThat(disbursement1.getIsBillingApproved()).isTrue();
        Assertions.assertThat(disbursement2.getIsBillingApproved()).isTrue();
    }

    @Test
    void whenCancelApproveBillingBulk_thenReturnDisbursementDtoList() {
        var disbursement1 = Disbursement.builder().id(1L).build();
        var disbursement2 = Disbursement.builder().id(2L).build();
        when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(disbursement1, disbursement2));
        when(repository.saveAll(Mockito.anyList()))
                .thenReturn(List.of(disbursement1, disbursement2));
        Assertions.assertThat(service.cancelApprovelBillingBulk(List.of(1L, 2L)))
                .isNotEmpty();
        Assertions.assertThat(disbursement1.getIsBillingApproved()).isFalse();
        Assertions.assertThat(disbursement2.getIsBillingApproved()).isFalse();
    }

    @Test
    void whenApproveBilling_thenThrowException() {
        var disbursement1 = Disbursement.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .build();
        var disbursement2 = Disbursement.builder()
                .id(2L)
                .disbursementDate(LocalDate.now())
                .toBeBilled(ToBeBilledChoice.YES)
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(ActivityResponse.Payload.builder()
                        .billingAccountId(1L)
                        .billingPeriodEnds(LocalDate.now())
                        .build())
                .build();
        when(activityClient.getActivity(Mockito.anyLong())).thenReturn(activityResponse);
        when(repository.findByActivityId(Mockito.anyLong())).thenReturn(List.of(disbursement1, disbursement2));
        when(repository.findAll(Mockito.any(DisbursementToBeBilledSpecification.class))).thenReturn(List.of(disbursement1, disbursement2));
        when(billingAccountService.getBillingAccountById(Mockito.anyLong())).thenReturn(new BillingAccount());
        var approveBillingRequest = ApproveBillingRequest.builder()
                .activityId(1L)
                .build();

        assertThatThrownBy(() -> service.approveBilling(approveBillingRequest))
                .isInstanceOf(DisbursementBillingApproveException.class);
    }

    @Test
    void whenApproveBilling_thenBaseResponse() {
        var billingPeriodEnds = LocalDate.parse("2024-07-18");
        var disbursement1 = Disbursement.builder()
                .id(1L)
                .disbursementDate(LocalDate.parse("2024-07-19"))
                .expenseCode("x")
                .amount(BigDecimal.TEN)
                .toBeBilled(ToBeBilledChoice.YES)
                .isBillingApproved(true)
                .currency("TL")
                .build();
        var disbursement2 = Disbursement.builder()
                .id(2L)
                .disbursementDate(LocalDate.parse("2024-07-18"))
                .expenseCode("x")
                .amount(BigDecimal.TEN)
                .toBeBilled(ToBeBilledChoice.YES)
                .isBillingApproved(true)
                .currency("TL")
                .build();
        var activityResponse = ActivityResponse.builder()
                .payload(ActivityResponse.Payload.builder()
                        .billingAccountId(1L)
                        .billingPeriodEnds(billingPeriodEnds)
                        .build())
                .build();
        when(activityClient.getActivity(Mockito.anyLong())).thenReturn(activityResponse);
        when(repository.findByActivityId(Mockito.anyLong())).thenReturn(List.of(disbursement1, disbursement2));
        when(repository.findAll(Mockito.any(DisbursementToBeBilledSpecification.class))).thenReturn(List.of(disbursement1, disbursement2));
        var billingAccount = BillingAccount.builder().currency("USD").build();
        when(billingAccountService.getBillingAccountById(Mockito.anyLong())).thenReturn(billingAccount);

        when(expenseService.save(Mockito.any(ExpenseDto.class))).thenReturn(ExpenseDto.builder().id(1L).build());
        var exchangeRateResponseDto = ExchangeRateResponse.ExchangeRateResponseDto.builder()
                .exchangeRate(BigDecimal.ONE)
                .build();
        when(paramCommandClient.getLatestExchangeRate(Mockito.anyString(), Mockito.anyString(), Mockito.any(LocalDate.class)))
                .thenReturn(ExchangeRateResponse.builder().payload(exchangeRateResponseDto).build());
        var approveBillingRequest = ApproveBillingRequest.builder()
                .activityId(1L)
                .build();

        service.approveBilling(approveBillingRequest);

        assertThat(disbursement2.getExpenseDate()).isEqualTo(billingPeriodEnds);
    }

    @Test
    void whenGetToBeBilledDisbursements_thenReturnDisbursementPageDto() {
        var disbursement = Disbursement.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType(DisbursementType.CALLING_USA)
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .build();
        Page<Disbursement> page = new PageImpl<>(List.of(disbursement), Pageable.ofSize(1), 1);
        when(repository.findAll(Mockito.any(DisbursementToBeBilledSpecification.class), Mockito.any(Pageable.class)))
                .thenReturn(page);
        assertThat(service.getToBeBilledDisbursements(DisbursementFilterRequest.builder().build(), 2, 3).getTotalElements())
                .isEqualTo(1L);
    }

    @Test
    void whenGetBilledDisbursements_thenReturnDisbursementPageDto() {
        var disbursement = Disbursement.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType(DisbursementType.CALLING_USA)
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .build();
        Page<Disbursement> page = new PageImpl<>(List.of(disbursement), Pageable.ofSize(1), 1);
        when(repository.findByIdInAndExpenseDateBeforeOrderByDisbursementDateDesc(Mockito.anyList(), Mockito.any(LocalDate.class), Mockito.any(Pageable.class)))
                .thenReturn(page);
        var disbursementFilterRequest = DisbursementFilterRequest.builder()
                .ids(List.of(1L))
                .billingPeriodEnds(LocalDate.now())
                .build();

        assertThat(service.getBilledDisbursements(disbursementFilterRequest, 2, 3).getTotalElements())
                .isEqualTo(1L);
    }

    @Test
    void whenIsBillable_thenReturnTrue1() {
        var disbursement = Disbursement.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType(DisbursementType.CALLING_USA)
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .toBeBilled(ToBeBilledChoice.YES)
                .build();
        when(repository.findByIdInAndExpenseDateIsNullAndToBeBilled(List.of(1L), ToBeBilledChoice.YES)).thenReturn(List.of(disbursement));

        assertThat(service.hasBillable(List.of(1L), List.of(2L))).isTrue();
    }

    @Test
    void whenIsBillable_thenReturnTrue2() {
        when(repository.findByIdInAndExpenseDateIsNullAndToBeBilled(List.of(1L), ToBeBilledChoice.YES))
                .thenReturn(List.of());
        when(expenseService.isBillable(Mockito.anyList())).thenReturn(true);

        assertThat(service.hasBillable(List.of(1L), List.of(2L))).isTrue();
    }

    @Test
    void whenUndoBillingApproval_thenDoesNotThrowAnyException() {
        var disbursement1 = Disbursement.builder()
                .id(1L)
                .build();
        var disbursement2 = Disbursement.builder()
                .id(2L)
                .build();

        when(repository.findByIdIn(Mockito.anyList())).thenReturn(List.of(disbursement1, disbursement2));

        assertThatCode(() -> service.undoBillingApproval(List.of(1L, 2L), LocalDate.now())).doesNotThrowAnyException();
    }


    @Test
    void givenApprovedDisbursements_whenUndoBillingApproval_thenClearExpenseDatesAndDeleteExpenses() {
        LocalDate billingPeriodEnds = LocalDate.of(2025, 3, 20);
        List<Long> disbursementIds = Arrays.asList(1L, 2L, 3L);

        Disbursement approvedDisbursement1 = Disbursement.builder()
                .id(1L)
                .expenseDate(billingPeriodEnds)
                .build();

        Disbursement approvedDisbursement2 = Disbursement.builder()
                .id(2L)
                .expenseDate(billingPeriodEnds)
                .build();

        Disbursement unapprovedDisbursement = Disbursement.builder()
                .id(3L)
                .expenseDate(LocalDate.of(2025, 2, 15))
                .build();

        List<Disbursement> allDisbursements = Arrays.asList(
                approvedDisbursement1,
                approvedDisbursement2,
                unapprovedDisbursement
        );

        when(repository.findByIdIn(disbursementIds)).thenReturn(allDisbursements);
        doNothing().when(expenseService).deleteByDisbursementIds(anyList());
        when(repository.saveAll(anyList())).thenReturn(Arrays.asList(approvedDisbursement1, approvedDisbursement2));

        service.undoBillingApproval(disbursementIds, billingPeriodEnds);

        verify(repository).findByIdIn(disbursementIds);
        verify(repository).saveAll(disbursementListCaptor.capture());
        verify(expenseService).deleteByDisbursementIds(anyList());
        verify(producerService, times(2)).sendTransfer(any(TransferEvent.class));

        List<Disbursement> savedDisbursements = disbursementListCaptor.getValue();

        assertThat(savedDisbursements)
                .hasSize(2)
                .allMatch(d -> d.getExpenseDate() == null)
                .extracting(Disbursement::getId)
                .containsExactlyInAnyOrder(1L, 2L);
    }

    @Test
    void whenHasBillable_thenReturnTrue() {
        var disbursement1 = Disbursement.builder()
                .toBeBilled(ToBeBilledChoice.YES)
                .build();
        when(repository.findByIdInAndExpenseDateIsNullAndToBeBilled(List.of(1L), ToBeBilledChoice.YES))
                .thenReturn(List.of(disbursement1));
        var hasBillable = service.hasBillable(List.of(1L), List.of(2L));

        assertThat(hasBillable).isTrue();
    }

    @Test
    void whenHasBillable_thenReturnFalse() {
        when(repository.findByIdInAndExpenseDateIsNullAndToBeBilled(List.of(1L), ToBeBilledChoice.YES))
                .thenReturn(List.of());
        var hasBillable = service.hasBillable(List.of(1L), List.of(2L));

        assertThat(hasBillable).isFalse();
    }

    @Test
    void whenIsTotalAmountMoreThanBillingAmountWithEmptyList_thenReturnFalse() {
        mockDefaultCurrencyParameter();
        mockBillingAmountParam();
        var moreThanBillingAmount = service.isTotalAmountMoreThanBillingAmount(List.of(), List.of());

        assertThat(moreThanBillingAmount).isFalse();
    }

    private void mockDefaultCurrencyParameter() {
        var defaultCurrencyResponse = ParameterResponse.builder()
                .payload(ParameterResponse.Payload.builder().value("USD").build())
                .build();
        when(parameterClient.getByKey(DEFAULT_CURRENCY_PARAM)).thenReturn(defaultCurrencyResponse);
    }

    @Test
    void whenIsTotalAmountMoreThanBillingAmount_thenReturnTrue() {
        var disbursement = Disbursement.builder()
                .currency("TL")
                .amount(BigDecimal.valueOf(100))
                .build();
        var exchangeRateResponse = ExchangeRateResponse.builder()
                .payload(ExchangeRateResponse.ExchangeRateResponseDto.builder()
                        .exchangeRate(BigDecimal.TEN)
                        .build())
                .build();
        when(paramCommandClient.getLatestExchangeRate(anyString(), anyString(), any(LocalDate.class)))
                .thenReturn(exchangeRateResponse);
        when(repository.findByIdInAndExpenseDateIsNullAndToBeBilled(anyList(), any(ToBeBilledChoice.class)))
                .thenReturn(List.of(disbursement));
        mockDefaultCurrencyParameter();
        mockBillingAmountParam();
        var moreThanBillingAmount = service.isTotalAmountMoreThanBillingAmount(List.of(1L), List.of(2L));

        assertThat(moreThanBillingAmount).isTrue();
    }

    private void mockBillingAmountParam() {
        var autoBillingAmountResponse = ParameterResponse.builder()
                .payload(ParameterResponse.Payload.builder().value("40").build())
                .build();
        when(parameterClient.getByKey(AUTO_BILLING_AMOUNT_PARAM)).thenReturn(autoBillingAmountResponse);
    }

    @Test
    void whenIsTotalAmountMoreThanZero_thenReturnTrue() {
        var disbursement = Disbursement.builder()
                .currency("TL")
                .amount(BigDecimal.valueOf(100))
                .build();

        when(repository.findByIdInAndExpenseDateIsNullAndToBeBilled(anyList(), any(ToBeBilledChoice.class)))
                .thenReturn(List.of(disbursement));
        var moreThanBillingAmount = service.isTotalAmountMoreThanZero(List.of(1L), List.of(2L));

        assertThat(moreThanBillingAmount).isTrue();
    }

    @Test
    void updateDisbursementsForBillingCancellation_withNullIdsList_shouldDoNothing() {
        LocalDate billingPeriodEnds = LocalDate.of(2025, 3, 20);
        List<Long> nullIds = null;

        service.updateDisbursementsForBillingCancellation(nullIds, billingPeriodEnds);

        verify(producerService, never()).sendTransfer(any());
    }

    @Test
    void updateDisbursementsForBillingCancellation_withNoMatchingDisbursements_shouldDoNothing() {
        LocalDate billingPeriodEnds = LocalDate.of(2025, 3, 20);
        List<Long> disbursementIds = Arrays.asList(1L, 2L, 3L);

        when(repository.findAll(any(DisbursementToBeBilledSpecification.class)))
                .thenReturn(Collections.emptyList());

        service.updateDisbursementsForBillingCancellation(disbursementIds, billingPeriodEnds);

        verify(repository).findAll(any(DisbursementToBeBilledSpecification.class));
        verify(producerService, never()).sendTransfer(any());
    }

    @Test
    void updateDisbursementsForBillingCancellation_withMatchingButAlreadyProcessedDisbursements_shouldDoNothing() {
        LocalDate billingPeriodEnds = LocalDate.of(2025, 3, 20);
        List<Long> disbursementIds = Arrays.asList(1L, 2L, 3L);

        var notEligible = Disbursement.builder()
                .id(2L)
                .isBillingApproved(true)
                .toBeBilled(ToBeBilledChoice.NO)
                .expenseDate(LocalDate.now().minusDays(1))
                .build();
        List<Disbursement> disbursements = List.of(notEligible);
        when(repository.findAll(any(DisbursementToBeBilledSpecification.class)))
                .thenReturn(disbursements);

        service.updateDisbursementsForBillingCancellation(disbursementIds, billingPeriodEnds);

        verify(repository).findAll(any(DisbursementToBeBilledSpecification.class));
        verify(producerService, never()).sendTransfer(any());
    }

    @Test
    void updateDisbursementsForBillingCancellation_withSomeDisbursementsToUpdate_shouldUpdateOnlyEligibleDisbursements() {
        LocalDate billingPeriodEnds = LocalDate.of(2025, 3, 20);
        List<Long> disbursementIds = Arrays.asList(1L, 2L, 3L, 4L);

        List<Disbursement> allDisbursements = new ArrayList<>();

        Disbursement eligible1 = Disbursement.builder()
                .id(1L)
                .isBillingApproved(true)
                .toBeBilled(ToBeBilledChoice.NO)
                .expenseDate(null)
                .build();
        allDisbursements.add(eligible1);

        Disbursement notEligible1 = Disbursement.builder()
                .id(2L)
                .isBillingApproved(true)
                .toBeBilled(ToBeBilledChoice.NO)
                .expenseDate(LocalDate.now().minusDays(1))
                .build();
        allDisbursements.add(notEligible1);

        Disbursement notEligible2 = Disbursement.builder()
                .id(3L)
                .isBillingApproved(true)
                .toBeBilled(ToBeBilledChoice.YES)
                .expenseDate(null)
                .build();
        allDisbursements.add(notEligible2);

        Disbursement notEligible3 = Disbursement.builder()
                .id(4L)
                .isBillingApproved(false)
                .toBeBilled(ToBeBilledChoice.NO)
                .expenseDate(null)
                .build();
        allDisbursements.add(notEligible3);

        when(repository.findAll(any(DisbursementToBeBilledSpecification.class)))
                .thenReturn(allDisbursements);

        service.updateDisbursementsForBillingCancellation(disbursementIds, billingPeriodEnds);

        verify(repository).saveAll(disbursementListCaptor.capture());
        List<Disbursement> savedDisbursements = disbursementListCaptor.getValue();

        assertThat(savedDisbursements).hasSize(1);
        assertThat(savedDisbursements.get(0).getId()).isEqualTo(1L);
        assertThat(savedDisbursements.get(0).getExpenseDate()).isNotNull();
        assertThat(savedDisbursements.get(0).getExpenseDate()).isEqualTo(LocalDate.now());

        verify(producerService, times(1)).sendTransfer(any(TransferEvent.class));
    }

    @Test
    void updateDisbursementsForBillingCancellation_withAllDisbursementsToUpdate_shouldUpdateAll() {
        LocalDate billingPeriodEnds = LocalDate.of(2025, 3, 20);
        List<Long> disbursementIds = Arrays.asList(1L, 2L);

        List<Disbursement> allEligibleDisbursements = Arrays.asList(
                Disbursement.builder()
                        .id(1L)
                        .isBillingApproved(true)
                        .toBeBilled(ToBeBilledChoice.NO)
                        .expenseDate(null)
                        .build(),
                Disbursement.builder()
                        .id(2L)
                        .isBillingApproved(true)
                        .toBeBilled(ToBeBilledChoice.NO_DEBIT_NOT_WILL_BE_SENT)
                        .expenseDate(null)
                        .build()
        );

        when(repository.findAll(any(DisbursementToBeBilledSpecification.class)))
                .thenReturn(allEligibleDisbursements);

        service.updateDisbursementsForBillingCancellation(disbursementIds, billingPeriodEnds);

        verify(repository).saveAll(disbursementListCaptor.capture());
        List<Disbursement> savedDisbursements = disbursementListCaptor.getValue();

        assertThat(savedDisbursements)
                .hasSize(2)
                .allMatch(d -> d.getExpenseDate() != null)
                .allMatch(d -> d.getExpenseDate().equals(LocalDate.now()));
        verify(producerService, times(2)).sendTransfer(any(TransferEvent.class));
    }
}
