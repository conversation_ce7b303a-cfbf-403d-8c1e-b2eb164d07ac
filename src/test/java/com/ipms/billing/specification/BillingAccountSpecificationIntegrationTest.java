package com.ipms.billing.specification;

import com.fasterxml.jackson.core.JsonProcessingException;
import com.ipms.billing.enums.BillingDay;
import com.ipms.billing.enums.RiskManagement;
import com.ipms.billing.model.BillingAccount;
import com.ipms.billing.repository.BillingAccountRepository;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.util.ArrayList;
import java.util.Arrays;
import java.util.List;

import static org.junit.jupiter.api.Assertions.*;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import=classpath:/application.yml")
@Transactional
public class BillingAccountSpecificationIntegrationTest {

    @Autowired
    private BillingAccountRepository repository;

    @Before
    public void givenBillingAccounts() throws JsonProcessingException {
        // Arrange
        var billingAccount = BillingAccount.builder()
                .accountName("accountName-1")
                .accountNo("accountNo-1")
                .issuerId(1L)
                .address("address-1")
                .discountRate(BigDecimal.ONE)
                .currency("USD")
                .riskManagement(RiskManagement.NO_RISK)
                .description("description-1")
                .issueIds(new ArrayList<>(Arrays.asList(1L, 2L, 3L)))
                .billingDay(BillingDay.DAY_1)
                .endOfBillingPeriod(BillingDay.END_OF_MONTH)
                .willBeSentToResponsible(true)
                .sendToEmails(new ArrayList<>(Arrays.asList("<EMAIL>", "<EMAIL>")))
                .copyToEmails(new ArrayList<>(Arrays.asList("<EMAIL>", "<EMAIL>")))
                .willOriginalInvoiceBeSent(true)
                .originalInvoiceRecipient("Name Surname or Department")
                .billingSystem("Tymetrix 360")
                .attachmentsToBeSent("Timespents, disbursements")
                .note("Lorem ipsum dolor sit amet")
                .build();

        repository.save(billingAccount);
    }

    @Test
    public void givenBillingAccount_whenFindByAccountName_thenReturnBillingAccount() {
        // Act
        var billingAccountSpecification = BillingAccountSpecification.builder()
                .accountNames(List.of("accountName-1"))
                .build();
        var billingAccount = repository.findOne(billingAccountSpecification).orElse(null);

        // Assert
        assertNotNull(billingAccount);
        assertEquals("accountName-1", billingAccount.getAccountName());
    }

    @Test
    public void givenBillingAccount_whenFindByAccountNo_thenReturnBillingAccount() {
        // Act
        var billingAccountSpecification = BillingAccountSpecification.builder()
                .accountNos(List.of("accountNo-1"))
                .build();
        var billingAccount = repository.findOne(billingAccountSpecification).orElse(null);

        // Assert
        assertNotNull(billingAccount);
        assertEquals("accountNo-1", billingAccount.getAccountNo());
    }

    @Test
    public void givenBillingAccount_whenFindByIssuerId_thenReturnBillingAccount() {
        // Act
        var billingAccountSpecification = BillingAccountSpecification.builder()
                .issuerIds(List.of(1L))
                .build();
        var billingAccount = repository.findOne(billingAccountSpecification).orElse(null);

        // Assert
        assertNotNull(billingAccount);
        assertEquals(1L, billingAccount.getIssuerId());
    }

    @Test
    public void givenBillingAccount_whenFindByIssueId_thenReturnBillingAccount() {
        // Act
        var billingAccountSpecification = BillingAccountSpecification.builder()
                .issueIds(List.of(1L))
                .build();
        var billingAccount = repository.findOne(billingAccountSpecification).orElse(null);

        // Assert
        assertNotNull(billingAccount);
        assertTrue(billingAccount.getIssueIds().contains(1L));
    }

    @Test
    public void givenBillingAccount_whenFindBySearchKey_thenReturnBillingAccount() {
        // Act
        var billingAccountSpecification1 = BillingAccountSpecification.builder()
                .searchKey("Name-1")
                .build();
        var billingAccountSpecification2 = BillingAccountSpecification.builder()
                .searchKey("No-1")
                .build();
        var billingAccount1 = repository.findOne(billingAccountSpecification1).orElse(null);
        var billingAccount2 = repository.findOne(billingAccountSpecification2).orElse(null);

        // Assert
        assertNotNull(billingAccount1);
        assertNotNull(billingAccount2);
        assertTrue(billingAccount1.getIssueIds().contains(1L));
        assertTrue(billingAccount2.getIssueIds().contains(1L));
    }

}