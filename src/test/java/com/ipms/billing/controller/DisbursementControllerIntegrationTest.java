package com.ipms.billing.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.billing.dto.ApproveBillingRequest;
import com.ipms.billing.dto.DisbursementDto;
import com.ipms.billing.dto.DisbursementPageDto;
import com.ipms.billing.dto.IdsRequest;
import com.ipms.billing.service.DisbursementService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.Arrays;
import java.util.List;

import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/application.yml")
public class DisbursementControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private DisbursementService service;

    ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule());

    @Test
    public void whenSave_thenReturnBaseResponse() throws Exception {
        var disbursementDto = DisbursementDto.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType("test-type")
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .user("test-user")
                .activityId(1L)
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .build();
        Mockito.when(service.save(Mockito.mock(DisbursementDto.class)))
                .thenReturn(disbursementDto);

        mvc.perform(post("/disbursement/")
                        .content(objectMapper.writeValueAsString(disbursementDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenDisbursementIdsAndPageAndSize_whenGetByIds_thenReturnBaseResponse() throws Exception {
        var disbursementDto = DisbursementDto.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType("test-type")
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .user("test-user")
                .activityId(1L)
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .build();
        var pageDto = DisbursementPageDto.builder()
                .disbursements(List.of(disbursementDto))
                .totalElements(5L)
                .totalPages(1L)
                .build();
        Mockito.when(service.getByIds(Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(pageDto);
        mvc.perform(get("/disbursement/1,2/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong(), Mockito.anyLong());
        mvc.perform(delete("/disbursement/5/0")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByIds_thenReturnBaseResponse() throws Exception {
        var disbursementDto = DisbursementDto.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType("test-type")
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .user("test-user")
                .activityId(1L)
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .build();

        Mockito.when(service.getByIdIn(Mockito.anyList()))
                .thenReturn(List.of(disbursementDto));

        mvc.perform(get("/disbursement/ids/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGivenIdsRequest_thenApproveBillingBulk() throws Exception {
        var ids = List.of(1L, 2L);
        var idsRequest = IdsRequest.builder()
                .ids(ids)
                .build();
        Mockito.when(service.approveBillingBulk(ids))
                .thenReturn(List.of(new DisbursementDto(), new DisbursementDto()));

        mvc.perform(put("/disbursement/billing-approve/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(idsRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGivenIdsRequest_thenCancelApproveBillingBulk() throws Exception {
        var ids = List.of(1L, 2L);
        var idsRequest = IdsRequest.builder()
                .ids(ids)
                .build();
        Mockito.when(service.cancelApprovelBillingBulk(ids))
                .thenReturn(List.of(new DisbursementDto(), new DisbursementDto()));

        mvc.perform(put("/disbursement/cancel-billing-approve/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(idsRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGivenApproveBillingRequest_thenApproveBilling() throws Exception {
        var approveBillingRequest = new ApproveBillingRequest();
        Mockito.doNothing().when(service).approveBilling(approveBillingRequest);

        mvc.perform(put("/disbursement/approve-billing")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(approveBillingRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetToBeBilledDisbursements_thenReturnBaseResponse() throws Exception {
        var disbursementDto = DisbursementDto.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType("test-type")
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .user("test-user")
                .activityId(1L)
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .build();
        var pageDto = DisbursementPageDto.builder()
                .disbursements(List.of(disbursementDto))
                .totalElements(5L)
                .totalPages(1L)
                .build();
        Mockito.when(service.getToBeBilledDisbursements(Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(pageDto);
        mvc.perform(get("/disbursement/to-be-billed/1/1?ids=1,2&billingPeriodEnds=2021-01-01")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetBilledDisbursements_thenReturnBaseResponse() throws Exception {
        var disbursementDto = DisbursementDto.builder()
                .id(1L)
                .disbursementDate(LocalDate.now())
                .disbursementType("test-type")
                .amount(BigDecimal.ZERO)
                .currency("TL")
                .user("test-user")
                .activityId(1L)
                .description("test-description")
                .fileName("image001.png")
                .contentType("image/png")
                .build();
        var pageDto = DisbursementPageDto.builder()
                .disbursements(List.of(disbursementDto))
                .totalElements(5L)
                .totalPages(1L)
                .build();
        Mockito.when(service.getBilledDisbursements(Mockito.any(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(pageDto);
        mvc.perform(get("/disbursement/billed/1/1?ids=1,2&billingPeriodEnds=2021-01-01")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenHasBillable_thenReturnBaseResponse() throws Exception {
        var disbursementIds = List.of(1L, 2L);
        var expenseIds = List.of(1L, 2L);
        Mockito.when(service.hasBillable(disbursementIds, expenseIds))
               .thenReturn(true);

        mvc.perform(get("/disbursement/has-billable?disbursementId=1,2s&expenseIds=3,4")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
               .andExpect(status().isOk());
    }

    @Test
    public void whenUndoBillingApproval_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).undoBillingApproval(List.of(1L, 2L), LocalDate.parse("2021-01-01"));

        mvc.perform(put("/disbursement/undo-billing-approve/1,2/2021-01-01")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenMoreThanBillingAccount_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.isTotalAmountMoreThanBillingAmount(Mockito.anyList(), Mockito.anyList()))
                .thenReturn(true);

        mvc.perform(get("/disbursement/more-than-billing-amount")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenMoreThanZero_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.isTotalAmountMoreThanZero(Mockito.anyList(), Mockito.anyList()))
                .thenReturn(true);

        mvc.perform(get("/disbursement/more-than-zero")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUpdateTimesheetsForBillingCancellation_thenReturnBaseResponse() throws Exception {
        List<Long> disbursementIds = Arrays.asList(1L, 2L, 3L);
        LocalDate billingPeriodEnds = LocalDate.of(2025, 3, 1);
        mvc.perform(put("/disbursement/billing-cancellation/{disbursementIds}/{billingPeriodEnds}",
                        String.join(",", disbursementIds.stream().map(String::valueOf).toList()),
                        billingPeriodEnds
                ).contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(service).updateDisbursementsForBillingCancellation(disbursementIds, billingPeriodEnds);
    }
}
