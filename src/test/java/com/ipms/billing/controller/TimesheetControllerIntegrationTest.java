package com.ipms.billing.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.billing.dto.*;
import com.ipms.billing.service.TimesheetService;
import com.ipms.core.entity.Revision;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.Collections;
import java.util.List;

import static org.mockito.ArgumentMatchers.*;
import static org.mockito.Mockito.times;
import static org.mockito.Mockito.verify;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/application.yml")
public class TimesheetControllerIntegrationTest {

    @Autowired
    private MockMvc mvc;

    @MockBean
    private TimesheetService service;

    ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule());

    @Test
    public void givenTimesheetDto_whenSave_thenReturnBaseResponse() throws Exception {
        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .description("test-description")
                .date(LocalDate.MAX)
                .activityId(1L)
                .matterId(1L)
                .loggedTime(BigDecimal.ONE)
                .billableTime(BigDecimal.ZERO)
                .isIncludeReport(Boolean.FALSE)
                .isApproved(Boolean.FALSE)
                .includedInCharge(Boolean.FALSE)
                .timekeeper("test-timekeeper")
                .localAttorneyId(1L)
                .build();
        Mockito.when(service.save(Mockito.any(TimesheetDto.class)))
                .thenReturn(timesheetDto);
        mvc.perform(post("/timesheets/")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(timesheetDto))
                        .accept(MediaType.APPLICATION_JSON))
                        .andExpect(status().isOk());
    }

    @Test
    public void givenId_whenGetActivity_thenReturnBaseResponse() throws Exception {
        var timesheetDto = TimesheetDto.builder().id(1L).build();
        Mockito.when(service.getById(anyLong()))
                .thenReturn(timesheetDto);
        mvc.perform(get("/timesheets/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                        .andExpect(status().isOk());
    }

    @Test
    public void givenDates_whenGetAllByBetweenDates_thenReturnBaseResponse() throws Exception {
        var today = LocalDate.now();
        var yesterday = today.minusDays(1);
        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .build();
        var timesheetSummaryDto = TimesheetSummaryDto
                .builder()
                .timesheetList(List.of(timesheetDto))
                .build();
        Mockito.when(service.getAllByDates(yesterday, today))
                .thenReturn(timesheetSummaryDto);
        mvc.perform(get("/timesheets/2022-11-16/2022-11-17")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                        .andExpect(status().isOk());
    }

    @Test
    public void givenActivityIdAndPageAndSize_whenGetAllByActivity_thenReturnBaseResponse() throws Exception {
        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .build();
        var timesheetPageDto = TimesheetPageDto
                .builder()
                .timesheetList(List.of(timesheetDto))
                .build();
        Mockito.when(service.getByActivity(anyLong(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(timesheetPageDto);
        mvc.perform(get("/timesheets/1/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                        .andExpect(status().isOk());
    }

    @Test
    public void givenTimesheetDto_whenUpdate_thenReturnBaseResponse() throws Exception {
        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .version(1L)
                .activityId(1L)
                .matterId(1L)
                .date(LocalDate.now())
                .billableTime(BigDecimal.ONE)
                .loggedTime(BigDecimal.ONE)
                .isIncludeReport(Boolean.TRUE)
                .isApproved(Boolean.TRUE)
                .description("test-description")
                .includedInCharge(true)
                .timekeeper("test-timekeeper")
                .localAttorneyId(1L)
                .build();
        Mockito.when(service.update(Mockito.any(TimesheetDto.class)))
                .thenReturn(timesheetDto);
        mvc.perform(put("/timesheets/")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(timesheetDto))
                        .accept(MediaType.APPLICATION_JSON))
                        .andExpect(status().isOk());
    }

    @Test
    public void givenIdAndVersion_whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service)
                .delete(anyLong(), anyLong());
        mvc.perform(delete("/timesheets/1/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetList_thenReturnBaseResponse() throws Exception {
        var timesheetDto = TimesheetDto.builder()
                .id(1L)
                .version(1L)
                .build();
        Mockito.when(service.getByIdIn(Mockito.anyList()))
                .thenReturn(List.of(timesheetDto));

        mvc.perform(get("/timesheets/ids/1231")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenActivityId_whenGetTotalTimesByActivity_thenReturnBaseResponse() throws Exception {
        var totalTimes = TimesheetTotalTimeDto.builder().totalBillableTime(BigDecimal.ONE).build();
        Mockito.when(service.getTimesByActivity(anyLong()))
                .thenReturn(totalTimes);
        mvc.perform(get("/timesheets/times/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetRevisions_thenReturnBaseResponse() throws Exception {
        var revision = Revision.builder()
                .fieldName("status")
                .updatedAt(LocalDateTime.now())
                .build();
        Mockito.when(service.getRevisions(anyLong()))
                .thenReturn(List.of(revision));

        mvc.perform(get("/timesheets/revisions/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetRevisionsByField_thenReturnBaseResponse() throws Exception {
        var revision = Revision.builder()
                .fieldName("status")
                .updatedAt(LocalDateTime.now())
                .build();
        Mockito.when(service.getRevisionsByField(anyLong(), anyString()))
                .thenReturn(List.of(revision));

        mvc.perform(get("/timesheets/revisions/1/status")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGivenApproveBulkRequest_thenApproveBulk() throws Exception {
        var ids = List.of(1L, 2L);
        var approveBulkRequest = ApproveBulkRequest.builder()
                .ids(ids)
                .build();
        Mockito.when(service.approveBulk(ids))
                .thenReturn(List.of(new TimesheetDto(), new TimesheetDto()));

        mvc.perform(put("/timesheets/approve/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(approveBulkRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetOtherUnbilledTimes_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getOtherUnbilledTimes(1L, LocalDate.now()))
                .thenReturn(TimesheetTotalTimeDto.builder().build());

        mvc.perform(get("/timesheets/not-invoiced-times/1/2007-12-03?others=true")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetOtherUnbilledPage_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getOtherUnbilledPage(1L, LocalDate.now(), 1, 10))
                .thenReturn(TimesheetPageDto.builder().build());

        mvc.perform(get("/timesheets/not-invoiced/1/2007-12-03/10/1?others=true")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetUnbilledTimes_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getUnbilledTimes(1L, LocalDate.now()))
                .thenReturn(TimesheetTotalTimeDto.builder().build());

        mvc.perform(get("/timesheets/not-invoiced-times/1/2007-12-03?others=false")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetUnbilledTimesheetsPage_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getUnbilledTimesheetsPage(1L, LocalDate.now(), 10, 1))
                .thenReturn(TimesheetPageDto.builder().build());

        mvc.perform(get("/timesheets/not-invoiced/1/2007-12-03/10/1?others=false")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenApproveBySystem_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).approveBySystem(LocalDate.now());

        mvc.perform(post("/timesheets/approve-by-system/2024-07-03")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenCalculateIncludedInCharge_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service)
                .calculateIncludedInCharge(Mockito.anyLong(), Mockito.any(LocalDate.class), Mockito.any(BigDecimal.class));

        var includedInChargeRequest = TimesheetIncludedInChargeRequest.builder()
                .tsicIncludedCharge(BigDecimal.ONE)
                .activityId(1L)
                .packageStartDate(LocalDate.now())
                .build();
        mvc.perform(put("/timesheets/calculate-included-in-charge")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(includedInChargeRequest)))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetAllByBetweenDates_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getAllByDates(1L, LocalDate.now(), LocalDate.now()))
                .thenReturn(TimesheetTotalTimeDto.builder().build());

        mvc.perform(get("/timesheets/by-activity/1/2024-06-03/2024-07-03")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetUnbilledTimesByActivity_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getUnbilledTimesByActivity(1L))
                .thenReturn(TimesheetTotalTimeDto.builder().build());

        mvc.perform(get("/timesheets/not-invoiced-times/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenChangeActivity_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).changeActivity(Mockito.any(TimesheetChangeActivityRequest.class));
        var changeActivityRequest = TimesheetChangeActivityRequest.builder()
                .targetActivityId(1L)
                .build();
        mvc.perform(put("/timesheets/change-activity")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(changeActivityRequest)))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGivenApproveBulkRequest_thenApproveBillingBulk() throws Exception {
        var ids = List.of(1L, 2L);
        var approveBulkRequest = ApproveBulkRequest.builder()
                .ids(ids)
                .build();
        Mockito.when(service.approveBillingBulk(ids))
                .thenReturn(List.of(new TimesheetDto(), new TimesheetDto()));

        mvc.perform(put("/timesheets/billing-approve/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(approveBulkRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGivenApproveBulkRequest_thenCancelApproveBillingBulk() throws Exception {
        var ids = List.of(1L, 2L);
        var approveBulkRequest = ApproveBulkRequest.builder()
                .ids(ids)
                .build();
        Mockito.when(service.cancelApprovelBillingBulk(ids))
                .thenReturn(List.of(new TimesheetDto(), new TimesheetDto()));

        mvc.perform(put("/timesheets/cancel-billing-approve/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(approveBulkRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetTotalTimesByActivityAndStartDate_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.getTimesByActivityAndStartDate(1L, LocalDate.now()))
                .thenReturn(TimesheetTotalTimeDto.builder().build());

        mvc.perform(get("/timesheets/times/1/2024-06-03")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetTotalTimesByActivityAndEndDate_thenReturnBaseResponse() throws Exception {
        mvc.perform(get("/timesheets/times-by-end-date/1/2024-06-03")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenHasUnapproved_thenReturnBaseResponse() throws Exception {
        Mockito.when(service.hasUnapproved(1L, LocalDate.now()))
                .thenReturn(Boolean.TRUE);

        mvc.perform(get("/timesheets/has-unapproved/1/2024-06-03")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenCancelBillingOrder_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).cancelBillingOrder(List.of(1L, 2L));
        mvc.perform(put("/timesheets/cancel-billing-order/1,2")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void whenUpdateTimesheetsForBillingCancellation_thenReturnBaseResponse() throws Exception {
        Long activityId = 123L;
        LocalDate billingPeriodEnds = LocalDate.of(2025, 3, 1);
        mvc.perform(put("/timesheets/billing-cancellation/{activityId}/{billingPeriodEnds}",
                        activityId, "2025-03-01")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());

        verify(service, times(1))
                .updateTimesheetsForBillingCancellation(activityId, billingPeriodEnds);
    }

    @Test
    public void whenGetTimesheets_shouldReturnFilteredTimesheets() throws Exception {
        TimesheetDto timesheet1 = TimesheetDto.builder().id(1L).activityId(101L).build();
        var timesheets = Collections.singletonList(timesheet1);

        Mockito.when(service.getAll(any(TimesheetFilterRequest.class))).thenReturn(timesheets);

        mvc.perform(get("/timesheets/timesheets")
                        .param("startDate", "2023-01-01T00:00:00")
                        .param("endDate", "2023-12-31T23:59:59")
                        .contentType(MediaType.APPLICATION_JSON)
                                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
    
    @Test
    public void givenIncludedInChargeBulkRequest_whenUpdateIncludedInChargeBulk_thenReturnUpdatedTimesheets() throws Exception {
        var ids = List.of(1L, 2L);
        var includedInChargeBulkRequest = IncludedInChargeBulkRequest.builder()
                .ids(ids)
                .includedInCharge(true)
                .build();
                
        var timesheetDto1 = TimesheetDto.builder().id(1L).includedInCharge(true).build();
        var timesheetDto2 = TimesheetDto.builder().id(2L).includedInCharge(true).build();
        
        Mockito.when(service.updateIncludedInChargeBulk(anyList(), anyBoolean()))
                .thenReturn(List.of(timesheetDto1, timesheetDto2));
                
        mvc.perform(put("/timesheets/included-in-charge/bulk")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(includedInChargeBulkRequest))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                
        verify(service, times(1)).updateIncludedInChargeBulk(ids, true);
    }
    
    @Test
    public void givenApproveBillingRequest_whenApproveBilling_thenReturnSuccess() throws Exception {
        ApproveBillingRequest request = ApproveBillingRequest.builder()
                .activityId(123L)
                .build();
        
        Mockito.doNothing().when(service).approveBilling(any(ApproveBillingRequest.class));
        
        mvc.perform(put("/timesheets/approve-billing")
                .contentType(MediaType.APPLICATION_JSON)
                .content(objectMapper.writeValueAsString(request))
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                
        verify(service, times(1)).approveBilling(any(ApproveBillingRequest.class));
    }
    
    @Test
    public void givenActivityIdAndDate_whenCheckIfTimesheetsAreApprovable_thenReturnBoolean() throws Exception {
        Long activityId = 123L;
        LocalDate billingPeriodEnds = LocalDate.of(2023, 12, 31);
        
        Mockito.when(service.isBillingApprovableTimesheets(activityId, billingPeriodEnds)).thenReturn(true);
        
        mvc.perform(get("/timesheets/is-approvable/{activityId}/{billingPeriodEnds}", activityId, billingPeriodEnds)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                
        verify(service, times(1)).isBillingApprovableTimesheets(activityId, billingPeriodEnds);
    }
    
    @Test
    public void givenActivityIdAndDate_whenCheckIfTimesheetsHaveBillable_thenReturnBoolean() throws Exception {
        Long activityId = 123L;
        LocalDate billingPeriodEnds = LocalDate.of(2023, 12, 31);
        
        Mockito.when(service.hasBillableTimesheet(activityId, billingPeriodEnds)).thenReturn(true);
        
        mvc.perform(get("/timesheets/has-billable/{activityId}/{billingPeriodEnds}", activityId, billingPeriodEnds)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                
        verify(service, times(1)).hasBillableTimesheet(activityId, billingPeriodEnds);
    }
    
    @Test
    public void givenActivityIdAndDate_whenUndoBillingApproval_thenReturnSuccess() throws Exception {
        Long activityId = 123L;
        LocalDate billingPeriodEnds = LocalDate.of(2023, 12, 31);
        
        Mockito.doNothing().when(service).undoBillingApprovalTimesheets(activityId, billingPeriodEnds);
        
        mvc.perform(put("/timesheets/undo-billing-approve/{activityId}/{billingPeriodEnds}", activityId, billingPeriodEnds)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                
        verify(service, times(1)).undoBillingApprovalTimesheets(activityId, billingPeriodEnds);
    }
    
    @Test
    public void givenTimesheetIdAndDate_whenSplitTimesheet_thenReturnBoolean() throws Exception {
        Long timesheetId = 123L;
        LocalDate billingPeriodEnds = LocalDate.of(2023, 12, 31);
        
        Mockito.when(service.splitTimesheet(timesheetId, billingPeriodEnds)).thenReturn(true);
        
        mvc.perform(post("/timesheets/split-timesheet/{timesheetId}/{billingPeriodEnds}", timesheetId, billingPeriodEnds)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                
        verify(service, times(1)).splitTimesheet(timesheetId, billingPeriodEnds);
    }
    
    @Test
    public void givenPageAndSizeAndFilter_whenGetTimesheetPage_thenReturnTimesheetPageDto() throws Exception {
        int page = 0;
        int size = 10;
        TimesheetPageDto pageDto = TimesheetPageDto.builder()
                .timesheetList(List.of(TimesheetDto.builder().id(1L).build()))
                .totalElements(1L)
                .totalPages(1)
                .build();
        
        Mockito.when(service.getPage(any(TimesheetFilterRequest.class), eq(page), eq(size))).thenReturn(pageDto);
        
        mvc.perform(get("/timesheets/timesheets/{page}/{size}", page, size)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                
        verify(service, times(1)).getPage(any(TimesheetFilterRequest.class), eq(page), eq(size));
    }
    
    @Test
    public void givenActivityIdAndDate_whenGetTimesByActivityAndStartDateForFds_thenReturnTimesheetTotalTimeDto() throws Exception {
        Long activityId = 123L;
        LocalDate startDate = LocalDate.of(2023, 1, 1);
        TimesheetTotalTimeDto totalTimeDto = TimesheetTotalTimeDto.builder()
                .totalBillableTime(BigDecimal.valueOf(10))
                .totalLoggedTime(BigDecimal.valueOf(12))
                .build();
        
        Mockito.when(service.getTimesByActivityAndStartDateForFds(activityId, startDate)).thenReturn(totalTimeDto);
        
        mvc.perform(get("/timesheets/times-fds/{activityId}/{startDate}", activityId, startDate)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                
        verify(service, times(1)).getTimesByActivityAndStartDateForFds(activityId, startDate);
    }
    
    @Test
    public void givenActivityId_whenGetUnbilledTimesheetsByActivityId_thenReturnListOfTimesheetDto() throws Exception {
        Long activityId = 123L;
        List<TimesheetDto> timesheets = List.of(
            TimesheetDto.builder().id(1L).build(),
            TimesheetDto.builder().id(2L).build()
        );
        
        Mockito.when(service.getUnbilledTimesheetsByActivityId(activityId)).thenReturn(timesheets);
        
        mvc.perform(get("/timesheets/unbilled/{activityId}", activityId)
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
                
        verify(service, times(1)).getUnbilledTimesheetsByActivityId(activityId);
    }
}
