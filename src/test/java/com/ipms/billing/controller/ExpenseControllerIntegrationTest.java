package com.ipms.billing.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.billing.dto.*;
import com.ipms.billing.service.ExpenseService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;
import org.springframework.test.web.servlet.ResultActions;
import org.springframework.test.web.servlet.request.MockHttpServletRequestBuilder;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

import static org.mockito.ArgumentMatchers.any;
import static org.mockito.Mockito.doNothing;
import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/application.yml")
public class ExpenseControllerIntegrationTest {

    private static final String BASE_URL = "/expense";
    private static final LocalDate TEST_DATE = LocalDate.now();
    private static final List<Long> TEST_IDS = List.of(1L, 2L);

    @Autowired
    private MockMvc mvc;

    @MockBean
    private ExpenseService service;

    ObjectMapper objectMapper = new ObjectMapper()
            .registerModule(new JavaTimeModule());

    private ExpenseDto createDefaultExpenseDto() {
        return ExpenseDto.builder()
                .id(1L)
                .expenseCode("test-code")
                .expenseDate(TEST_DATE)
                .currency("TL")
                .quantity(1L)
                .unitPrice(BigDecimal.ONE)
                .description("test-description")
                .activityId(1L)
                .build();
    }

    private ResultActions performRequest(MockHttpServletRequestBuilder requestBuilder) throws Exception {
        return mvc.perform(requestBuilder
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));
    }

    private ResultActions performRequestWithBody(MockHttpServletRequestBuilder requestBuilder, Object body) throws Exception {
        return mvc.perform(requestBuilder
                .content(objectMapper.writeValueAsString(body))
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON));
    }

    @Test
    public void whenSave_thenReturnBaseResponse() throws Exception {
        var expenseDto = createDefaultExpenseDto();
        when(service.save(Mockito.mock(ExpenseDto.class)))
                .thenReturn(expenseDto);

        performRequestWithBody(post(BASE_URL + "/"), expenseDto)
                .andExpect(status().isOk());
    }

    @Test
    public void givenActivityIdAndPageAndSize_whenGetByIds_thenReturnBaseResponse() throws Exception {
        var expenseDto = createDefaultExpenseDto();
        var pageDto = ExpensePageDto.builder()
                .expenses(List.of(expenseDto))
                .totalElements(5L)
                .totalPages(1L)
                .build();
        when(service.getByIds(Mockito.anyList(), Mockito.anyInt(), Mockito.anyInt()))
                .thenReturn(pageDto);
        performRequest(get(BASE_URL + "/1,2/1/1"))
                .andExpect(status().isOk());
    }

    @Test
    public void whenDelete_thenReturnBaseResponse() throws Exception {
        Mockito.doNothing().when(service).delete(Mockito.anyLong(), Mockito.anyLong());

        performRequest(delete(BASE_URL + "/5/0"))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetByIds_thenReturnBaseResponse() throws Exception {
        var expenseDto = createDefaultExpenseDto();

        when(service.getByIdIn(Mockito.anyList()))
                .thenReturn(List.of(expenseDto));

        performRequest(get(BASE_URL + "/ids/1"))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGivenIdsRequest_thenApproveBillingBulk() throws Exception {
        IdsRequest idsRequest = IdsRequest.builder()
                .ids(TEST_IDS)
                .build();

        when(service.approveBillingBulk(TEST_IDS))
                .thenReturn(List.of(new ExpenseDto(), new ExpenseDto()));

        performRequestWithBody(put(BASE_URL + "/billing-approve/bulk"), idsRequest)
                .andExpect(status().isOk());
    }

    @Test
    public void whenGivenIdsRequest_thenCancelApproveBillingBulk() throws Exception {
        IdsRequest idsRequest = IdsRequest.builder()
                .ids(TEST_IDS)
                .build();

        when(service.cancelApprovelBillingBulk(TEST_IDS))
                .thenReturn(List.of(new ExpenseDto(), new ExpenseDto()));

        performRequestWithBody(put(BASE_URL + "/cancel-billing-approve/bulk"), idsRequest)
                .andExpect(status().isOk());
    }

    @Test
    public void whenGivenIds_thenDeleteBulk() throws Exception {
        doNothing().when(service).deleteBulk(TEST_IDS);

        performRequest(delete(BASE_URL + "/1,2"))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGivenPageAndSizeAndFilterRequest_thenGetToBeBilledExpenses() throws Exception {
        var filterRequest = new ExpenseFilterRequest();
        when(service.getToBeBilledExpensePage(filterRequest, 1, 1))
                .thenReturn(new ExpensePageDto());

        performRequest(get(BASE_URL + "/to-be-billed/1/1?ids=1,2&billingPeriodEnds=2021-01-01"))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGivenPageAndSizeAndExpenseIds_thenGetOrderCreationExpenses() throws Exception {
        when(service.getOrderCreationExpenses("test", 0, 10))
                .thenReturn(ExpenseSummaryPageDto.builder().build());

        performRequest(get(BASE_URL + "/order-creation/1,2/0/10"))
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetListByOrderNo_thenReturnBaseResponse() throws Exception {
        when(service.getExpensesByOrderNo("test")).thenReturn(List.of(ExpenseDto.builder().build()));

        performRequest(get(BASE_URL + "/by-order/test"))
                .andExpect(status().isOk());
    }

    @Test
    public void whenIsInvoiceCompleted_thenReturnBaseResponse() throws Exception {
        when(service.isInvoiceCompleted(LocalDate.parse("2021-01-01"), List.of(1L))).thenReturn(true);

        performRequest(get(BASE_URL + "/is-invoice-completed/2021-01-01/1"))
                .andExpect(status().isOk());
    }

    @Test
    public void whenApplyBillingDiscount_thenReturnBaseResponse() throws Exception {
        var discountRequest = DiscountRequest.builder().build();
        doNothing().when(service).applyBillingDiscount(discountRequest);

        performRequestWithBody(put(BASE_URL + "/billing-discount"), discountRequest)
                .andExpect(status().isOk());
    }

    @Test
    public void whenGetTotalAmount_thenReturnBaseResponse() throws Exception {
        when(service.getTotalAmountForDiscount(List.of(1L))).thenReturn(new TotalAmount());

        performRequest(get(BASE_URL + "/billing-discount/total-amount/1"))
                .andExpect(status().isOk());
    }

    @Test
    public void whenCleanDiscountRate_thenReturnBaseResponse() throws Exception {
        doNothing().when(service).clearDiscountRate(any(), any());

        performRequest(put(BASE_URL + "/clear-discount-rate/1/2021-01-01"))
                .andExpect(status().isOk());
    }
}
