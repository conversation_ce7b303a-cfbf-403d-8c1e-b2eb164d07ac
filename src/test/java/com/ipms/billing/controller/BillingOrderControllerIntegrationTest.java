package com.ipms.billing.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.fasterxml.jackson.datatype.jsr310.JavaTimeModule;
import com.ipms.billing.dto.BillingOrderDetailDto;
import com.ipms.billing.dto.BillingOrderDto;
import com.ipms.billing.enums.BillingOrderStatus;
import com.ipms.billing.service.BillingOrderService;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;
import org.junit.runner.RunWith;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;

import static org.mockito.Mockito.when;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.*;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import=classpath:/application.yml")
class BillingOrderControllerIntegrationTest {

    @Autowired
    private MockMvc mockMvc;

    @MockBean
    private BillingOrderService service;

    ObjectMapper objectMapper = new ObjectMapper().registerModule(new JavaTimeModule());

    private BillingOrderDto billingOrderDto;

    @BeforeEach
    public void setUp() {
        var orderDetailDto1 = BillingOrderDetailDto.builder()
                .id(1L)
                .expenseCode("expenseCode-1")
                .quantity(10L)
                .unitPrice(BigDecimal.valueOf(10.10))
                .build();
        billingOrderDto = BillingOrderDto.builder()
                .id(1L)
                .orderNumber("orderNumber-1")
                .orderDate(LocalDate.parse("2024-01-01"))
                .businessUnit("bu-1")
                .issuerId(1L)
                .billingAccountNo("billingAccountNo-1")
                .orderDetails(new ArrayList<>(Arrays.asList(orderDetailDto1)))
                .description1("description-1")
                .description2("description-2")
                .description3("description-3")
                .externalOrderNumber("externalOrderNumber-1")
                .status(BillingOrderStatus.ERROR_IN_ORDER)
                .invoiceNumber("invoiceNumber-1")
                .invoiceDate(LocalDate.parse("2024-01-01"))
                .build();
    }

    @Test
    void whenGetById_thenReturnBaseResponse() throws Exception {
        when(service.getById(1L)).thenReturn(billingOrderDto);

        mockMvc.perform(get("/billing-orders/1")
                .contentType(MediaType.APPLICATION_JSON)
                .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void whenSave_thenReturnBaseResponse() throws Exception {
        when(service.save(billingOrderDto)).thenReturn(billingOrderDto);

        mockMvc.perform(post("/billing-orders")
                        .content(objectMapper.writeValueAsString(billingOrderDto))
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void whenDelete_thenReturnBaseResponse() throws Exception {
        mockMvc.perform(delete("/billing-orders/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void whenGetBillingOrderList_thenReturnBaseResponse() throws Exception {
        mockMvc.perform(get("/billing-orders/ids/1,2")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    void whenCancelBillingOrder_thenReturnBaseResponse() throws Exception {
        mockMvc.perform(put("/billing-orders/cancel/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
