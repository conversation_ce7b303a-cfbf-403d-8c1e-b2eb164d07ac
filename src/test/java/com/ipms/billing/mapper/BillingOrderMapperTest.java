package com.ipms.billing.mapper;

import com.ipms.billing.dto.BillingOrderDetailDto;
import com.ipms.billing.dto.BillingOrderDto;
import com.ipms.billing.enums.BillingOrderStatus;
import com.ipms.billing.model.BillingOrder;
import com.ipms.billing.model.BillingOrderDetail;
import org.junit.jupiter.api.BeforeEach;
import org.junit.jupiter.api.Test;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.Arrays;

import static org.junit.jupiter.api.Assertions.*;

class BillingOrderMapperTest {
    private final BillingOrderMapper mapper = new BillingOrderMapperImpl();

    private BillingOrder billingOrder;
    private BillingOrderDto billingOrderUpdateDto;
    private BillingOrderDto billingOrderDto;

    @BeforeEach
    public void setUp() {
        var orderDetail1 = BillingOrderDetail.builder()
                .id(1L)
                .expenseCode("expenseCode-1")
                .quantity(10L)
                .unitPrice(BigDecimal.valueOf(10.10))
                .build();

        billingOrder = BillingOrder.builder()
                .id(1L)
                .orderNumber("orderNumber-1")
                .orderDate(LocalDate.parse("2024-01-01"))
                .businessUnit("businessUnit-1")
                .issuerId(1L)
                .billingAccountNo("billingAccountNo-1")
                .orderDetails(new ArrayList<>(Arrays.asList(orderDetail1)))
                .description1("description-1")
                .description2("description-2")
                .description3("description-3")
                .externalOrderNumber("externalOrderNumber-1")
                .status(BillingOrderStatus.ERROR_IN_ORDER)
                .invoiceNumber("invoiceNumber-1")
                .invoiceDate(LocalDate.parse("2024-01-01"))
                .build();

        var orderDetailDto1 = BillingOrderDetailDto.builder()
                .id(1L)
                .expenseCode("expenseCode-1")
                .quantity(10L)
                .unitPrice(BigDecimal.valueOf(10.10))
                .build();
        var orderDetailDto2 = BillingOrderDetailDto.builder()
                .id(2L)
                .expenseCode("expenseCode-2")
                .quantity(13L)
                .unitPrice(BigDecimal.valueOf(14.10))
                .build();

        billingOrderUpdateDto = BillingOrderDto.builder()
                .id(2L)
                .orderNumber("orderNumber-2")
                .orderDate(LocalDate.parse("2024-02-02"))
                .businessUnit("businessUnit-2")
                .issuerId(2L)
                .billingAccountNo("billingAccountNo-2")
                .orderDetails(new ArrayList<>(Arrays.asList(orderDetailDto1, orderDetailDto2)))
                .description1("description-2")
                .description2("description-2")
                .description3("description-3")
                .externalOrderNumber("externalOrderNumber-2")
                .status(BillingOrderStatus.ERROR_IN_ORDER)
                .invoiceNumber("invoiceNumber-2")
                .invoiceDate(LocalDate.parse("2024-02-02"))
                .build();

        var orderDetailDto3 = BillingOrderDetailDto.builder()
                .id(2L)
                .expenseCode("expenseCode-2")
                .quantity(13L)
                .unitPrice(BigDecimal.valueOf(14.10))
                .build();

        billingOrderDto = BillingOrderDto.builder()
                .id(1L)
                .orderNumber("orderNumber-1")
                .orderDate(LocalDate.parse("2024-01-01"))
                .businessUnit("businessUnit-1")
                .issuerId(1L)
                .billingAccountNo("billingAccountNo-1")
                .orderDetails(new ArrayList<>(Arrays.asList(orderDetailDto3)))
                .description1("description-1")
                .description2("description-2")
                .description3("description-3")
                .externalOrderNumber("externalOrderNumber-1")
                .status(BillingOrderStatus.ERROR_IN_ORDER)
                .invoiceNumber("invoiceNumber-1")
                .invoiceDate(LocalDate.parse("2024-01-01"))
                .build();
    }

    @Test
    void toBillingOrderDto() {
        var mappedBillingOrderDto = mapper.toBillingOrderDto(billingOrder);

        assertEquals(billingOrder.getId(), mappedBillingOrderDto.getId());
        assertEquals(billingOrder.getOrderNumber(), mappedBillingOrderDto.getOrderNumber());
        assertEquals(billingOrder.getOrderDate(), mappedBillingOrderDto.getOrderDate());
        assertEquals(billingOrder.getBusinessUnit(), mappedBillingOrderDto.getBusinessUnit());
        assertEquals(billingOrder.getIssuerId(), mappedBillingOrderDto.getIssuerId());
        assertEquals(billingOrder.getBillingAccountNo(), mappedBillingOrderDto.getBillingAccountNo());
        assertEquals(billingOrder.getOrderDetails().size(), mappedBillingOrderDto.getOrderDetails().size());
        assertEquals(billingOrder.getOrderDetails().get(0).getId(), mappedBillingOrderDto.getOrderDetails().get(0).getId());
        assertEquals(billingOrder.getDescription1(), mappedBillingOrderDto.getDescription1());
        assertEquals(billingOrder.getDescription2(), mappedBillingOrderDto.getDescription2());
        assertEquals(billingOrder.getDescription3(), mappedBillingOrderDto.getDescription3());
        assertEquals(billingOrder.getExternalOrderNumber(), mappedBillingOrderDto.getExternalOrderNumber());
        assertEquals(billingOrder.getStatus(), mappedBillingOrderDto.getStatus());
        assertEquals(billingOrder.getInvoiceNumber(), mappedBillingOrderDto.getInvoiceNumber());
        assertEquals(billingOrder.getInvoiceDate(), mappedBillingOrderDto.getInvoiceDate());
    }

    @Test
    void toBillingOrder() {
        var mappedBillingOrder = mapper.toBillingOrder(billingOrderDto);

        assertNull(mappedBillingOrder.getId());
        assertNull(mappedBillingOrder.getOrderNumber());
        assertEquals(billingOrderDto.getOrderDate(), mappedBillingOrder.getOrderDate());
        assertEquals(billingOrderDto.getBusinessUnit(), mappedBillingOrder.getBusinessUnit());
        assertEquals(billingOrderDto.getIssuerId(), mappedBillingOrder.getIssuerId());
        assertEquals(billingOrderDto.getBillingAccountNo(), mappedBillingOrder.getBillingAccountNo());
        assertEquals(billingOrderDto.getOrderDetails().size(), mappedBillingOrder.getOrderDetails().size());
        assertEquals(billingOrderDto.getDescription1(), mappedBillingOrder.getDescription1());
        assertEquals(billingOrderDto.getDescription2(), mappedBillingOrder.getDescription2());
        assertEquals(billingOrderDto.getDescription3(), mappedBillingOrder.getDescription3());
        assertEquals(billingOrderDto.getExternalOrderNumber(), mappedBillingOrder.getExternalOrderNumber());
        assertEquals(billingOrderDto.getStatus(), mappedBillingOrder.getStatus());
        assertEquals(billingOrderDto.getInvoiceNumber(), mappedBillingOrder.getInvoiceNumber());
        assertEquals(billingOrderDto.getInvoiceDate(), mappedBillingOrder.getInvoiceDate());
    }

    @Test
    void toBillingOrderFromDto() {
        var mappedBillingOrder = mapper.toBillingOrderFromDto(billingOrderUpdateDto, billingOrder);

        assertNotEquals(billingOrderUpdateDto.getId(), mappedBillingOrder.getId());
        assertEquals(billingOrder.getOrderNumber(), mappedBillingOrder.getOrderNumber());
        assertEquals(billingOrderUpdateDto.getOrderDate(), mappedBillingOrder.getOrderDate());
        assertEquals(billingOrderUpdateDto.getBusinessUnit(), mappedBillingOrder.getBusinessUnit());
        assertEquals(billingOrderUpdateDto.getIssuerId(), mappedBillingOrder.getIssuerId());
        assertEquals(billingOrderUpdateDto.getBillingAccountNo(), mappedBillingOrder.getBillingAccountNo());
        assertEquals(billingOrderUpdateDto.getOrderDetails().size(), mappedBillingOrder.getOrderDetails().size());
        assertEquals(billingOrderUpdateDto.getDescription1(), mappedBillingOrder.getDescription1());
        assertEquals(billingOrderUpdateDto.getDescription2(), mappedBillingOrder.getDescription2());
        assertEquals(billingOrderUpdateDto.getDescription3(), mappedBillingOrder.getDescription3());
        assertEquals(billingOrderUpdateDto.getExternalOrderNumber(), mappedBillingOrder.getExternalOrderNumber());
        assertEquals(billingOrderUpdateDto.getStatus(), mappedBillingOrder.getStatus());
        assertEquals(billingOrderUpdateDto.getInvoiceNumber(), mappedBillingOrder.getInvoiceNumber());
        assertEquals(billingOrderUpdateDto.getInvoiceDate(), mappedBillingOrder.getInvoiceDate());
    }
}
