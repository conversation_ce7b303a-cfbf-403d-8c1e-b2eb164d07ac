package com.ipms.activity.controller;

import com.fasterxml.jackson.databind.ObjectMapper;
import com.ipms.activity.dto.RenewalHistoryDto;
import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.service.RenewalHistoryService;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.Mockito;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.boot.test.autoconfigure.web.servlet.AutoConfigureMockMvc;
import org.springframework.boot.test.context.SpringBootTest;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.http.MediaType;
import org.springframework.test.context.ActiveProfiles;
import org.springframework.test.context.TestPropertySource;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.web.servlet.MockMvc;

import java.util.List;

import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.get;
import static org.springframework.test.web.servlet.request.MockMvcRequestBuilders.put;
import static org.springframework.test.web.servlet.result.MockMvcResultMatchers.status;

@RunWith(SpringRunner.class)
@SpringBootTest
@ActiveProfiles("integration-test")
@AutoConfigureMockMvc(addFilters = false)
@TestPropertySource(properties = "spring.config.import:classpath:/ipms-activity/application.yml")
public class RenewalHistoryControllerIntegrationTest {
    @Autowired
    private MockMvc mvc;

    @MockBean
    private RenewalHistoryService service;

    private final ObjectMapper objectMapper = new ObjectMapper();

    @Test
    public void givenRenewalId_whenGetByActivity_thenReturnBaseResponse() throws Exception {
        var renewalHistoryDto = RenewalHistoryDto.builder()
                .period(1L)
                .status(ActivityStatus.ACCEPTED)
                .build();

        Mockito.when(service.getByRenewalId(Mockito.anyLong()))
                .thenReturn(List.of(renewalHistoryDto));

        mvc.perform(get("/renewal-history/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }

    @Test
    public void givenIdAndDto_whenUpdate_thenReturnBaseResponse() throws Exception {
        var renewalHistoryDto = RenewalHistoryDto.builder()
                .period(2L)
                .status(ActivityStatus.COMPLETED)
                .build();

        Mockito.when(service.update(Mockito.anyLong(), Mockito.any(RenewalHistoryDto.class)))
                .thenReturn(renewalHistoryDto);

        mvc.perform(put("/renewal-history/1")
                        .contentType(MediaType.APPLICATION_JSON)
                        .content(objectMapper.writeValueAsString(renewalHistoryDto))
                        .accept(MediaType.APPLICATION_JSON))
                .andExpect(status().isOk());
    }
}
