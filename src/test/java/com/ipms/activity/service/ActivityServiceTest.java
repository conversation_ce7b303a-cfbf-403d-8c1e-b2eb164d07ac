package com.ipms.activity.service;

import com.ipms.activity.client.BillingClient;
import com.ipms.activity.client.DerisClient;
import com.ipms.activity.client.DocumentClient;
import com.ipms.activity.client.MatterClient;
import com.ipms.activity.dto.*;
import com.ipms.activity.dto.trademark.MarkDto;
import com.ipms.activity.dto.turkpatent.GoodsAndServiceDto;
import com.ipms.activity.dto.turkpatent.GoodsAndServices;
import com.ipms.activity.dto.turkpatent.MarkInformationResponse;
import com.ipms.activity.enums.*;
import com.ipms.activity.exception.ActivityCannotBeUpdatedException;
import com.ipms.activity.exception.CDLetterNullException;
import com.ipms.activity.mapper.*;
import com.ipms.activity.model.*;
import com.ipms.activity.repository.ActivityRepository;
import com.ipms.activity.service.impl.ActivityServiceImpl;
import com.ipms.activity.service.impl.ComponentCourtServiceImpl;
import com.ipms.activity.service.impl.SearchActivityServiceImpl;
import com.ipms.activity.specification.ActivitySpecification;
import com.ipms.activity.validator.ActivityValidator;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.model.TransferEvent;
import com.ipms.core.service.RevisionService;
import org.junit.Before;
import org.junit.Test;
import org.junit.jupiter.api.DisplayName;
import org.junit.runner.RunWith;
import org.mockito.Mock;
import org.mockito.Mockito;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.domain.Sort;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.time.format.DateTimeFormatter;
import java.util.*;
import java.util.function.Function;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.AssertionsForClassTypes.catchThrowable;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.when;

@RunWith(SpringRunner.class)
public class ActivityServiceTest {

    @MockBean
    private ActivityService service;

    @Mock
    private ActivityRepository repository;

    @Mock
    private ProducerService producerService;

    @Mock
    private MatterClient matterClient;

    @Mock
    private BillingClient billingClient;

    @Mock
    private InvestigationFirmService investigationFirmService;

    @Mock
    private PreliminaryInjunctionService preliminaryInjunctionService;

    @Mock
    private RevisionService<Activity> revisionService;

    @Mock
    private RevisionService<CDLetter> cdLetterRevisionService;

    @Mock
    private RevisionService<UseInvestigation> useInvestigationRevisionService;

    @Mock
    private CounterRightOwnerService counterRightOwnerService;

    @Mock
    private SearchActivityServiceImpl searchActivityService;

    @Mock
    private ComponentCourtServiceImpl componentCourtService;

    @Mock
    private CustomsSuspensionService customsSuspensionService;

    @Mock
    private DerisClient derisClient;

    @Mock
    private ActivityDueDateService activityDueDateService;

    @Mock
    private RenewalService renewalService;

    @Mock
    private BulletinRelatedActivityService bulletinRelatedActivityService;

    @Mock
    private CounterRightOwnerCardService counterRightOwnerCardService;

    private final ActivityValidator validator = new ActivityValidator();

    private final ActivityMapper mapper = new ActivityMapperImpl();

    private final CriminalActionMapper criminalActionMapper = new CriminalActionMapperImpl();
    private final CustomsRecordalMapper customsRecordalMapper = new CustomsRecordalMapperImpl();
    private final NotaryDeterminationMapper notaryDeterminationMapper = new NotaryDeterminationMapperImpl();
    private DocumentClient documentClient;

    @Before
    public void setUp() {
        ReflectionTestUtils.setField(mapper, "criminalActionMapper", criminalActionMapper);
        ReflectionTestUtils.setField(mapper, "customsRecordalMapper", customsRecordalMapper);
        ReflectionTestUtils.setField(mapper, "notaryDeterminationMapper", notaryDeterminationMapper);
        service = new ActivityServiceImpl(repository, mapper, producerService, matterClient,
                billingClient, validator, investigationFirmService, preliminaryInjunctionService, revisionService,
                cdLetterRevisionService, useInvestigationRevisionService, counterRightOwnerService, searchActivityService,
                componentCourtService, customsSuspensionService, documentClient, derisClient, activityDueDateService,
                renewalService, bulletinRelatedActivityService, counterRightOwnerCardService);
    }

    @Test
    public void givenActivityDto_whenSave_thenReturnActivityDto() {
        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .build();
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.CUSTOMS.name())
                .build();
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        assertThat(service.save(activityDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenCdLetterType_whenSave_thenReturnActivityDto() {
        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CEASE_AND_DESIST_LETTER)
                .build();
        var cdLetter = CDLetterDto.builder()
                .ourPosition(CDPosition.DRAWEE.name())
                .date(LocalDate.now())
                .notificationDate(LocalDate.of(2022, 9, 16))
                .notificationType(CDNotificationType.CARGO.name())
                .officialDocumentNumber("DocumentNumber")
                .notaryNameNumber("NotaryNameNumber")
                .postalTrackingNumber("TrackingNumber")
                .build();
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.CEASE_AND_DESIST_LETTER.name())
                .cdLetter(cdLetter)
                .build();
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        assertThat(service.save(activityDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenNullCdLetterType_whenSave_thenThrowCDLetterNullException() {
        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.CEASE_AND_DESIST_LETTER.name())
                .build();
        Throwable thrown = catchThrowable(() -> service.save(activityDto));
        assertThat(thrown).isInstanceOf(CDLetterNullException.class);
    }

    @Test
    public void givenUseInvestigationType_whenSave_thenReturnActivityDto() {
        var investigationFirm = InvestigationFirm.builder()
                .name("Updated Investigation Firm")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();
        var useInvestigation = UseInvestigation.builder()
                .type(UseInvestigationType.OUTSOURCE)
                .completionDate(LocalDate.now())
                .investigationFirms(Set.of(investigationFirm))
                .build();
        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.USE_INVESTIGATION)
                .useInvestigation(useInvestigation)
                .build();

        var investigationFirmDto = InvestigationFirmDto.builder()
                .name("Updated Investigation Firm")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();
        var useInvestigationDto = UseInvestigationDto.builder()
                .type(UseInvestigationType.OUTSOURCE.name())
                .completionDate(LocalDate.now())
                .investigationFirms(List.of(investigationFirmDto))
                .build();

        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.USE_INVESTIGATION.name())
                .useInvestigation(useInvestigationDto)
                .build();
        Mockito.when(investigationFirmService.saveList(Mockito.anyList()))
                        .thenReturn(Set.of(investigationFirm));

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        assertThat(service.save(activityDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenCourtAction_whenSave_thenReturnActivityDto() {
        var courtAction = CourtAction.builder()
                .ourPosition(CourtActionOurPosition.DEFENDANT)
                .stage(CourtActionStage.DECISION)
                .types(List.of(CourtActionType.NONINFRINGEMENT_ACTION_FILED_BY_PARTY))
                .counterActions(List.of(CourtActionType.PATENT_REQUEST_FOR_DETERMINATION_OF_EVIDENCE))
                .attendance(Boolean.TRUE)
                .build();

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.COURT_ACTION)
                .courtAction(courtAction)
                .build();

        var courtActionDto = CourtActionDto.builder()
                .ourPosition(CourtActionOurPosition.DEFENDANT.name())
                .stage(CourtActionStage.DECISION.name())
                .types(List.of(CourtActionType.NONINFRINGEMENT_ACTION_FILED_BY_PARTY.name()))
                .counterActions(List.of(CourtActionType.PATENT_REQUEST_FOR_DETERMINATION_OF_EVIDENCE.name()))
                .attendance(Boolean.TRUE)
                .build();

        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.COURT_ACTION.name())
                .courtAction(courtActionDto)
                .build();

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        assertThat(service.save(activityDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenCriminalAction_whenSave_thenReturnActivityDto() {
        var enforcementDepartment = EnforcementDepartment.builder()
                .enforcementCity("Ankara")
                .enforcementProvince("Çankaya")
                .enforcementType(EnforcementDepartmentType.GENDARME)
                .build();

        var product = CriminalActionProduct.builder()
                .id(1L)
                .quantity(1)
                .type("test")
                .build();

        var trustee = CriminalActionTrustee.builder()
                .storageFees(true)
                .trusteeName("trustee-1")
                .storageInfo("storage-1")
                .build();

        CriminalAction criminalAction = CriminalAction.builder()
                .id(1L)
                .stage(CriminalActionStage.APPEAL_SUPREME_COURT)
                .enforcementDepartment(enforcementDepartment)
                .ourPosition(CriminalActionOurPosition.SUSPECT)
                .attendance(true)
                .prosecutersOfficeId(1L)
                .complaintDate(LocalDate.of(2023, 10, 3))
                .prosecutionNumber("ipms-1")
                .executionDateOfSeizure(LocalDate.of(2023, 10, 3))
                .exOfficioRaid(true)
                .products(List.of(product))
                .deliveryType(CriminalActionDeliveryType.TRUSTEE)
                .trustee(trustee)
                .transfer(true)
                .receivingOffice(null)
                .penaltyDecision(CriminalActionPenaltyDecision.IMPRISONMENT)
                .productsDestructed(true)
                .build();

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CRIMINAL_ACTION)
                .criminalAction(criminalAction)
                .build();

        var departmentDto = EnforcementDepartmentDto.builder()
                .enforcementCity("Ankara")
                .enforcementProvince("Çankaya")
                .enforcementType(EnforcementDepartmentType.GENDARME.name())
                .build();

        var productDto = CriminalActionProductDto.builder()
                .quantity(1)
                .type("test")
                .build();

        var trusteeDto = CriminalActionTrusteeDto.builder()
                .storageFees(true)
                .trusteeName("trustee-1")
                .storageInfo("storage-1")
                .build();

        var criminalActionDto = CriminalActionDto.builder()
                .stage(CourtActionStage.APPEAL_SUPREME_COURT.name())
                .enforcementDepartment(departmentDto)
                .ourPosition(CriminalActionOurPosition.SUSPECT.name())
                .attendance(true)
                .prosecutersOfficeId(1L)
                .complaintDate(LocalDate.of(2023, 10, 3))
                .prosecutionNumber("ipms-1")
                .executionDateOfSeizure(LocalDate.of(2023, 10, 3))
                .exOfficioRaid(true)
                .products(List.of(productDto))
                .deliveryType(CriminalActionDeliveryType.TRUSTEE.name())
                .trustee(trusteeDto)
                .transfer(true)
                .receivingOffice(null)
                .penaltyDecision(CriminalActionPenaltyDecision.IMPRISONMENT.name())
                .productsDestructed(true)
                .build();

        var activityDto = ActivityDto.builder()
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.CRIMINAL_ACTION.name())
                .criminalAction(criminalActionDto)
                .build();

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        var saved = service.save(activityDto);
        assertThat(saved.getId()).isEqualTo(1L);
        assertThat(saved.getCriminalAction().getId()).isEqualTo(1L);
    }

    @Test
    public void givenCriminalAction_whenUpdate_thenReturnActivityDto() {
        var enforcementDepartment = EnforcementDepartment.builder()
                .enforcementCity("Ankara")
                .enforcementProvince("Çankaya")
                .enforcementType(EnforcementDepartmentType.GENDARME)
                .build();

        var product = CriminalActionProduct.builder()
                .id(1L)
                .quantity(1)
                .type("test")
                .build();

        var trustee = CriminalActionTrustee.builder()
                .storageFees(true)
                .trusteeName("trustee-1")
                .storageInfo("storage-1")
                .build();

        CriminalAction criminalAction = CriminalAction.builder()
                .id(1L)
                .stage(CriminalActionStage.APPEAL_SUPREME_COURT)
                .enforcementDepartment(enforcementDepartment)
                .ourPosition(CriminalActionOurPosition.SUSPECT)
                .attendance(true)
                .prosecutersOfficeId(1L)
                .complaintDate(LocalDate.of(2023, 10, 3))
                .prosecutionNumber("ipms-1")
                .executionDateOfSeizure(LocalDate.of(2023, 10, 3))
                .exOfficioRaid(true)
                .products(new ArrayList(List.of(product)))
                .deliveryType(CriminalActionDeliveryType.TRUSTEE)
                .trustee(trustee)
                .transfer(true)
                .receivingOffice(null)
                .penaltyDecision(CriminalActionPenaltyDecision.IMPRISONMENT)
                .productsDestructed(true)
                .build();


        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(List.of(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CRIMINAL_ACTION)
                .criminalAction(criminalAction)
                .build();

        var departmentDto = EnforcementDepartmentDto.builder()
                .enforcementCity("Ankara")
                .enforcementProvince("Çankaya")
                .enforcementType(EnforcementDepartmentType.GENDARME.name())
                .build();

        var productDto = CriminalActionProductDto.builder()
                .quantity(1)
                .type("test")
                .build();

        var trusteeDto = CriminalActionTrusteeDto.builder()
                .storageFees(true)
                .trusteeName("trustee-1")
                .storageInfo("storage-1")
                .build();

        var criminalActionDto = CriminalActionDto.builder()
                .id(1L)
                .stage(CourtActionStage.APPEAL_SUPREME_COURT.name())
                .enforcementDepartment(departmentDto)
                .ourPosition(CriminalActionOurPosition.SUSPECT.name())
                .attendance(true)
                .prosecutersOfficeId(1L)
                .complaintDate(LocalDate.of(2023, 10, 3))
                .prosecutionNumber("ipms-1")
                .executionDateOfSeizure(LocalDate.of(2023, 10, 3))
                .exOfficioRaid(true)
                .products(List.of(productDto))
                .deliveryType(CriminalActionDeliveryType.TRUSTEE.name())
                .trustee(trusteeDto)
                .transfer(true)
                .receivingOffice(null)
                .penaltyDecision(CriminalActionPenaltyDecision.IMPRISONMENT.name())
                .productsDestructed(true)
                .build();


        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.CRIMINAL_ACTION.name())
                .criminalAction(criminalActionDto)
                .build();

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.when(repository.findById(1L))
                .thenReturn(Optional.of(activity));
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        var saved = service.update(activityDto, 1L);
        assertThat(saved.getId()).isEqualTo(1L);
        assertThat(saved.getCriminalAction().getId()).isEqualTo(1L);
    }

    @Test
    public void givenCustomsSuspension_whenSave_thenReturnActivityDto() {
        CustomsSuspension customsSuspension = CustomsSuspension.builder()
                .id(1L)
                .customsId(1L)
                .decisionNumber("test-1")
                .notificationDate(LocalDate.of(2023, 10, 3))
                .build();

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CUSTOMS_SUSPENSION)
                .customsSuspension(customsSuspension)
                .build();

        var customsSuspensionDto = CustomsSuspensionDto.builder()
                .customsId(1L)
                .decisionNumber("test-1")
                .notificationDate(LocalDate.of(2023, 10, 3))
                .build();


        var activityDto = ActivityDto.builder()
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.CUSTOMS_SUSPENSION.name())
                .customsSuspension(customsSuspensionDto)
                .build();

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        var saved = service.save(activityDto);
        assertThat(saved.getId()).isEqualTo(1L);
        assertThat(saved.getCustomsSuspension().getId()).isEqualTo(1L);
    }

    @Test
    public void givenCustomsSuspension_whenUpdate_thenReturnActivityDto() {
        CustomsSuspension customsSuspension = CustomsSuspension.builder()
                .id(1L)
                .customsId(1L)
                .decisionNumber("test-1")
                .notificationDate(LocalDate.of(2023, 10, 3))
                .build();

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(List.of(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CUSTOMS_SUSPENSION)
                .customsSuspension(customsSuspension)
                .build();

        var customsSuspensionDto = CustomsSuspensionDto.builder()
                .id(1L)
                .customsId(1L)
                .decisionNumber("test-1")
                .notificationDate(LocalDate.of(2023, 10, 3))
                .build();


        var activityDto = ActivityDto.builder()
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.CUSTOMS_SUSPENSION.name())
                .customsSuspension(customsSuspensionDto)
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        var saved = service.update(activityDto, 1L);
        assertThat(saved.getId()).isEqualTo(1L);
        assertThat(saved.getCustomsSuspension().getId()).isEqualTo(1L);
    }

    @Test
    public void givenCustomsRecordal_whenSave_thenReturnActivityDto() {
        CustomsRecordal customsRecordal = CustomsRecordal.builder()
                .id(1L)
                .matterType(MatterType.COPYRIGHT)
                .registrationNumber("test-1")
                .renewalDate(LocalDate.of(2023, 10, 3))
                .build();

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CUSTOMS_RECORDAL)
                .customsRecordal(customsRecordal)
                .build();

        var customsRecordalDto = CustomsRecordalDto.builder()
                .id(1L)
                .matterType(MatterType.COPYRIGHT.name())
                .registrationNumber("test-1")
                .renewalDate(LocalDate.of(2023, 10, 3))
                .build();


        var activityDto = ActivityDto.builder()
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.CUSTOMS_SUSPENSION.name())
                .customsRecordal(customsRecordalDto)
                .build();

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        var saved = service.save(activityDto);
        assertThat(saved.getId()).isEqualTo(1L);
        assertThat(saved.getCustomsRecordal().getId()).isEqualTo(1L);
    }

    @Test
    public void givenCustomsRecordal_whenUpdate_thenReturnActivityDto() {
        CustomsRecordal customsRecordal = CustomsRecordal.builder()
                .id(1L)
                .matterType(MatterType.COPYRIGHT)
                .registrationNumber("test-1")
                .renewalDate(LocalDate.of(2023, 10, 3))
                .build();

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(List.of(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CUSTOMS_RECORDAL)
                .customsRecordal(customsRecordal)
                .build();

        var customsRecordalDto = CustomsRecordalDto.builder()
                .id(1L)
                .matterType(MatterType.COPYRIGHT.name())
                .registrationNumber("test-1")
                .renewalDate(LocalDate.of(2023, 10, 3))
                .build();


        var activityDto = ActivityDto.builder()
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.CUSTOMS_SUSPENSION.name())
                .customsRecordal(customsRecordalDto)
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        var saved = service.update(activityDto, 1L);
        assertThat(saved.getId()).isEqualTo(1L);
        assertThat(saved.getCustomsRecordal().getId()).isEqualTo(1L);
    }

    @Test
    public void givenDeterminationAction_whenSave_thenReturnActivityDto() {
        var determinationAction = DeterminationAction.builder()
                .ourPosition(DeterminationActionOurPosition.CLAIMANT)
                .build();

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.DETERMINATION_ACTION)
                .determinationAction(determinationAction)
                .build();

        var determinationActionDto = DeterminationActionDto.builder()
                .ourPosition(DeterminationActionOurPosition.CLAIMANT.name())
                .build();

        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.DETERMINATION_ACTION.name())
                .determinationAction(determinationActionDto)
                .build();

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        assertThat(service.save(activityDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenPreliminaryInjunction_whenSave_thenReturnActivityDto() {
        var preliminaryInjunction = PreliminaryInjunction.builder()
                .types(List.of(PIType.PI_ORDER_FOR_PREVENTION_OF_ASSIGNMENT))
                .guaranteeRefunded(Boolean.TRUE)
                .guaranteeReleased(Boolean.TRUE)
                .refundedAttachments(List.of(RefundedAttachment.builder().id(1L).build()))
                .releasedAttachments(List.of(ReleasedAttachment.builder().id(1L).build()))
                .partyRequestingPIOrder(PIPartyRequestingPIOrder.COUNTER_PARTY)
                .build();

        var preliminaryInjunctionDetail = PreliminaryInjunctionDetail.builder()
                .preliminaryInjunction(preliminaryInjunction)
                .filingDate(LocalDate.now())
                .executionDate(LocalDate.now())
                .executionNo("-")
                .executionOffice("-")
                .executionOpeningDate(LocalDate.now())
                .instructionFileNo("-")
                .notificationDate(LocalDate.now())
                .result(PIResult.EXECUTED)
                .build();

        preliminaryInjunction.setDetails(List.of(preliminaryInjunctionDetail));

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.PRELIMINARY_INJUCTION)
                .preliminaryInjunction(preliminaryInjunction)
                .build();

        var preliminaryInjunctionDto = PreliminaryInjunctionDto.builder()
                .types(List.of(PIType.PI_ORDER_FOR_PREVENTION_OF_ASSIGNMENT.name()))
                .filingDate(LocalDate.now())
                .guaranteeRefunded(Boolean.TRUE)
                .guaranteeReleased(Boolean.TRUE)
                .refundedAttachments(List.of(AttachmentDto.builder().fileName("test-1").build()))
                .releasedAttachments(List.of(AttachmentDto.builder().fileName("test-1").build()))
                .partyRequestingPIOrder(PIPartyRequestingPIOrder.COUNTER_PARTY.name())
                .build();

        var detailDto = PreliminaryInjunctionDetailDto.builder()
                .filingDate(LocalDate.now())
                .executionDate(LocalDate.now())
                .executionNo("test-execution-no")
                .executionOffice("test-execution-office")
                .executionOpeningDate(LocalDate.now())
                .instructionFileNo("test-instruction-filer-no")
                .notificationDate(LocalDate.now())
                .result(PIResult.EXECUTED.name())
                .build();

        preliminaryInjunctionDto.setDetails(List.of(detailDto));

        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.PRELIMINARY_INJUCTION.name())
                .preliminaryInjunction(preliminaryInjunctionDto)
                .build();

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        assertThat(service.save(activityDto).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenPreliminaryInjunction_whenUpdate_thenReturnActivityDto() {
        var preliminaryInjunction = PreliminaryInjunction.builder()
                .types(new ArrayList(List.of(PIType.PI_ORDER_FOR_PREVENTION_OF_ASSIGNMENT)))
                .partyRequestingPIOrder(PIPartyRequestingPIOrder.COUNTER_PARTY)
                .build();

        var preliminaryInjunctionDetail = PreliminaryInjunctionDetail.builder()
                .preliminaryInjunction(preliminaryInjunction)
                .filingDate(LocalDate.now())
                .executionDate(LocalDate.now())
                .executionNo("-")
                .executionOffice("-")
                .executionOpeningDate(LocalDate.now())
                .instructionFileNo("-")
                .notificationDate(LocalDate.now())
                .result(PIResult.EXECUTED)
                .build();

        preliminaryInjunction.setDetails(new ArrayList(List.of(preliminaryInjunctionDetail)));

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.PRELIMINARY_INJUCTION)
                .preliminaryInjunction(preliminaryInjunction)
                .build();

        var preliminaryInjunctionDto = PreliminaryInjunctionDto.builder()
                .types(new ArrayList(List.of(PIType.PI_ORDER_FOR_PREVENTION_OF_ASSIGNMENT.name())))
                .filingDate(LocalDate.now())
                .partyRequestingPIOrder(PIPartyRequestingPIOrder.COUNTER_PARTY.name())
                .build();

        var detailDto = PreliminaryInjunctionDetailDto.builder()
                .filingDate(LocalDate.now())
                .executionDate(LocalDate.now())
                .executionNo("test-execution-no")
                .executionOffice("test-execution-office")
                .executionOpeningDate(LocalDate.now())
                .instructionFileNo("test-instruction-filer-no")
                .notificationDate(LocalDate.now())
                .result(PIResult.EXECUTED.name())
                .build();

        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.PRELIMINARY_INJUCTION.name())
                .preliminaryInjunction(preliminaryInjunctionDto)
                .build();

        preliminaryInjunctionDto.setDetails(List.of(detailDto));

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);

        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());

        assertThat(service.update(activityDto, 1L).getId())
                .isEqualTo(1L);
        }

    @Test
    public void givenActivityDtoAndId_whenUpdate_thenReturnActivityDto() {
        var activity = Activity
                .builder()
                .id(1L)
                .type(ActivityType.USE_INVESTIGATION)
                .issues(new ArrayList(Arrays.asList(1L)))
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);

        var dto = ActivityDto.builder()
                .id(1L)
                .type(ActivityType.USE_INVESTIGATION.name())
                .issues(new ArrayList(Arrays.asList(1L,2L)))
                .build();

        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());
        assertThat(service.update(dto, 1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenCdLetterType_whenUpdate_thenReturnActivityDto() {
        var cdLetter = CDLetter.builder()
                .ourPosition(CDPosition.DRAWEE)
                .date(LocalDate.now())
                .notificationDate(LocalDate.of(2022, 9, 16))
                .notificationType(CDNotificationType.CARGO)
                .officialDocumentNumber("DocumentNumber")
                .notaryNameNumber("NotaryNameNumber")
                .postalTrackingNumber("TrackingNumber")
                .build();
        var cdLetterDto = CDLetterDto.builder()
                .ourPosition(CDPosition.DRAWEE.name())
                .date(LocalDate.now())
                .notificationDate(LocalDate.of(2022, 9, 16))
                .notificationType(CDNotificationType.CARGO.name())
                .officialDocumentNumber("DocumentNumber")
                .notaryNameNumber("NotaryNameNumber")
                .postalTrackingNumber("TrackingNumber")
                .build();
        var activity = Activity.builder()
                .id(1L)
                .version(0L)
                .createdBy("created")
                .createdAt(LocalDateTime.now())
                .updatedBy("updated")
                .updatedAt(LocalDateTime.now())
                .type(ActivityType.CEASE_AND_DESIST_LETTER)
                .status(ActivityStatus.ABANDONMENT)
                .instructionDate(LocalDate.now())
                .firmId(1L)
                .matterId(2L)
                .includedCharge(BigDecimal.TEN)
                .agentReference("Agent Ref")
                .issues(new ArrayList(Arrays.asList(1L, 2L, 3L)))
                .documents(new ArrayList(Arrays.asList(6L, 7L)))
                .evidences(new ArrayList(Arrays.asList(10L)))
                .cdLetter(cdLetter)
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);

        var dto = ActivityDto.builder()
                .id(1L)
                .createdBy("created")
                .updatedBy("updated")
                .version(0L)
                .type(ActivityType.CEASE_AND_DESIST_LETTER.name())
                .status(ActivityStatus.ABANDONMENT.name())
                .instructionDate(LocalDate.now())
                .firmId(1L)
                .matterId(2L)
                .includedCharge(BigDecimal.TEN)
                .agentReference("Agent Ref")
                .issues(new ArrayList(Arrays.asList(1L, 2L, 3L)))
                .documents(new ArrayList(Arrays.asList(6L, 7L)))
                .evidences(new ArrayList(Arrays.asList(10L)))
                .cdLetter(cdLetterDto)
                .build();

        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());

        assertThat(service.update(dto, 1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenActivityWithApprovedBillingStatus_whenUpdateWithoutBillingStatus_thenThrowActivityCannotBeUpdatedException() {
        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .billingStatus(BillingStatus.APPROVED)
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));

        var dto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT.name())
                .matterId(1L)
                .type(ActivityType.CUSTOMS.name())
                .build();

        Throwable thrown = catchThrowable(() -> service.update(dto, 1L));
        assertThat(thrown).isInstanceOf(ActivityCannotBeUpdatedException.class);
    }

    @Test
    public void givenActivityWithNonUpdatableBillingPeriodStatus_whenUpdateWithDifferentBillingPeriodEnds_thenThrowActivityCannotBeUpdatedException() {
        LocalDate originalDate = LocalDate.of(2023, 1, 1);
        LocalDate newDate = LocalDate.of(2023, 2, 1);

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .billingStatus(BillingStatus.APPROVED)
                .billingPeriodEnds(originalDate)
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));

        var dto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT.name())
                .matterId(1L)
                .type(ActivityType.CUSTOMS.name())
                .billingStatus(BillingStatus.APPROVED.name())
                .billingPeriodEnds(newDate)
                .build();

        Throwable thrown = catchThrowable(() -> service.update(dto, 1L));
        assertThat(thrown).isInstanceOf(ActivityCannotBeUpdatedException.class);
    }

    @Test
    public void givenActivityWithUpdatableBillingPeriodStatus_whenUpdateWithDifferentBillingPeriodEnds_thenSuccessfullyUpdate() {
        LocalDate originalDate = LocalDate.of(2023, 1, 1);
        LocalDate newDate = LocalDate.of(2023, 2, 1);

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .billingStatus(BillingStatus.BILLING_STARTED)
                .billingPeriodEnds(originalDate)
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));

        var updatedActivity = activity.toBuilder()
                .billingPeriodEnds(newDate)
                .build();

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(updatedActivity);

        var dto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT.name())
                .matterId(1L)
                .type(ActivityType.CUSTOMS.name())
                .billingStatus(BillingStatus.BILLING_STARTED.name())
                .billingPeriodEnds(newDate)
                .build();

        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());

        var result = service.update(dto, 1L);
        assertThat(result.getBillingPeriodEnds()).isEqualTo(newDate);
    }

    @Test
    public void givenActivityWithUpdatableBillingPeriodEnds_whenUpdateBillingPeriodEnds_thenReturnUpdatedActivity() {
        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .billingStatus(BillingStatus.BILLING_STARTED)
                .billingPeriodEnds(LocalDate.of(2023, 1, 1))
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);

        var dto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT.name())
                .matterId(1L)
                .type(ActivityType.CUSTOMS.name())
                .billingStatus(BillingStatus.BILLING_STARTED.name())
                .billingPeriodEnds(LocalDate.of(2023, 2, 1))
                .build();

        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());

        assertThat(service.update(dto, 1L).getId()).isEqualTo(1L);
    }

    @Test
    public void givenUseInvestigation_whenUpdate_thenReturnActivityDto() {
        var investigationFirm = InvestigationFirm.builder()
                .name("Updated Investigation Firm")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();
        var useInvestigation = UseInvestigation.builder()
                .type(UseInvestigationType.OUTSOURCE)
                .completionDate(LocalDate.now())
                .investigationFirms(Set.of(investigationFirm))
                .build();
        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.USE_INVESTIGATION)
                .useInvestigation(useInvestigation)
                .build();

        var investigationFirmDto = InvestigationFirmDto.builder()
                .name("Updated Investigation Firm")
                .version(0L)
                .emails(List.of("<EMAIL>"))
                .phones(List.of("123456"))
                .faxes(List.of("456789"))
                .build();
        var useInvestigationDto = UseInvestigationDto.builder()
                .type(UseInvestigationType.OUTSOURCE.name())
                .completionDate(LocalDate.now())
                .investigationFirms(List.of(investigationFirmDto))
                .build();

        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.USE_INVESTIGATION.name())
                .useInvestigation(useInvestigationDto)
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);

        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());

        assertThat(service.update(activityDto, 1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenCourtAction_whenUpdate_thenReturnActivityDto() {
        var courtAction = CourtAction.builder()
                .ourPosition(CourtActionOurPosition.DEFENDANT)
                .stage(CourtActionStage.DECISION)
                .types(new ArrayList(Arrays.asList(CourtActionType.NONINFRINGEMENT_ACTION_FILED_BY_PARTY)))
                .counterActions(new ArrayList(Arrays.asList(CourtActionType.PATENT_REQUEST_FOR_DETERMINATION_OF_EVIDENCE)))
                .attendance(Boolean.TRUE)
                .build();

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.COURT_ACTION)
                .courtAction(courtAction)
                .build();

        var courtActionDto = CourtActionDto.builder()
                .ourPosition(CourtActionOurPosition.DEFENDANT.name())
                .stage(CourtActionStage.DECISION.name())
                .types(List.of(CourtActionType.NONINFRINGEMENT_ACTION_FILED_BY_PARTY.name()))
                .counterActions(List.of(CourtActionType.PATENT_REQUEST_FOR_DETERMINATION_OF_EVIDENCE.name()))
                .attendance(Boolean.TRUE)
                .build();

        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(new ArrayList(Arrays.asList(1L)))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.ABANDONMENT.name())
                .type(ActivityType.COURT_ACTION.name())
                .courtAction(courtActionDto)
                .build();

        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);

        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());

        assertThat(service.update(activityDto, 1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenId_whenGetByMatterAndActivityType_thenReturnActivityDtoList() {
        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .build();
        Mockito.when(repository.findAllByMatterIdInAndType(Mockito.anyList(), Mockito.any(ActivityType.class)))
                .thenReturn(List.of(activity));
        assertThat(service.getByMatterAndType(List.of(1L), ActivityType.CUSTOMS_RECORDAL).get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenId_whenGetByMatter_thenReturnActivityDtoList() {
        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .build();
        Mockito.when(repository.findByMatterIdIn(Mockito.anyList(), Mockito.any()))
                .thenReturn(List.of(activity));
        assertThat(service.getByMatter(List.of(1L), "instructionDate", "ASC").get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenId_whenGetByMatterAndType_thenReturnActivityDtoList() {
        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .build();
        Mockito.when(repository.findAllByMatterIdInAndType(Mockito.anyList(), Mockito.any()))
                .thenReturn(List.of(activity));
        assertThat(service.getByMatterAndType(List.of(1L), ActivityType.CUSTOMS_RECORDAL).get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenId_whenGetById_thenReturnActivityDto() {
        var activity = Activity.builder().id(1L).build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        assertThat(service.getById(1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenIssueId_whenGetByIssueId_thenReturnActivityDto() {
        var activity = Activity.builder().id(1L).build();
        Mockito.when(repository.findByIssues(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        assertThat(service.getByIssueId(1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenGetByIdIn_thenReturnDtoList() {
        var activity = Activity.builder()
                .id(1L)
                .build();
        Mockito.when(repository.findByIdIn(Mockito.anyList()))
                .thenReturn(List.of(activity));
        assertThat(service.getByIdIn(List.of(1L,2L)))
                .isNotEmpty();
    }

    @Test
    public void whenGetByIFirmId_thenReturnDtoList() {
        var activity = Activity.builder()
                .id(1L)
                .build();
        Mockito.when(repository.findByFirmId(Mockito.anyLong()))
                .thenReturn(List.of(activity));
        assertThat(service.getByFirmId(1L))
                .isNotEmpty();
    }

    @Test
    public void whenChangeIssue_thenReturnActivityDto() {
        var activity = Activity.builder()
                .id(1L)
                .issues(new ArrayList<>(Arrays.asList(5L)))
                .build();
        Mockito.when(repository.findByIssues(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        assertThat(service.changeIssue(5L, 1050L).getIssues())
                .hasSize(2);
    }

    @Test
    public void whenAddIssue_thenReturnActivityDto() {
        var activity = Activity.builder()
                .id(1L)
                .issues(new ArrayList<>(Arrays.asList(5L)))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        assertThat(service.addIssue(5L, 1050L).getIssues())
                .hasSize(2);
    }

    @Test
    public void whenGetIssuesByFirmId_thenReturnIssueIdList() {
        var activity = Activity.builder()
                .id(1L)
                .matterId(1L)
                .issues(List.of(1L,2L,3L))
                .status(ActivityStatus.COMPLETED)
                .build();
        var activity2 = Activity.builder()
                .id(2L)
                .matterId(2L)
                .issues(List.of(4L,5L,6L))
                .status(ActivityStatus.ABANDONMENT)
                .build();
        var activity3 = Activity.builder()
                .id(2L)
                .matterId(2L)
                .issues(List.of(7L))
                .status(ActivityStatus.ABANDONMENT)
                .build();

        Mockito.when(repository.findByFirmId(Mockito.anyLong()))
                .thenReturn(List.of(activity, activity2));

        MattersResponse response = new MattersResponse();
        response.setPayload(List.of(MatterDto.builder()
                .id(5L)
                .build()));
        Mockito.when(matterClient.getByFirmId(Mockito.anyLong()))
                .thenReturn(response);

        Mockito.when(repository.findByMatterIdIn(List.of(5L)))
                .thenReturn(List.of(activity3));

        assertThat(service.getIssuesByFirmId(1L))
                .isEqualTo(List.of(1L,2L,3L,4L,5L,6L,7L));
    }

    @Test
    public void whenAddDocument_thenReturnActivityDto() {
        var activity = Activity.builder()
                .id(1L)
                .documents(new ArrayList(Arrays.asList(1L)))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        assertThat(service.addDocument(1L,2L, false).getDocuments())
                .hasSize(2);
    }

    @Test
    public void givenCreated_whenAddDocument_thenReturnActivityDto() {
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());

        var activity = Activity.builder()
                .id(1L)
                .documents(new ArrayList(Arrays.asList(1L)))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        assertThat(service.addDocument(1L,2L, true).getDocuments())
                .hasSize(2);
    }

    @Test
    public void whenRemoveDocument_thenReturnActivityDto() {
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());

        var activity = Activity.builder()
                .id(1L)
                .documents(new ArrayList(Arrays.asList(1L, 2L)))
                .build();

        var saved = Activity.builder()
                .id(1L)
                .documents(new ArrayList(Arrays.asList(1L)))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(saved);
        assertThat(service.removeDocument(1L,1L).getDocuments())
                .hasSize(1);
    }

    @Test
    public void whenGetByDocument_thenReturnActivityDto() {
        var activity = Activity.builder()
                .id(1L)
                .documents(new ArrayList(Arrays.asList(1L)))
                .build();
        Mockito.when(repository.findByDocumentsOrderByInstructionDateAsc(Mockito.anyLong()))
                .thenReturn(List.of(activity));

        assertThat(service.getByDocumentId(1L)).hasSize(1);
    }

    @Test
    public void whenRemoveEvidence_thenReturnActivityDto() {
        var activity = Activity.builder()
                .id(1L)
                .evidences(new ArrayList(Arrays.asList(1L, 2L)))
                .build();

        var saved = Activity.builder()
                .id(1L)
                .evidences(new ArrayList(Arrays.asList(1L)))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(saved);
        assertThat(service.removeEvidence(1L,1L).getEvidences())
                .hasSize(1);
    }

    @Test
    public void givenId_whenAddEvidence_thenReturnActivityDto() {
        var activity = Activity.builder()
                .id(1L)
                .evidences(new ArrayList(Arrays.asList(1L)))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        assertThat(service.addEvidence(1L,1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenId_whenGetByEvidenceId_thenReturnActivityDtoList() {
        var activity = Activity.builder()
                .id(1L)
                .evidences(new ArrayList(Arrays.asList(1L)))
                .build();
        Mockito.when(repository.findByEvidencesOrderByInstructionDateDesc(Mockito.anyLong()))
                .thenReturn(List.of(activity));
        assertThat(service.getByEvidenceId(1L).get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenGetAll_thenReturnActivityPageDto() {
        var activity = Activity.builder()
                .id(1L)
                .evidences(new ArrayList(Arrays.asList(1L)))
                .build();
        Page<Activity> page = new Page<>() {
            @Override
            public int getTotalPages() {
                return 1;
            }

            @Override
            public long getTotalElements() {
                return 1;
            }

            @Override
            public <U> Page<U> map(Function<? super Activity, ? extends U> converter) {
                return null;
            }

            @Override
            public int getNumber() {
                return 0;
            }

            @Override
            public int getSize() {
                return 1;
            }

            @Override
            public int getNumberOfElements() {
                return 0;
            }

            @Override
            public List<Activity> getContent() {
                return List.of(activity);
            }

            @Override
            public boolean hasContent() {
                return false;
            }

            @Override
            public Sort getSort() {
                return null;
            }

            @Override
            public boolean isFirst() {
                return false;
            }

            @Override
            public boolean isLast() {
                return false;
            }

            @Override
            public boolean hasNext() {
                return false;
            }

            @Override
            public boolean hasPrevious() {
                return false;
            }

            @Override
            public Pageable nextPageable() {
                return null;
            }

            @Override
            public Pageable previousPageable() {
                return null;
            }

            @Override
            public Iterator<Activity> iterator() {
                return null;
            }
        };

        Mockito.when(repository.findAll(Mockito.any(ActivitySpecification.class), Mockito.any(Pageable.class)))
                .thenReturn(page);
        var filter = ActivityFilterRequest.builder()
                        .types(List.of(ActivityType.CUSTOMS.name()))
                        .statuses(List.of(ActivityStatus.ABANDONMENT.name()))
                        .build();
        assertThat(service.getAll(filter, 0 ,1).getTotalPages())
                .isEqualTo(1L);
    }

    @Test
    public void whenRemoveSample_thenReturnActivityDto() {
        var activity = Activity.builder()
                .id(1L)
                .samples(new ArrayList(Arrays.asList(1L, 2L)))
                .build();

        var saved = Activity.builder()
                .id(1L)
                .samples(new ArrayList(Arrays.asList(1L)))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(saved);
        assertThat(service.removeSample(1L,1L).getSamples())
                .hasSize(1);
    }

    @Test
    public void givenId_whenAddSample_thenReturnActivityDto() {
        var activity = Activity.builder()
                .id(1L)
                .samples(new ArrayList(Arrays.asList(1L)))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        assertThat(service.addSample(1L,1L).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenId_whenGetBySampleId_thenReturnActivityDtoList() {
        var activity = Activity.builder()
                .id(1L)
                .samples(new ArrayList(Arrays.asList(1L)))
                .build();
        Mockito.when(repository.findBySamplesOrderByInstructionDateDesc(Mockito.anyLong()))
                .thenReturn(List.of(activity));
        assertThat(service.getBySampleId(1L).get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void givenDocketNo_whenGetMatterByDocketNo_ThenReturnsCorrectActivityDto() {
        String docketNo = "123";
        ComponentCourt componentCourt = new ComponentCourt();
        Activity activity = Activity.builder()
                .id(1L)
                .build();

        ActivityDto activityDto = new ActivityDto();
        Mockito.when(componentCourtService.getByDocketNo(docketNo)).thenReturn(componentCourt);
        Mockito.when(repository.findById(Mockito.any())).thenReturn(Optional.of(activity));

        ActivityDto result = service.getMatterByDocketNo(docketNo);

        assertThat(activityDto.getMatterId()).isEqualTo(result.getMatterId());
    }

    @Test
    public void givenDocketNo_whenGetMatterByDocketNo_thenReturnsNull() {
        String docketNo = "123";
        Mockito.when(componentCourtService.getByDocketNo(docketNo)).thenReturn(null);

        ActivityDto result = service.getMatterByDocketNo(docketNo);

        assertThat(result).isNull();
    }

    @Test
    public void givenType_whenGetByMatterAndActivityType_thenReturnActivityDtoList() {
        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .type(ActivityType.AGREEMENT)
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .build();
        Mockito.when(repository.findAllByMatterIdInAndType(Mockito.anyList(), Mockito.any(ActivityType.class)))
                .thenReturn(List.of(activity));
        assertThat(service.getByMatterAndType(List.of(1L), ActivityType.CUSTOMS_RECORDAL).get(0).getId())
                .isEqualTo(1L);
    }

    @Test
    public void whenRemoveIssue_thenReturnActivityDto (){
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());

        var activity = Activity.builder()
                .id(1L)
                .issues(new ArrayList(Arrays.asList(1L, 2L)))
                .type(ActivityType.AGREEMENT)
                .build();

        var saved = Activity.builder()
                .id(1L)
                .type(ActivityType.AGREEMENT)
                .issues(new ArrayList(Arrays.asList(1L)))
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(saved);
        assertThat(service.removeIssue(1L,1L).getIssues())
                .hasSize(1);
    }

    @Test
    @DisplayName("When linkBillingAccount with risk management as ADVANCE_PAYMENT_REQUIRED, " +
            "then return activityDto with riskImpact as LOCKED")
    public void whenLinkBillingAccount_AdvancePaymentRequired_thenReturnsRiskImpactAsLocked() {
        // Given
        var activity = Activity.builder()
                .id(1L)
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        var billingAccountResponse = BillingAccountResponse.builder()
                .payload(BillingAccountDto.builder()
                        .id(1L)
                        .riskManagement(BillingAccountDto.RiskManagement.ADVANCE_PAYMENT_REQUIRED)
                        .build())
                .build();
        Mockito.when(billingClient.getById(Mockito.anyLong()))
                .thenReturn(billingAccountResponse);
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);

        // When
        var activityDtoResult = service.linkBillingAccount(1L, 1L);

        // Then
        Mockito.verify(repository).save(activity);
        assertThat(activityDtoResult.getBillingAccountId()).isEqualTo(1L);
        assertThat(activityDtoResult.getRiskImpact())
                .isEqualTo(RiskImpact.LOCKED.name());
    }

    @Test
    @DisplayName("When linkBillingAccount with risk management as HIGH_RISK, " +
            "then return activityDto with riskImpact as LOCKED")
    public void whenLinkBillingAccount_HighRisk_thenReturnsRiskImpactAsLocked() {
        // Given
        var activity = Activity.builder()
                .id(1L)
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        var billingAccountResponse = BillingAccountResponse.builder()
                .payload(BillingAccountDto.builder()
                        .id(1L)
                        .riskManagement(BillingAccountDto.RiskManagement.HIGH_RISK)
                        .build())
                .build();
        Mockito.when(billingClient.getById(Mockito.anyLong()))
                .thenReturn(billingAccountResponse);
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);

        // When
        var activityDtoResult = service.linkBillingAccount(1L, 1L);

        // Then
        Mockito.verify(repository).save(activity);
        assertThat(activityDtoResult.getBillingAccountId()).isEqualTo(1L);
        assertThat(activityDtoResult.getRiskImpact())
                .isEqualTo(RiskImpact.LOCKED.name());
    }

    @Test
    @DisplayName("When linkBillingAccount with risk management as IRREGULAR_PAYER, " +
            "then return activityDto with riskImpact as UNLOCKED")
    public void whenLinkBillingAccount_IrregularPayer_thanReturnsRiskImpactAsUnlocked() {
        // Given
        var activity = Activity.builder()
                .id(1L)
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        var billingAccountResponse = BillingAccountResponse.builder()
                .payload(BillingAccountDto.builder()
                        .id(1L)
                        .riskManagement(BillingAccountDto.RiskManagement.IRREGULAR_PAYER)
                        .build())
                .build();
        Mockito.when(billingClient.getById(Mockito.anyLong()))
                .thenReturn(billingAccountResponse);
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);

        // When
        var activityDtoResult = service.linkBillingAccount(1L, 1L);

        // Then
        Mockito.verify(repository).save(activity);
        assertThat(activityDtoResult.getBillingAccountId()).isEqualTo(1L);
        assertThat(activityDtoResult.getRiskImpact())
                .isEqualTo(RiskImpact.UNLOCKED.name());
    }

    @Test
    @DisplayName("When linkBillingAccount with risk management as NO_RISK, " +
            "then return activityDto with riskImpact as null")
    public void whenLinkBillingAccount_NoRisk_thenReturnsRiskImpactAsNull() {
        // Given
        var activity = Activity.builder()
                .id(1L)
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        var billingAccountResponse = BillingAccountResponse.builder()
                .payload(BillingAccountDto.builder()
                        .id(1L)
                        .riskManagement(BillingAccountDto.RiskManagement.NO_RISK)
                        .build())
                .build();
        Mockito.when(billingClient.getById(Mockito.anyLong()))
                .thenReturn(billingAccountResponse);
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);

        // When
        var activityDtoResult = service.linkBillingAccount(1L, 1L);

        // Then
        Mockito.verify(repository).save(activity);
        assertThat(activityDtoResult.getBillingAccountId()).isEqualTo(1L);
        assertThat(activityDtoResult.getRiskImpact())
                .isNull();
    }

    @Test
    @DisplayName("When unlinkBillingAccount, then return activityDto with billingAccountId as null")
    public void whenUnlinkBillingAccount_thenReturnsBillingAccountIdAsNull() {
        // Given
        var activity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .riskImpact(RiskImpact.LOCKED)
                .build();
        Mockito.when(repository.findById(Mockito.anyLong()))
                .thenReturn(Optional.of(activity));
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);

        // When
        var activityDtoResult = service.unlinkBillingAccount(1L);

        // Then
        Mockito.verify(repository).save(activity);
        assertThat(activityDtoResult.getBillingAccountId()).isNull();
        assertThat(activityDtoResult.getRiskImpact()).isNull();
    }

    @Test
    public void whenGetIdsForTimesheetManuelApproval_thenReturnActivityIds() {
        List<Long> matterIds = List.of(1L, 2L);
        List<ActivityId> activityIds = List.of(new ActivityId(3L), new ActivityId(4L));
        Mockito.when(matterClient.getMatterIdListForSystemApproval())
                .thenReturn(MatterIdsResponse.builder().payload(matterIds).build());
        Mockito.when(repository.findByTypeInAndMatterIdNotIn(ActivityType.TIMESHEET_MANUEL_APPROVALS, matterIds))
                .thenReturn(activityIds);

        List<Long> result = service.getIdsForTimesheetManuelApproval();

        assertThat(result).contains(3L, 4L);
    }

    @Test
    public void whenGetOtherActivityIds_thenReturnActivityIds() {
        var activity1 = Activity.builder()
                .id(1L)
                .matterId(1L)
                .build();
        var activity2 = Activity.builder()
                .id(2L)
                .build();
        Mockito.when(repository.findById(1L)).thenReturn(Optional.ofNullable(activity1));
        Mockito.when(repository.findByMatterIdIn(Mockito.anyList()))
                .thenReturn(List.of(activity1, activity2));

        var otherActivityIds = service.getOtherActivityIds(1L);
        assertThat(otherActivityIds).isEqualTo(List.of(2L));
    }

    @Test
    public void whenGetAllByIssueIds_thenReturnActivityDtos() {
        Mockito.when(repository.findByIssuesIn(List.of(1L, 2L))).thenReturn(Mockito.anyList());

        service.getAllByIssueIds(List.of(1L, 2L));

        Mockito.verify(repository).findByIssuesIn(List.of(1L, 2L));
    }

    @Test
    public void givenActiveActivities_whenUpdateRiskImpact_thenRiskImpactIsUpdatedAndEventIsSent() {
        Long billingAccountId = 123L;

        var activeActivity1 = Activity.builder()
                .id(1L)
                .billingAccountId(123L)
                .status(ActivityStatus.INSTRUCTION)
                .build();

        var activeActivity2 = Activity.builder()
                .id(2L)
                .billingAccountId(123L)
                .status(ActivityStatus.INSTRUCTION)
                .build();

        var inactiveActivity = Activity.builder()
                .id(3L)
                .billingAccountId(123L)
                .status(ActivityStatus.ABANDONED_BY_DERIS)
                .build();

        List<Activity> activities = List.of(activeActivity1, activeActivity2, inactiveActivity);

        Mockito.when(repository.findByBillingAccountId(billingAccountId)).thenReturn(activities);

        service.lockRiskImpactForActiveActivities(billingAccountId);

        Mockito.verify(repository, Mockito.times(1)).save(activeActivity1);
        Mockito.verify(repository, Mockito.times(1)).save(activeActivity2);
        Mockito.verify(repository, Mockito.never()).save(inactiveActivity);
    }

    @Test
    public void givenNoActiveActivities_whenUpdateRiskImpact_thenNoUpdatesOrEventsAreSent() {
        Long billingAccountId = 123L;

        var inactiveActivity1 = Activity.builder()
                .id(1L)
                .billingAccountId(123L)
                .status(ActivityStatus.ABANDONED_BY_DERIS)
                .build();

        var inactiveActivity2 = Activity.builder()
                .id(2L)
                .billingAccountId(123L)
                .status(ActivityStatus.ABANDONED_BY_DERIS)
                .build();

        List<Activity> activities = List.of(inactiveActivity1, inactiveActivity2);

        Mockito.when(repository.findByBillingAccountId(billingAccountId)).thenReturn(activities);

        service.lockRiskImpactForActiveActivities(billingAccountId);

        Mockito.verify(repository, Mockito.never()).save(inactiveActivity1);
        Mockito.verify(repository, Mockito.never()).save(inactiveActivity2);
    }

    @Test
    public void whenGetIssuesByBillingStatuses_thenReturnDistinctIssueIds() {
        var matterIdAndIssues1 = MatterIdAndIssues.builder().issues(1L).build();
        var matterIdAndIssues2 = MatterIdAndIssues.builder().issues(2L).build();
        var matterIdAndIssues3 = MatterIdAndIssues.builder().issues(1L).build();

        List<String> billingStatuses = List.of(
                BillingStatus.APPROVED.name(),
                BillingStatus.ORDER_CREATED.name()
        );

        Mockito.when(repository.findByBillingStatusIn(Mockito.anyList()))
                .thenReturn(List.of(matterIdAndIssues1, matterIdAndIssues2, matterIdAndIssues3));

        List<Long> result = service.getIssuesByBillingStatuses(billingStatuses);

        assertThat(result).containsExactly(1L, 2L);
        Mockito.verify(repository).findByBillingStatusIn(List.of(
                BillingStatus.APPROVED,
                BillingStatus.ORDER_CREATED
        ));
    }

    @Test
    public void whenGetIssuesByMatters_thenReturnDistinctIssueIds() {
        var matterIdAndActivityStatus1 = MatterIdAndActivityStatus.builder().issues(1L).build();
        var matterIdAndActivityStatus2 = MatterIdAndActivityStatus.builder().issues(2L).build();
        var matterIdAndActivityStatus3 = MatterIdAndActivityStatus.builder().issues(1L).build();

        List<Long> matterIds = List.of(1L, 2L);

        Mockito.when(repository.findAllByMatterIdIn(Mockito.anyList()))
                .thenReturn(List.of(matterIdAndActivityStatus1, matterIdAndActivityStatus2, matterIdAndActivityStatus3));

        List<Long> result = service.getIssuesByMatters(matterIds);

        assertThat(result).containsExactly(1L, 2L);
        Mockito.verify(repository).findAllByMatterIdIn(List.of(1L, 2L));
    }

    @Test
    public void whenGetIssuesByMatterTypes_thenReturnDistinctIssueIds() {
        List<String> matterTypes = List.of("OPINION_LITIGATION");
        List<Long> matterIds = List.of(1L, 2L);
        var matterIdAndActivityStatus1 = MatterIdAndActivityStatus.builder().issues(1L).build();
        var matterIdAndActivityStatus2 = MatterIdAndActivityStatus.builder().issues(2L).build();
        var matterIdAndActivityStatus3 = MatterIdAndActivityStatus.builder().issues(1L).build();

        Mockito.when(matterClient.getMatterIdsList(Mockito.any(MatterFilterRequest.class)))
                .thenReturn(MatterIdsResponse.builder().payload(matterIds).build());

        Mockito.when(repository.findAllByMatterIdIn(matterIds))
                .thenReturn(List.of(matterIdAndActivityStatus1, matterIdAndActivityStatus2, matterIdAndActivityStatus3));

        List<Long> result = service.getIssuesByMatterTypes(matterTypes);

        assertThat(result).containsExactly(1L, 2L);
        Mockito.verify(repository).findAllByMatterIdIn(List.of(1L, 2L));
    }

    @Test
    public void givenValidRequest_whenImportFromTurkPatent_thenReturnsUpdatedActivityDto() {

        LocalDate bulletinDate = LocalDate.of(2024, 5, 15);
        String bulletinDateStr = bulletinDate.format(DateTimeFormatter.ofPattern("dd.MM.yyyy"));
        Long matterId = 123L;

        GoodsAndServices goodsAndServices = new GoodsAndServices();
        goodsAndServices.setGoodsAndServiceDtoList(List.of(new GoodsAndServiceDto()));
        var markInformationResponse = MarkInformationResponse.builder()
            .goodsAndServices(goodsAndServices)
                .bulletinNumber("123")
                .bulletinDate(bulletinDateStr)
                .niceClasses("9/35")
                .trademark("TestMark")
                .applicationDate("01.01.2024")
                .applicationNo("2022/20123")
                .logo("base64encodedlogo")
                .build();

        var activityDto = ActivityDto.builder()
                .id(1L)
                .matterId(matterId)
                .type(ActivityType.OPPOSITION_OUT.name())
                .oppositionOut(OppositionOutDto.builder()
                        .bulletinNumber("123")
                        .bulletinPublicationDate(bulletinDate)
                        .build())
                .build();

        var importRequest = ImportFromTpRequest.builder()
                .applicationNumber("2022/20123")
                .activityDto(activityDto)
                .build();

        var activity = Activity.builder()
                .id(1L)
                .matterId(matterId)
                .type(ActivityType.OPPOSITION_OUT)
                .oppositionOut(OppositionOut.builder()
                        .bulletinNumber("123")
                        .bulletinPublicationDate(bulletinDate)
                        .build())
                .build();

        var matterDto = MatterDto.builder()
                .id(1L)
                .mark(MarkDto.builder().isOpposed(true).build())
                .build();
        var matterResponse = MatterResponse.builder()
                .payload(matterDto)
                .build();
        when(matterClient.getById(anyLong())).thenReturn(matterResponse);

        Mockito.when(derisClient.getMarkInformation(Mockito.any(MarkInformationRequestParam.class)))
                .thenReturn(markInformationResponse);
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService)
                .send(Mockito.anyString(), Mockito.anyString());

        var result = service.importFromTurkPatent(importRequest);

        assertThat(result.getId()).isEqualTo(1L);
        assertThat(result.getMatterId()).isEqualTo(matterId);
        assertThat(result.getOppositionOut().getBulletinNumber()).isEqualTo("123");
        assertThat(result.getOppositionOut().getBulletinPublicationDate()).isEqualTo(bulletinDate);
    }

    @Test
    public void givenRenewal_whenSave_thenReturnActivityDto() {
        var renewal = Renewal.builder()
                .id(1L)
                .bulletinNumber("BUL-123")
                .bulletinPublicationDate(LocalDate.now())
                .build();

        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.ABANDONMENT)
                .matterId(1L)
                .type(ActivityType.RENEWAL)
                .renewal(renewal)
                .build();

        var renewalDto = RenewalDto.builder()
                .renewalDate(LocalDate.now())
                .build();

        var activityDto = ActivityDto.builder()
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.PENDING.name())
                .type(ActivityType.RENEWAL.name())
                .renewal(renewalDto)
                .build();

        Mockito.when(renewalService.getRenewalDate(Mockito.any(Activity.class)))
                .thenReturn(LocalDate.now());
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        var saved = service.save(activityDto);
        assertThat(saved.getId()).isEqualTo(1L);
        assertThat(saved.getRenewal().getId()).isEqualTo(1L);
    }

    @Test
    public void givenRegistrationActivityCompleted_whenUpdate_thenCreateRenewalActivity() {
        var existingActivity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .type(ActivityType.REGISTRATION)
                .status(ActivityStatus.OPINION)
                .matterId(1L)
                .issues(new ArrayList<>())
                .build();

        var incomingActivityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .type(ActivityType.REGISTRATION.name())
                .status(ActivityStatus.COMPLETED.name())
                .matterId(1L)
                .build();

        var renewalActivity = Activity.builder()
                .id(2L)
                .type(ActivityType.RENEWAL)
                .matterId(1L)
                .build();

        Mockito.when(repository.findById(1L)).thenReturn(Optional.of(existingActivity));
        Mockito.when(repository.save(Mockito.any(Activity.class))).thenReturn(existingActivity);
        Mockito.when(renewalService.createRenewalActivityFromRegistrationActivity(Mockito.any(Activity.class)))
                .thenReturn(renewalActivity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());

        service.update(incomingActivityDto, 1L);

        Mockito.verify(renewalService, Mockito.times(1))
                .createRenewalActivityFromRegistrationActivity(Mockito.any(Activity.class));
        Mockito.verify(repository, Mockito.times(2)).save(Mockito.any(Activity.class));
    }

    @Test
    public void givenCompletedRegistration_whenSave_thenCreateRenewalActivityFromCalled() {
        var registration = Registration.builder().build();
        var activity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .status(ActivityStatus.COMPLETED)
                .matterId(1L)
                .type(ActivityType.REGISTRATION)
                .registration(registration)
                .build();

        var activityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .issues(List.of(1L))
                .agentReference("test-ref")
                .includedCharge(BigDecimal.ONE)
                .instructionDate(LocalDate.now())
                .matterId(1L)
                .status(ActivityStatus.COMPLETED.name())
                .type(ActivityType.REGISTRATION.name())
                .build();

        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(activity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.when(renewalService.createRenewalActivityFromRegistrationActivity(Mockito.any(Activity.class)))
                .thenReturn(activity);

        var result = service.save(activityDto);

        assertThat(result.getId()).isEqualTo(1L);
        Mockito.verify(repository, Mockito.times(2)).save(Mockito.any(Activity.class));
    }

    @Test
    public void givenRegistrationCompletedWithoutExistingRenewal_whenUpdate_thenCreateNewRenewalActivity() {
        var existingActivity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .type(ActivityType.REGISTRATION)
                .status(ActivityStatus.INSTRUCTION)
                .matterId(1L)
                .issues(new ArrayList<>())
                .build();

        var newRenewalActivity = Activity.builder()
                .id(2L)
                .type(ActivityType.RENEWAL)
                .matterId(1L)
                .build();

        var incomingActivityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .type(ActivityType.REGISTRATION.name())
                .status(ActivityStatus.COMPLETED.name())
                .matterId(1L)
                .build();

        Mockito.when(repository.findById(1L)).thenReturn(Optional.of(existingActivity));
        Mockito.when(repository.save(Mockito.any(Activity.class)))
                .thenReturn(existingActivity)
                .thenReturn(newRenewalActivity);
        Mockito.when(repository.findTopByMatterIdAndType(1L, ActivityType.RENEWAL))
                .thenReturn(Optional.empty());
        Mockito.when(renewalService.createRenewalActivityFromRegistrationActivity(Mockito.any(Activity.class)))
                .thenReturn(newRenewalActivity);
        Mockito.doNothing().when(renewalService).createRenewalHistoryFromActivity(Mockito.any(Activity.class));
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());

        service.update(incomingActivityDto, 1L);

        Mockito.verify(renewalService, Mockito.times(1))
                .createRenewalActivityFromRegistrationActivity(Mockito.any(Activity.class));
        Mockito.verify(renewalService, Mockito.times(1))
                .createRenewalHistoryFromActivity(Mockito.any(Activity.class));
        Mockito.verify(repository, Mockito.times(2)).save(Mockito.any(Activity.class));
    }

    @Test
    public void givenRegistrationCompletedWithExistingRenewal_whenUpdate_thenUseExistingRenewalActivity() {
        var existingActivity = Activity.builder()
                .id(1L)
                .firmId(1L)
                .type(ActivityType.REGISTRATION)
                .status(ActivityStatus.INSTRUCTION)
                .matterId(1L)
                .issues(new ArrayList<>())
                .build();

        var existingRenewalActivity = Activity.builder()
                .id(2L)
                .type(ActivityType.RENEWAL)
                .matterId(1L)
                .build();

        var incomingActivityDto = ActivityDto.builder()
                .id(1L)
                .firmId(1L)
                .type(ActivityType.REGISTRATION.name())
                .status(ActivityStatus.COMPLETED.name())
                .matterId(1L)
                .build();

        Mockito.when(repository.findById(1L)).thenReturn(Optional.of(existingActivity));
        Mockito.when(repository.save(Mockito.any(Activity.class))).thenReturn(existingActivity);
        Mockito.when(repository.findTopByMatterIdAndType(1L, ActivityType.RENEWAL))
                .thenReturn(Optional.of(existingRenewalActivity));
        Mockito.doNothing().when(renewalService).createRenewalHistoryFromActivity(existingRenewalActivity);
        Mockito.doNothing().when(producerService).sendTransfer(Mockito.any(TransferEvent.class));
        Mockito.doNothing().when(producerService).send(Mockito.anyString(), Mockito.anyString());

        service.update(incomingActivityDto, 1L);

        Mockito.verify(renewalService, Mockito.never())
                .createRenewalActivityFromRegistrationActivity(Mockito.any(Activity.class));
        Mockito.verify(renewalService, Mockito.times(1))
                .createRenewalHistoryFromActivity(existingRenewalActivity);
        Mockito.verify(repository, Mockito.times(1)).save(Mockito.any(Activity.class));
    }
}
