package com.ipms.activity.service;

import com.ipms.activity.dto.RenewalHistoryDto;
import com.ipms.activity.exception.RenewalHistoryException;
import com.ipms.activity.mapper.RenewalHistoryMapper;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.Renewal;
import com.ipms.activity.model.RenewalHistory;
import com.ipms.activity.repository.RenewalHistoryRepository;
import com.ipms.activity.enums.RenewalHistoryResponseCode;
import com.ipms.activity.service.impl.RenewalHistoryServiceImpl;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.InjectMocks;
import org.mockito.Mock;
import org.mockito.junit.MockitoJUnitRunner;

import java.util.List;
import java.util.Optional;

import static org.assertj.core.api.Assertions.assertThat;
import static org.assertj.core.api.Assertions.assertThatThrownBy;
import static org.mockito.Mockito.*;

@RunWith(MockitoJUnitRunner.class)
public class RenewalHistoryServiceTest {

    @Mock
    private RenewalHistoryRepository repository;

    @Mock
    private RenewalHistoryMapper mapper;

    @InjectMocks
    private RenewalHistoryServiceImpl service;

    @Before
    public void setUp() {
        service = new RenewalHistoryServiceImpl(repository, mapper);
    }

    @Test
    public void whenGetByRenewalId_thenReturnsMappedDtoList() {
        var renewalHistory = RenewalHistory.builder().id(1L).period(1L).build();
        var dto = RenewalHistoryDto.builder().id(1L).period(1L).build();

        when(repository.findByRenewal_IdOrderByPeriodAsc(10L)).thenReturn(List.of(renewalHistory));
        when(mapper.toDtoList(List.of(renewalHistory))).thenReturn(List.of(dto));

        var result = service.getByRenewalId(10L);

        assertThat(result).hasSize(1);
        assertThat(result.get(0).getId()).isEqualTo(1L);
        verify(repository).findByRenewal_IdOrderByPeriodAsc(10L);
    }

    @Test
    public void whenUpdate_withExistingId_thenUpdatesEntity() {
        var existing = RenewalHistory.builder().id(1L).period(1L).build();
        var updatedEntity = RenewalHistory.builder().id(1L).period(2L).build();
        var dto = RenewalHistoryDto.builder().id(1L).period(2L).build();

        when(repository.findById(1L)).thenReturn(Optional.of(existing));
        when(mapper.toRenewalHistoryFromDto(dto, existing.toBuilder().build())).thenReturn(updatedEntity);
        when(repository.save(updatedEntity)).thenReturn(updatedEntity);
        when(mapper.toDto(updatedEntity)).thenReturn(dto);

        var result = service.update(1L, dto);

        assertThat(result.getPeriod()).isEqualTo(2L);
        verify(repository).save(updatedEntity);
    }

    @Test
    public void whenUpdate_withNonExistingId_thenThrowsException() {
        var dto = RenewalHistoryDto.builder().id(1L).period(2L).build();
        when(repository.findById(1L)).thenReturn(Optional.empty());

        assertThatThrownBy(() -> service.update(1L, dto))
                .isInstanceOf(RenewalHistoryException.class)
                .extracting("code")
                .isEqualTo(RenewalHistoryResponseCode.NOT_FOUND);

        verify(repository, never()).save(any());
    }

    @Test
    public void whenCalculateNextPeriod_thenReturnsCountPlusOne() {
        var renewal = Renewal.builder().id(5L).build();
        var activity = Activity.builder().renewal(renewal).build();

        when(repository.countByRenewal(renewal)).thenReturn(3L);

        var result = service.calculateNextPeriod(activity);

        assertThat(result).isEqualTo(4L);
        verify(repository).countByRenewal(renewal);
    }

    @Test
    public void whenSaveEntity_thenReturnsSavedEntity() {
        var entity = RenewalHistory.builder().id(1L).period(1L).build();
        when(repository.save(entity)).thenReturn(entity);

        var result = service.save(entity);

        assertThat(result.getId()).isEqualTo(1L);
        verify(repository).save(entity);
    }
}
