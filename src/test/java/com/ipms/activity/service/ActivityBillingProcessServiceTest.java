package com.ipms.activity.service;

import com.ipms.activity.client.*;
import com.ipms.activity.dto.*;
import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.enums.BillingStatus;
import com.ipms.activity.exception.*;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.TSICPackage;
import com.ipms.activity.repository.ActivityRepository;
import com.ipms.activity.service.impl.ActivityBillingProcessServiceImpl;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.config.security.utils.SecurityUtils;
import com.ipms.core.entity.Revision;
import com.ipms.core.service.RevisionService;
import org.junit.Before;
import org.junit.Test;
import org.junit.runner.RunWith;
import org.mockito.ArgumentCaptor;
import org.mockito.Mock;
import org.mockito.MockedStatic;
import org.springframework.boot.test.mock.mockito.MockBean;
import org.springframework.test.context.junit4.SpringRunner;
import org.springframework.test.util.ReflectionTestUtils;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;

import static org.assertj.core.api.Assertions.*;
import static org.mockito.ArgumentMatchers.anyLong;
import static org.mockito.Mockito.*;

@RunWith(SpringRunner.class)
public class ActivityBillingProcessServiceTest {

    @MockBean
    private ActivityBillingProcessService activityBillingProcessService;
    @Mock
    private ActivityService activityService;
    @Mock
    private ActivityRepository activityRepository;
    @Mock
    private TSICPackageService tsicPackageService;
    @Mock
    private IssueClient issueClient;
    @Mock
    private BillingClient billingClient;
    @Mock
    private ParamCommandClient paramCommandClient;
    @Mock
    private MatterClient matterClient;
    @Mock
    private ParameterClient parameterClient;
    @Mock
    private ProducerService producerService;
    @Mock
    private RevisionService revisionService;

    @Before
    public void setUp() {
        activityBillingProcessService = new ActivityBillingProcessServiceImpl(activityService, activityRepository,
                tsicPackageService, issueClient, billingClient, paramCommandClient, matterClient,
                parameterClient, producerService, revisionService);
        var autoBillingTimeParam = "AUTO_BILLING_TIME";
        var autoBillingMonthParam = "AUTO_BILLING_MONTH";
        var billingApprovedTopic = "billing-approved-topic";
        var invoiceIsReadyTopic = "invoice-is-ready-topic";
        var closeIssueTopic = "close-issue-topic";
        var beforeBillingDay = 5;

        ReflectionTestUtils.setField(activityBillingProcessService, "autoBillingTimeParam", autoBillingTimeParam);
        ReflectionTestUtils.setField(activityBillingProcessService, "autoBillingMonthParam", autoBillingMonthParam);
        ReflectionTestUtils.setField(activityBillingProcessService, "billingApprovedTopic", billingApprovedTopic);
        ReflectionTestUtils.setField(activityBillingProcessService, "invoiceIsReadyTopic", invoiceIsReadyTopic);
        ReflectionTestUtils.setField(activityBillingProcessService, "closeIssueTopic", closeIssueTopic);
        ReflectionTestUtils.setField(activityBillingProcessService, "beforeBillingDay", beforeBillingDay);

        var billingTimeResponse = ParameterResponse.builder()
                .payload(ParameterResponse.Payload.builder().value("10").build())
                .build();
        when(parameterClient.getByKey(autoBillingTimeParam)).thenReturn(billingTimeResponse);
        var billingMonthResponse = ParameterResponse.builder()
                .payload(ParameterResponse.Payload.builder().value("2").build())
                .build();
        when(parameterClient.getByKey(autoBillingMonthParam)).thenReturn(billingMonthResponse);
    }

    @Test
    public void givenCase1_whenStartBillingProcess_thenCreateBillingIssueByBillingDay() {
        var activity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .tsicPackages(List.of())
                .type(ActivityType.CUSTOMS)
                .build();
        var tsicPackage = TSICPackage.builder()
                .startDate(LocalDate.now())
                .invoiced(false)
                .includedCharge(BigDecimal.TEN)
                .build();
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder()
                        .totalBillableTime(BigDecimal.valueOf(11))
                        .build())
                .build();
        when(tsicPackageService.getActivePackage(anyList())).thenReturn(Optional.of(tsicPackage));
        var billableResponse = getBillableResponse();
        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull()).thenReturn(List.of(activity));
        when(billingClient.isBillable(anyLong(), anyString())).thenReturn(billableResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDate(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDateForFds(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        var booleanResponse = BooleanResponse.builder()
                .payload(true)
                .build();
        when(billingClient.hasBillableDisbursementOrExpense(anyLong())).thenReturn(booleanResponse);
        var holidayResponse = HolidayResponse.builder()
                .payload(List.of())
                .build();
        when(paramCommandClient.getHolidayList()).thenReturn(holidayResponse);
        mockMatterById();
        var parameterResponse = getIssueTypeParameterResponse();
        when(paramCommandClient.getIssueTypeWithDates(anyString(), any(LocalDateTime.class)))
                .thenReturn(parameterResponse);
        activityBillingProcessService.startBillingProcessByBillingDay(LocalDate.now());

        verify(issueClient).save(any(IssueCreateDto.class));
    }

    private IssueTypeParameterResponse getIssueTypeParameterResponse() {
        return new IssueTypeParameterResponse(0,
                IssueTypeParameterResponse.TypeParameter.builder()
                        .targetDateDay(5)
                        .build());
    }

    @Test
    public void givenCase2_whenStartBillingProcess_thenCreateBillingIssueByBillingDay() {
        var activity = Activity.builder()
                .id(1L)
                .matterId(1L)
                .billingAccountId(1L)
                .type(ActivityType.CUSTOMS)
                .tsicPackages(List.of())
                .build();
        var tsicPackage = TSICPackage.builder()
                .startDate(LocalDate.now())
                .invoiced(false)
                .includedCharge(BigDecimal.TEN)
                .build();
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder()
                        .totalBillableTime(BigDecimal.valueOf(11))
                        .build())
                .build();
        when(tsicPackageService.getActivePackage(anyList())).thenReturn(Optional.of(tsicPackage));
        var billableResponse = getBillableResponse();
        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull()).thenReturn(List.of(activity));
        when(billingClient.isBillable(anyLong(), anyString())).thenReturn(billableResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDate(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDateForFds(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        var booleanResponse = BooleanResponse.builder()
                .payload(false)
                .build();
        when(billingClient.hasBillableDisbursementOrExpense(anyLong())).thenReturn(booleanResponse);
        var holidayResponse = HolidayResponse.builder()
                .payload(List.of())
                .build();
        when(paramCommandClient.getHolidayList()).thenReturn(holidayResponse);
        mockMatterById();
        var parameterResponse = getIssueTypeParameterResponse();
        when(paramCommandClient.getIssueTypeWithDates(anyString(), any(LocalDateTime.class)))
                .thenReturn(parameterResponse);

        activityBillingProcessService.startBillingProcessByBillingDay(LocalDate.now());

        verify(issueClient).save(any(IssueCreateDto.class));
    }

    private BillingAccountBillableResponse getBillableResponse() {
        return BillingAccountBillableResponse.builder()
                .payload(BillableBillingAccount.builder()
                        .isBillable(Boolean.TRUE)
                        .billingDay(LocalDate.now())
                        .build())
                .build();
    }

    private void mockMatterById() {
        var matterDto = MatterDto.builder()
                .id(1L)
                .invoiceApproval("nilgun")
                .build();
        var matterResponse = MatterResponse.builder()
                .payload(matterDto)
                .build();
        when(matterClient.getById(anyLong())).thenReturn(matterResponse);
    }

    @Test
    public void givenCase3_whenStartBillingProcess_thenCreateBillingIssueByBillingDay() {
        var activity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .tsicPackages(List.of())
                .build();
        var tsicPackage = TSICPackage.builder()
                .startDate(LocalDate.now())
                .invoiced(false)
                .includedCharge(BigDecimal.TEN)
                .build();
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder()
                        .totalBillableTime(BigDecimal.valueOf(10))
                        .build())
                .build();
        when(tsicPackageService.getActivePackage(anyList())).thenReturn(Optional.of(tsicPackage));
        var billableResponse = getBillableResponse();
        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull()).thenReturn(List.of(activity));
        when(billingClient.isBillable(anyLong(), anyString())).thenReturn(billableResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDate(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDateForFds(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        var booleanResponse = BooleanResponse.builder()
                .payload(false)
                .build();
        when(billingClient.hasBillableDisbursementOrExpense(anyLong())).thenReturn(booleanResponse);
        var holidayResponse = HolidayResponse.builder()
                .payload(List.of())
                .build();
        when(paramCommandClient.getHolidayList()).thenReturn(holidayResponse);
        mockMatterById();
        var parameterResponse = getIssueTypeParameterResponse();
        when(paramCommandClient.getIssueTypeWithDates(anyString(), any(LocalDateTime.class)))
                .thenReturn(parameterResponse);

        activityBillingProcessService.startBillingProcessByBillingDay(LocalDate.now());

        verify(issueClient).save(any(IssueCreateDto.class));
    }

    @Test
    public void givenCase4_whenStartBillingProcess_thenCreateBillingIssueByBillingDay() {
        var activity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .tsicPackages(List.of())
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .build();
        var tsicPackage = TSICPackage.builder()
                .startDate(LocalDate.now())
                .invoiced(false)
                .includedCharge(BigDecimal.TEN)
                .build();
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder()
                        .totalBillableTime(BigDecimal.valueOf(11))
                        .build())
                .build();
        when(tsicPackageService.getActivePackage(anyList())).thenReturn(Optional.of(tsicPackage));
        var billableResponse = getBillableResponse();
        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull()).thenReturn(List.of(activity));
        when(billingClient.isBillable(anyLong(), anyString())).thenReturn(billableResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDate(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDateForFds(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        var booleanResponse = BooleanResponse.builder()
                .payload(false)
                .build();
        when(billingClient.hasBillableDisbursementOrExpense(anyLong())).thenReturn(booleanResponse);
        var holidayResponse = HolidayResponse.builder()
                .payload(List.of())
                .build();
        when(paramCommandClient.getHolidayList()).thenReturn(holidayResponse);
        mockMatterById();
        var parameterResponse = getIssueTypeParameterResponse();
        when(paramCommandClient.getIssueTypeWithDates(anyString(), any(LocalDateTime.class)))
                .thenReturn(parameterResponse);

        activityBillingProcessService.startBillingProcessByBillingDay(LocalDate.now());

        verify(issueClient).save(any(IssueCreateDto.class));
    }

    @Test
    public void givenCase5_whenStartBillingProcess_thenCreateBillingIssueByBillingDay() {
        var activity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .tsicPackages(List.of())
                .build();
        var tsicPackage = TSICPackage.builder()
                .startDate(LocalDate.now())
                .invoiced(true)
                .includedCharge(BigDecimal.valueOf(7))
                .build();
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder()
                        .totalBillableTime(BigDecimal.valueOf(13))
                        .build())
                .build();
        when(tsicPackageService.getActivePackage(anyList())).thenReturn(Optional.of(tsicPackage));
        var billableResponse = getBillableResponse();
        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull()).thenReturn(List.of(activity));
        when(billingClient.isBillable(anyLong(), anyString())).thenReturn(billableResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDate(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDateForFds(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        var booleanResponse = BooleanResponse.builder()
                .payload(true)
                .build();
        when(billingClient.hasBillableDisbursementOrExpense(anyLong())).thenReturn(booleanResponse);
        var holidayResponse = HolidayResponse.builder()
                .payload(List.of())
                .build();
        when(paramCommandClient.getHolidayList()).thenReturn(holidayResponse);
        mockMatterById();
        var parameterResponse = getIssueTypeParameterResponse();
        when(paramCommandClient.getIssueTypeWithDates(anyString(), any(LocalDateTime.class)))
                .thenReturn(parameterResponse);

        activityBillingProcessService.startBillingProcessByBillingDay(LocalDate.now());

        verify(issueClient).save(any(IssueCreateDto.class));
    }

    @Test
    public void givenCase6_whenStartBillingProcess_thenCreateBillingIssueByBillingDay() {
        var activity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .tsicPackages(List.of())
                .type(ActivityType.CUSTOMS)
                .build();
        var tsicPackage = TSICPackage.builder()
                .startDate(LocalDate.now())
                .invoiced(true)
                .includedCharge(BigDecimal.valueOf(7))
                .build();
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder()
                        .totalBillableTime(BigDecimal.valueOf(11))
                        .build())
                .build();
        when(tsicPackageService.getActivePackage(anyList())).thenReturn(Optional.of(tsicPackage));
        var billableResponse = getBillableResponse();
        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull()).thenReturn(List.of(activity));
        when(billingClient.isBillable(anyLong(), anyString())).thenReturn(billableResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDate(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDateForFds(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        var booleanResponse = BooleanResponse.builder()
                .payload(true)
                .build();
        when(billingClient.hasBillableDisbursementOrExpense(anyLong())).thenReturn(booleanResponse);
        var holidayResponse = HolidayResponse.builder()
                .payload(List.of())
                .build();
        when(paramCommandClient.getHolidayList()).thenReturn(holidayResponse);
        mockMatterById();
        var parameterResponse = getIssueTypeParameterResponse();
        when(paramCommandClient.getIssueTypeWithDates(anyString(), any(LocalDateTime.class)))
                .thenReturn(parameterResponse);

        activityBillingProcessService.startBillingProcessByBillingDay(LocalDate.now());

        verify(issueClient).save(any(IssueCreateDto.class));
    }

    @Test
    public void givenCase7_whenStartBillingProcess_thenCreateBillingIssueByBillingDay() {
        var activity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .tsicPackages(List.of())
                .issues(new ArrayList<>())
                .build();
        var tsicPackage = TSICPackage.builder()
                .startDate(LocalDate.now())
                .invoiced(true)
                .includedCharge(BigDecimal.valueOf(7))
                .build();
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder()
                        .totalBillableTime(BigDecimal.valueOf(11))
                        .build())
                .build();
        when(tsicPackageService.getActivePackage(anyList())).thenReturn(Optional.of(tsicPackage));
        var billableResponse = getBillableResponse();
        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull()).thenReturn(List.of(activity));
        when(billingClient.isBillable(anyLong(), anyString())).thenReturn(billableResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDate(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDateForFds(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        var booleanResponse = BooleanResponse.builder()
                .payload(false)
                .build();
        when(billingClient.hasBillableDisbursementOrExpense(anyLong())).thenReturn(booleanResponse);
        var holidayResponse = HolidayResponse.builder()
                .payload(List.of())
                .build();
        when(paramCommandClient.getHolidayList()).thenReturn(holidayResponse);
        mockMatterById();
        var issueResponse = IssueResponse.builder()
                .payload(IssueResponse.Payload.builder().id(1L).build())
                .build();
        when(issueClient.save(any())).thenReturn(issueResponse);
        var parameterResponse = getIssueTypeParameterResponse();
        when(paramCommandClient.getIssueTypeWithDates(anyString(), any(LocalDateTime.class)))
                .thenReturn(parameterResponse);

        activityBillingProcessService.startBillingProcessByBillingDay(LocalDate.now());

        verify(issueClient).save(any(IssueCreateDto.class));
    }

    @Test
    public void givenCase8_whenStartBillingProcess_thenCreateBillingIssueByBillingDay() {
        var activity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .tsicPackages(List.of())
                .build();
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder()
                        .totalBillableTime(BigDecimal.valueOf(11))
                        .build())
                .build();
        when(tsicPackageService.getActivePackage(anyList())).thenReturn(Optional.empty());
        var billableResponse = getBillableResponse();
        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull()).thenReturn(List.of(activity));
        when(billingClient.isBillable(anyLong(), anyString())).thenReturn(billableResponse);
        when(billingClient.getUnbilledTimesByActivityIdAndStartDate(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        when(billingClient.getUnbilledTimesByActivityId(anyLong())).thenReturn(timesheetResponse);
        var booleanResponse = BooleanResponse.builder()
                .payload(false)
                .build();
        when(billingClient.hasBillableDisbursementOrExpense(anyLong())).thenReturn(booleanResponse);
        var holidayResponse = HolidayResponse.builder()
                .payload(List.of())
                .build();
        when(paramCommandClient.getHolidayList()).thenReturn(holidayResponse);
        mockMatterById();
        var parameterResponse = getIssueTypeParameterResponse();
        when(paramCommandClient.getIssueTypeWithDates(anyString(), any(LocalDateTime.class)))
                .thenReturn(parameterResponse);

        activityBillingProcessService.startBillingProcessByBillingDay(LocalDate.now());

        verify(issueClient).save(any(IssueCreateDto.class));
    }

    @Test
    public void whenStartManuelBillingProcess_thenCreateBillingIssue() {
        var activity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .issues(new ArrayList<>())
                .build();
        when(activityService.getActivityById(anyLong())).thenReturn(activity);
        mockMatterById();
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder().build())
                .build();
        when(billingClient.getUnbilledTimesByActivityId(anyLong())).thenReturn(timesheetResponse);
        when(activityRepository.save(any())).thenReturn(activity);
        var issueResponse = IssueResponse.builder()
                .payload(IssueResponse.Payload.builder().id(1L).build())
                .build();
        when(issueClient.save(any())).thenReturn(issueResponse);
        when(billingClient.hasBillableDisbursementOrExpense(anyLong()))
                .thenReturn(BooleanResponse.builder().payload(true).build());
        var holidayResponse = HolidayResponse.builder()
                .payload(List.of())
                .build();
        when(paramCommandClient.getHolidayList()).thenReturn(holidayResponse);
        mockMatterById();
        var parameterResponse = getIssueTypeParameterResponse();
        when(paramCommandClient.getIssueTypeWithDates(anyString(), any(LocalDateTime.class)))
                .thenReturn(parameterResponse);

        activityBillingProcessService.startManuelBillingProcess(1L);

        verify(issueClient).save(any(IssueCreateDto.class));
        verify(activityRepository).save(any(Activity.class));
    }

    @Test
    public void whenStartManuelBillingProcess_thenThrowBillingCannotCreateException() {
        var activity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .issues(new ArrayList<>())
                .tsicPackages(new ArrayList<>())
                .build();
        when(activityService.getActivityById(anyLong())).thenReturn(activity);
        mockMatterById();
        
        // Mock TSIC package service to return empty optional
        when(tsicPackageService.getActivePackage(anyList())).thenReturn(Optional.empty());
        
        // Mock billing client methods to return proper responses
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder().build())
                .build();
        when(billingClient.getUnbilledTimesByActivityId(anyLong())).thenReturn(timesheetResponse);
        
        // Mock hasBillableDisbursementOrExpense to return false (no billable expenses)
        var booleanResponse = BooleanResponse.builder()
                .payload(false)
                .build();
        when(billingClient.hasBillableDisbursementOrExpense(anyLong())).thenReturn(booleanResponse);
        
        when(activityRepository.save(any())).thenReturn(activity);
        var issueResponse = IssueResponse.builder()
                .payload(IssueResponse.Payload.builder().id(1L).build())
                .build();
        when(issueClient.save(any())).thenReturn(issueResponse);

        Throwable thrown = catchThrowable(() -> activityBillingProcessService.startManuelBillingProcess(1L));
        assertThat(thrown).isInstanceOf(BillingCannotCreateException.class);

    }

    @Test
    public void whenGetActivitiesForBillingCart() {
        when(activityService.getIssuesByBillingStatus(anyString())).thenReturn(List.of(1L, 2L));
        var issueListResponse = IssueListResponse.builder()
                .payload(List.of(IssueListResponse.Payload.builder().id(1L).build()))
                .build();
        when(issueClient.getAll(any(IssueFilterRequest.class)))
                .thenReturn(issueListResponse);

        activityBillingProcessService.getActivitiesForBillingCart("test");

        verify(activityService).getAllByIssueIds(List.of(1L));
    }

    @Test
    public void whenProcessOrderIntegrationSuccess_thenReturnNothing() {
        var activity1 = Activity.builder()
                .id(1L)
                .billingStatus(BillingStatus.ORDER_CREATED)
                .build();
        var activity2 = Activity.builder()
                .id(2L)
                .billingStatus(BillingStatus.BILLING_STARTED)
                .build();
        var orderNumber = "test123";
        
        var expenseResponse = ExpenseResponse.builder()
                .payload(List.of(
                    ExpenseResponse.Payload.builder().activityId(1L).build(),
                    ExpenseResponse.Payload.builder().activityId(2L).build()
                ))
                .build();
        when(billingClient.getActivityIdsByOrderNo(orderNumber)).thenReturn(expenseResponse);
        when(activityRepository.findByIdIn(anyList())).thenReturn(List.of(activity1, activity2));
        when(activityRepository.save(any(Activity.class))).thenAnswer(invocation -> invocation.getArgument(0));

        activityBillingProcessService.processOrderIntegrationSuccess(orderNumber);

        assertThat(activity1.getBillingStatus()).isEqualTo(BillingStatus.ORDER_INTEGRATED);
        assertThat(activity2.getBillingStatus()).isEqualTo(BillingStatus.BILLING_STARTED);
    }

    @Test
    public void whenProcessOrderIntegrationError_thenReturnNothing() {
        var activity1 = Activity.builder()
                .id(1L)
                .billingStatus(BillingStatus.ORDER_CREATED)
                .build();
        var activity2 = Activity.builder()
                .id(2L)
                .billingStatus(BillingStatus.BILLING_STARTED)
                .build();
        var orderNumber = "test123";

        var expenseResponse = ExpenseResponse.builder()
                .payload(List.of(
                    ExpenseResponse.Payload.builder().activityId(1L).build(),
                    ExpenseResponse.Payload.builder().activityId(2L).build()
                ))
                .build();
        when(billingClient.getActivityIdsByOrderNo(orderNumber)).thenReturn(expenseResponse);
        when(activityRepository.findByIdIn(anyList())).thenReturn(List.of(activity1, activity2));
        when(activityRepository.save(any(Activity.class))).thenAnswer(invocation -> invocation.getArgument(0));

        activityBillingProcessService.processOrderIntegrationError(orderNumber);

        assertThat(activity1.getBillingStatus()).isEqualTo(BillingStatus.ERROR_IN_ORDER);
        assertThat(activity2.getBillingStatus()).isEqualTo(BillingStatus.BILLING_STARTED);
    }

    @Test
    public void whenProcessInvoiceUpdated_thenReturnNothing() {
        var activity1 = Activity.builder()
                .id(1L)
                .billingStatus(BillingStatus.ORDER_INTEGRATED)
                .billingPeriodEnds(LocalDate.parse("2021-01-01"))
                .build();
        var activity2 = Activity.builder()
                .id(2L)
                .billingStatus(BillingStatus.ORDER_INTEGRATED)
                .billingPeriodEnds(LocalDate.parse("2021-01-01"))
                .build();
        var activity3 = Activity.builder()
                .id(3L)
                .billingStatus(BillingStatus.BILLING_STARTED)
                .billingPeriodEnds(LocalDate.parse("2021-01-01"))
                .build();

        when(activityRepository.findByIdIn(anyList())).thenReturn(List.of(activity1, activity2, activity3));
        when(activityRepository.save(any(Activity.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(billingClient.isInvoiceCompleted(activity1.getId(), activity1.getBillingPeriodEnds()))
                .thenReturn(BooleanResponse.builder().payload(true).build());
        when(billingClient.isInvoiceCompleted(activity2.getId(), activity2.getBillingPeriodEnds()))
                .thenReturn(BooleanResponse.builder().payload(false).build());

        activityBillingProcessService.processInvoiceUpdated(Set.of(1L, 2L));

        assertThat(activity1.getBillingStatus()).isEqualTo(BillingStatus.INVOICE_IS_READY);
        assertThat(activity2.getBillingStatus()).isEqualTo(BillingStatus.ORDER_INTEGRATED);
        assertThat(activity3.getBillingStatus()).isEqualTo(BillingStatus.BILLING_STARTED);
    }

    @Test
    public void whenStartBillingProcessByBillingAmount_thenReturnNothingByBillingDay() {
        var activity1 = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .type(ActivityType.CUSTOMS)
                .build();
        var activity2 = Activity.builder()
                .id(2L)
                .matterId(1L)
                .billingAccountId(2L)
                .type(ActivityType.CUSTOMS)
                .issues(new ArrayList<>())
                .build();

        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull())
                .thenReturn(List.of(activity1, activity2));
        var billingAccountDto1 = BillingAccountDto.builder()
                .id(1L)
                .billingDay("1")
                .build();
        var billingAccountDto2 = BillingAccountDto.builder()
                .id(2L)
                .billingDay("")
                .build();
        var billingAccountListResponse = BillingAccountListResponse.builder()
                .payload(List.of(billingAccountDto1, billingAccountDto2))
                .build();
        when(billingClient.getByIds(Set.of(1L, 2L))).thenReturn(billingAccountListResponse);
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder().totalBillableTime(BigDecimal.TEN).build())
                .build();
        when(billingClient.getUnbilledTimesByActivityIdAndEndDate(anyLong(), any(LocalDate.class)))
                .thenReturn(timesheetResponse);
        when(billingClient.isTotalAmountMoreThanBillingAmount(anyLong()))
                .thenReturn(BooleanResponse.builder().payload(true).build());
        mockMatterById();
        var issueResponse = IssueResponse.builder()
                .payload(IssueResponse.Payload.builder().id(1L).build())
                .build();
        when(issueClient.save(any())).thenReturn(issueResponse);
        var parameterResponse = getIssueTypeParameterResponse();
        when(paramCommandClient.getIssueTypeWithDates(anyString(), any(LocalDateTime.class)))
                .thenReturn(parameterResponse);

        activityBillingProcessService.startBillingProcessByBillingAmount();

        verify(issueClient).save(any(IssueCreateDto.class));
    }

    @Test
    public void whenStartBillingProcessByBillingDayByStatus_thenReturnNothing() {
        var activity1 = Activity.builder()
                .id(1L)
                .updatedAt(LocalDateTime.parse("2021-01-01T00:00:00"))
                .billingAccountId(1L)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .issues(new ArrayList<>())
                .build();
        var activity2 = Activity.builder()
                .id(2L)
                .updatedAt(LocalDateTime.parse("2021-01-02T00:00:00"))
                .billingAccountId(2L)
                .issues(new ArrayList<>())
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .build();

        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNullAndUpdatedAtBetweenAndStatusIn(
                LocalDate.now().minusDays(1).atStartOfDay(),
                LocalDate.now().atStartOfDay(),
                ActivityStatus.AUTO_BILLING_STATUSES)).thenReturn(List.of(activity1, activity2));
        var billingAccountDto1 = BillingAccountDto.builder()
                .id(1L)
                .billingDay("1")
                .build();
        var billingAccountDto2 = BillingAccountDto.builder()
                .id(2L)
                .billingDay("")
                .build();
        var billingAccountListResponse = BillingAccountListResponse.builder()
                .payload(List.of(billingAccountDto1, billingAccountDto2))
                .build();
        when(billingClient.getByIds(Set.of(1L, 2L))).thenReturn(billingAccountListResponse);
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder().totalBillableTime(BigDecimal.TEN).build())
                .build();
        when(billingClient.getUnbilledTimesByActivityId(anyLong()))
                .thenReturn(timesheetResponse);
        when(billingClient.isTotalAmountMoreThanZero(anyLong()))
                .thenReturn(BooleanResponse.builder().payload(true).build());
        mockMatterById();
        var issueResponse = IssueResponse.builder()
                .payload(IssueResponse.Payload.builder().id(1L).build())
                .build();
        when(issueClient.save(any())).thenReturn(issueResponse);
        var parameterResponse = getIssueTypeParameterResponse();
        when(paramCommandClient.getIssueTypeWithDates(anyString(), any(LocalDateTime.class)))
                .thenReturn(parameterResponse);
        var revision1 = Revision.builder()
                .updatedAt(activity1.getUpdatedAt())
                .build();
        var revision2 = Revision.builder()
                .updatedAt(activity2.getUpdatedAt())
                .build();
        when(activityService.getRevisionsByField(any(), anyString())).thenReturn(List.of(revision1, revision2));

        activityBillingProcessService.startBillingProcessByStatus(LocalDate.now().minusDays(1));

        verify(issueClient).save(any(IssueCreateDto.class));
    }

    @Test
    public void whenApproveBilling_thenStatusUpdatedAndEventsTriggered() {
        var issueId = 1L;
        var activity = Activity.builder()
                .id(100L)
                .billingStatus(BillingStatus.BILLING_STARTED)
                .billingPeriodEnds(LocalDate.parse("2024-12-19"))
                .build();

        when(activityService.getActivityByIssueId(issueId)).thenReturn(activity);
        when(activityRepository.save(any(Activity.class))).thenReturn(activity);
        when(billingClient.hasBillableDisbursementOrExpense(anyLong()))
                .thenReturn(BooleanResponse.builder().payload(true).build());

        activityBillingProcessService.approveBilling(issueId);

        assertThat(activity.getBillingStatus()).isEqualTo(BillingStatus.APPROVED);
        verify(activityRepository).save(activity);
        verify(billingClient).approveBilling(any(ApproveBillingRequest.class));
        verify(producerService).send(any(), any());
    }

    @Test
    public void whenConsumeBillingIssueTopic_thenEmptyBillingFields() {
        var issueId = 1L;
        var activity1 = Activity.builder()
                .billingStatus(BillingStatus.BILLING_STARTED)
                .billingPeriodEnds(LocalDate.parse("2024-12-19"))
                .processStartDate(LocalDateTime.parse("2024-12-19T00:00:00"))
                .build();
        var activity2 = Activity.builder()
                .billingStatus(BillingStatus.BILLING_STARTED)
                .billingPeriodEnds(LocalDate.parse("2024-12-19"))
                .processStartDate(LocalDateTime.parse("2024-12-19T00:00:00"))
                .build();
        when(activityRepository.findAllByIssues(issueId)).thenReturn(List.of(activity1, activity2));

        activityBillingProcessService.consumeBillingIssueClosedTopic(issueId);

        verify(activityRepository, times(2)).save(any(Activity.class));
        assertBillingFieldsIsNull(activity1);
        assertBillingFieldsIsNull(activity2);
    }

    private static void assertBillingFieldsIsNull(Activity activity) {
        assertThat(activity.getBillingStatus()).isNull();
        assertThat(activity.getBillingPeriodEnds()).isNull();
        assertThat(activity.getProcessStartDate()).isNull();
    }

    @Test
    public void whenCheckBillingCancellable_thenReturnTrue() {
        when(billingClient.hasAnyBillable(anyLong(), any(LocalDate.class)))
                .thenReturn(getBooleanResponseFalse());
        var activity = Activity.builder()
                .id(1L)
                .billingPeriodEnds(LocalDate.parse("2024-12-19"))
                .build();
        when(activityService.getActivityById(1L)).thenReturn(activity);

        var result = activityBillingProcessService.checkBillingCancellable(1L);

        assertThat(result).isTrue();
    }

    @Test
    public void whenCheckBillingCancellable_thenThrowBillingIssueCannotBeCanceledException() {
        when(billingClient.hasAnyBillable(anyLong(), any(LocalDate.class)))
                .thenReturn(getBooleanResponseTrue());
        var activity = Activity.builder()
                .id(1L)
                .billingPeriodEnds(LocalDate.parse("2024-12-19"))
                .build();
        when(activityService.getActivityById(1L)).thenReturn(activity);

        assertThatThrownBy(() -> activityBillingProcessService.checkBillingCancellable(1L))
                .isInstanceOf(BillingIssueCannotBeCanceledException.class);
    }

    private static BooleanResponse getBooleanResponseTrue() {
        return BooleanResponse.builder()
                .payload(true)
                .build();
    }

    private static BooleanResponse getBooleanResponseFalse() {
        return BooleanResponse.builder()
                .payload(false)
                .build();
    }

    @Test
    public void whenIsBillingOrderCancellationSafe_thenReturnTrue() {
        var orderNo = "test123";
        var activity = Activity.builder()
                .id(1L)
                .billingPeriodEnds(LocalDate.parse("2024-12-19"))
                .billingStatus(BillingStatus.ORDER_INTEGRATED)
                .build();
        
        var expenseResponse = ExpenseResponse.builder()
                .payload(List.of(ExpenseResponse.Payload.builder().activityId(1L).build()))
                .build();
        when(billingClient.getActivityIdsByOrderNo(orderNo)).thenReturn(expenseResponse);
        when(activityRepository.findByIdIn(anyList())).thenReturn(List.of(activity));
        when(billingClient.hasAnyUnapproved(anyLong(), any(LocalDate.class)))
                .thenReturn(getBooleanResponseFalse());

        var result = activityBillingProcessService.isBillingOrderCancellationSafe(orderNo);

        assertThat(result).isTrue();
    }

    @Test
    public void whenIsBillingOrderCancellationSafe_thenReturnFalse() {
        var orderNo = "test123";
        var activity = Activity.builder()
                .id(1L)
                .billingPeriodEnds(LocalDate.parse("2024-12-19"))
                .billingStatus(BillingStatus.ORDER_INTEGRATED)
                .build();
        
        var expenseResponse = ExpenseResponse.builder()
                .payload(List.of(ExpenseResponse.Payload.builder().activityId(1L).build()))
                .build();
        when(billingClient.getActivityIdsByOrderNo(orderNo)).thenReturn(expenseResponse);
        when(activityRepository.findByIdIn(anyList())).thenReturn(List.of(activity));
        when(billingClient.hasAnyUnapproved(anyLong(), any(LocalDate.class)))
                .thenReturn(getBooleanResponseTrue());
        var result = activityBillingProcessService.isBillingOrderCancellationSafe(orderNo);

        assertThat(result).isFalse();
    }

    @Test
    public void whenCancelBillingOrder_thenReturnNothing() {
        var activityId = 1L;
        var activity1 = Activity.builder()
                .id(activityId)
                .billingStatus(BillingStatus.WAITING_IN_BILLING_CART)
                .issues(List.of(1L, 2L))
                .build();
        when(activityRepository.findById(activityId))
                .thenReturn(Optional.of(activity1));

        activityBillingProcessService.cancelBillingOrder(activityId);

        assertThat(activity1.getBillingStatus()).isEqualTo(BillingStatus.APPROVED);
    }

    @Test
    public void whenCancelBillingOrder_thenUpdateBillingStatus() {
        var activityId = 1L;
        var activityOrderIntegrated = Activity.builder()
                .id(activityId)
                .billingStatus(BillingStatus.ORDER_INTEGRATED)
                .billingPeriodEnds(LocalDate.parse("2024-03-19"))
                .build();
        when(activityRepository.findById(activityId))
                .thenReturn(Optional.of(activityOrderIntegrated));
        when(billingClient.hasAnyUnapproved(anyLong(), any(LocalDate.class)))
                .thenReturn(getBooleanResponseTrue());

        activityBillingProcessService.cancelBillingOrder(activityId);

        assertThat(activityOrderIntegrated.getBillingStatus()).isEqualTo(BillingStatus.BILLING_STARTED);
    }

    @Test
    public void cancelBilling_WhenFillEmptyExpenseDatesIsFalse_ShouldOnlySendCloseIssueEvent() {
        var activityId = 1L;
        var issueId = 1L;
        activityBillingProcessService.cancelBilling(activityId, issueId, false);

        verify(activityService, never()).getActivityById(anyLong());
        verify(billingClient, never()).cancelBilling(anyLong(), any());
        verify(producerService).send(any(), anyString());

        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        verify(producerService).send(any(), messageCaptor.capture());
        assertThat(messageCaptor.getValue()).contains("\"issueId\":" + issueId);
    }

    @Test
    public void cancelBilling_WhenFillEmptyExpenseDatesIsTrue_ShouldUpdateDatesAndSendCloseIssueEvent() {
        var activityId = 1L;
        var issueId = 1L;
        var billingPeriodEnds = LocalDate.parse("2024-03-19");
        var activity = Activity.builder()
                .id(activityId)
                .billingPeriodEnds(billingPeriodEnds)
                .tsicPackages(new ArrayList<>())
                .build();
        when(activityService.getActivityById(activityId)).thenReturn(activity);
        when(tsicPackageService.getActivePackage(anyList())).thenReturn(Optional.empty());
        when(billingClient.getUnbilledTimesByActivityId(activityId))
                .thenReturn(TimesheetTotalTimeResponse.builder()
                        .payload(TimesheetTotalTimeResponse.Payload.builder()
                                .tsicRemaining(BigDecimal.TEN)
                                .build())
                        .build());

        activityBillingProcessService.cancelBilling(activityId, issueId, true);

        verify(activityService).getActivityById(activityId);
        verify(billingClient).cancelBilling(activityId, billingPeriodEnds);
        verify(producerService).send(any(), anyString());
    }

    @Test
    public void cancelBilling_WhenActiveFixedFeePackageExists_ShouldProcessNormally() {
        var activityId = 1L;
        var issueId = 1L;
        var billingPeriodEnds = LocalDate.parse("2024-03-19");
        var activity = Activity.builder()
                .id(activityId)
                .billingPeriodEnds(billingPeriodEnds)
                .build();
        when(activityService.getActivityById(activityId)).thenReturn(activity);

        TSICPackage tsicPackage = new TSICPackage();
        tsicPackage.setFixedFee(true);
        when(tsicPackageService.getActivePackage(any())).thenReturn(Optional.of(tsicPackage));

        activityBillingProcessService.cancelBilling(activityId, issueId, true);

        verify(activityService).getActivityById(activityId);
        verify(billingClient).cancelBilling(activityId, billingPeriodEnds);
        verify(producerService).send(any(), anyString());
    }

    @Test
    public void cancelBilling_WhenNonFixedFeeWithTimesExceedingPackage_ShouldThrowException() {
        var activityId = 1L;
        var issueId = 1L;
        var billingPeriodEnds = LocalDate.parse("2024-03-19");
        var activity = Activity.builder()
                .id(activityId)
                .billingPeriodEnds(billingPeriodEnds)
                .build();
        when(activityService.getActivityById(activityId)).thenReturn(activity);

        TSICPackage tsicPackage = new TSICPackage();
        tsicPackage.setFixedFee(false);
        when(tsicPackageService.getActivePackage(any())).thenReturn(Optional.of(tsicPackage));

        when(billingClient.getUnbilledTimesByActivityId(activityId))
                .thenReturn(createTimesheetResponseWithNegativeRemaining());

        assertThatThrownBy(() -> activityBillingProcessService.cancelBilling(activityId, issueId, true))
                .isInstanceOf(BillingCannotBeCanceledException.class);

        verify(activityService).getActivityById(activityId);
        verify(tsicPackageService).getActivePackage(any());
        verify(billingClient).getUnbilledTimesByActivityId(activityId);
        verify(billingClient, never()).cancelBilling(anyLong(), any());
        verify(producerService, never()).send(anyString(), anyString());
    }

    private TimesheetTotalTimeResponse createTimesheetResponseWithNegativeRemaining() {
        TimesheetTotalTimeResponse response = new TimesheetTotalTimeResponse();
        TimesheetTotalTimeResponse.Payload payload = new TimesheetTotalTimeResponse.Payload();
        payload.setTsicRemaining(new BigDecimal("-10.5"));
        response.setPayload(payload);
        return response;
    }

    @Test
    public void cancelBilling_WithNullDisbursements_ShouldNotCallBillingClient() {
        var activityId = 1L;
        var issueId = 1L;
        var billingPeriodEnds = LocalDate.parse("2024-03-19");
        var activity = Activity.builder()
                .id(activityId)
                .billingPeriodEnds(billingPeriodEnds)
                .build();
        when(activityService.getActivityById(activityId)).thenReturn(activity);

        activityBillingProcessService.cancelBilling(activityId, issueId, true);

        verify(activityService).getActivityById(activityId);
        verify(billingClient).cancelBilling(activityId, billingPeriodEnds);
        verify(billingClient, never()).hasAnyUnapproved(anyLong(), any(LocalDate.class));
        verify(producerService).send(any(), anyString());
    }

    @Test
    public void whenUndoBillingApproval_thenShouldSetBillingStatusAndCallClients() {
        var issueId = 1L;
        var activityId = 10L;
        var matterId = 20L;
        var billingPeriodEnds = LocalDate.now();
        var currentUsername = "testUser";
        var rolesList = List.of("ROLE_USER");
        var rolesSet = new HashSet<>(rolesList);
        var invoiceApprover = "approverUser";

        var activity = Activity.builder()
                .id(activityId)
                .matterId(matterId)
                .billingStatus(BillingStatus.APPROVED)
                .billingPeriodEnds(billingPeriodEnds)
                .issues(new ArrayList<>(List.of(issueId))) // Ensure mutable list
                .build();

        var matterDto = MatterDto.builder()
                .id(matterId)
                .invoiceApproval(invoiceApprover)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(matterDto)
                .build();

        when(activityService.getActivityByIssueId(issueId)).thenReturn(activity);
        when(matterClient.getById(matterId)).thenReturn(matterResponse);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getCurrentUsername).thenReturn(Optional.of(currentUsername));
            mockedSecurityUtils.when(SecurityUtils::getRoles).thenReturn(rolesSet);

            activityBillingProcessService.undoBillingApproval(issueId);

            ArgumentCaptor<Activity> activityCaptor = ArgumentCaptor.forClass(Activity.class);
            verify(activityRepository).save(activityCaptor.capture());
            Activity savedActivity = activityCaptor.getValue();
            assertThat(savedActivity.getBillingStatus()).isEqualTo(BillingStatus.REVISION_REQUESTED);

            verify(billingClient).undoBillingApproval(activityId, billingPeriodEnds);

            ArgumentCaptor<AssignmentRequest> assignmentRequestCaptor = ArgumentCaptor.forClass(AssignmentRequest.class);
            verify(issueClient).assign(eq(issueId), assignmentRequestCaptor.capture());
            AssignmentRequest assignmentRequest = assignmentRequestCaptor.getValue();
            assertThat(assignmentRequest.getAssignee()).isEqualTo(currentUsername);
            assertThat(assignmentRequest.getAssigneeRoles()).isEqualTo(rolesList);
        }
    }

    @Test
    public void whenApproveBilling_thenShouldUpdateStatusAndCallClients() {
        var issueId = 1L;
        var activityId = 10L;
        var matterId = 20L;
        var billingAccountId = 30L;
        var billingPeriodEnds = LocalDate.now();
        var agentReference = "AG123";

        var activity = Activity.builder()
                .id(activityId)
                .matterId(matterId)
                .billingAccountId(billingAccountId)
                .billingPeriodEnds(billingPeriodEnds)
                .agentReference(agentReference)
                .billingStatus(BillingStatus.BILLING_STARTED)
                .build();

        when(activityService.getActivityByIssueId(issueId)).thenReturn(activity);
        when(activityRepository.save(any(Activity.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(billingClient.hasBillableDisbursementOrExpense(anyLong()))
                .thenReturn(createBooleanResponse(true));

        activityBillingProcessService.approveBilling(issueId);

        ArgumentCaptor<Activity> activityCaptor = ArgumentCaptor.forClass(Activity.class);
        verify(activityRepository, times(1)).save(activityCaptor.capture());
        Activity savedActivity = activityCaptor.getValue();
        assertThat(savedActivity.getBillingStatus()).isEqualTo(BillingStatus.APPROVED);

        ArgumentCaptor<ApproveBillingRequest> requestCaptor = ArgumentCaptor.forClass(ApproveBillingRequest.class);
        verify(billingClient, times(1)).approveBilling(requestCaptor.capture());

        List<ApproveBillingRequest> capturedRequests = requestCaptor.getAllValues();
        assertThat(capturedRequests).as("Captured requests").hasSize(1);
        assertThat(capturedRequests.get(0).getActivityId()).isEqualTo(activityId);

        ArgumentCaptor<String> topicCaptor = ArgumentCaptor.forClass(String.class);
        ArgumentCaptor<String> messageCaptor = ArgumentCaptor.forClass(String.class);
        verify(producerService, times(1)).send(topicCaptor.capture(), messageCaptor.capture());

        assertThat(topicCaptor.getValue()).isEqualTo("billing-approved-topic");
        String eventJson = messageCaptor.getValue();
        assertThat(eventJson)
                .contains("\"activityId\":" + activityId)
                .contains("\"matterId\":" + matterId)
                .contains("\"billingAccountId\":" + billingAccountId)
                .contains("\"billingPeriodEnds\":\"" + billingPeriodEnds + "\"")
                .contains("\"agentReference\":\"" + agentReference + "\"");
    }

    @Test
    public void whenApproveBillingWithNonApprovableStatus_thenShouldThrowException() {
        var issueId = 1L;
        var activityId = 10L;
        var activity = Activity.builder()
                .id(activityId)
                .billingStatus(BillingStatus.APPROVED)
                .build();

        when(activityService.getActivityByIssueId(issueId)).thenReturn(activity);

        assertThatThrownBy(() -> activityBillingProcessService.approveBilling(issueId))
                .isInstanceOf(BillingCannotBeApprovedException.class);

        verify(activityRepository, never()).save(any(Activity.class));
        verify(billingClient, never()).approveBilling(any(ApproveBillingRequest.class));
        verify(billingClient, never()).approveBilling(any(ApproveBillingRequest.class));
        verify(producerService, never()).send(any(), anyString());
    }

    @Test
    public void whenApproveBillingThrowsException_thenShouldRevertStatusAndRethrow() {
        var issueId = 1L;
        var activityId = 10L;
        var originalStatus = BillingStatus.BILLING_STARTED;

        var activity = spy(Activity.builder()
                .id(activityId)
                .billingStatus(originalStatus)
                .build());

        when(activityService.getActivityByIssueId(issueId)).thenReturn(activity);
        when(activityRepository.save(any(Activity.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(billingClient.hasAnyUnapproved(anyLong(), any(LocalDate.class)))
                .thenReturn(createBooleanResponse(true));
        doThrow(new RuntimeException("Test exception")).when(billingClient).approveBilling(any(ApproveBillingRequest.class));

        assertThatThrownBy(() -> activityBillingProcessService.approveBilling(issueId))
                .isInstanceOf(RuntimeException.class)
                .hasMessage("Test exception");

        verify(activityRepository, times(2)).save(activity);
        verify(activity).setBillingStatus(BillingStatus.APPROVED);
        verify(activity).setBillingStatus(originalStatus);
        assertThat(activity.getBillingStatus()).isEqualTo(originalStatus);
        verify(producerService, never()).send(anyString(), anyString());
    }

    @Test
    public void whenApproveBillingWithNoBillableExpenses_thenShouldThrowNoExpenseRecordToBeInvoicedException() {
        var issueId = 1L;
        var activityId = 10L;
        var originalStatus = BillingStatus.BILLING_STARTED;

        var activity = Activity.builder()
                .id(activityId)
                .billingStatus(originalStatus)
                .build();

        when(activityService.getActivityByIssueId(issueId)).thenReturn(activity);
        when(activityRepository.save(any(Activity.class))).thenAnswer(invocation -> invocation.getArgument(0));
        when(billingClient.hasBillableDisbursementOrExpense(activityId))
                .thenReturn(createBooleanResponse(false));

        assertThatThrownBy(() -> activityBillingProcessService.approveBilling(issueId))
                .isInstanceOf(NoExpenseRecordToBeInvoicedException.class);

        verify(activityRepository, times(2)).save(activity);
        verify(billingClient, times(1)).approveBilling(any(ApproveBillingRequest.class));
        verify(billingClient, times(1)).approveBilling(any(ApproveBillingRequest.class));
        verify(producerService, never()).send(anyString(), anyString());
    }

    @Test
    public void whenUndoBillingApprovalByInvoiceApprover_thenShouldSetBillingStatusToBillingStarted() {
        var issueId = 1L;
        var activityId = 10L;
        var matterId = 20L;
        var billingPeriodEnds = LocalDate.now();
        var currentUsername = "approverUser";
        var rolesList = List.of("ROLE_APPROVER");
        var rolesSet = new HashSet<>(rolesList);
        var invoiceApprover = "approverUser";

        var activity = Activity.builder()
                .id(activityId)
                .matterId(matterId)
                .billingStatus(BillingStatus.APPROVED)
                .billingPeriodEnds(billingPeriodEnds)
                .issues(new ArrayList<>(List.of(issueId))) // Ensure mutable list
                .build();

        var matterDto = MatterDto.builder()
                .id(matterId)
                .invoiceApproval(invoiceApprover)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(matterDto)
                .build();

        when(activityService.getActivityByIssueId(issueId)).thenReturn(activity);
        when(matterClient.getById(matterId)).thenReturn(matterResponse);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getCurrentUsername).thenReturn(Optional.of(currentUsername));
            mockedSecurityUtils.when(SecurityUtils::getRoles).thenReturn(rolesSet);

            activityBillingProcessService.undoBillingApproval(issueId);

            ArgumentCaptor<Activity> activityCaptor = ArgumentCaptor.forClass(Activity.class);
            verify(activityRepository).save(activityCaptor.capture());
            Activity savedActivity = activityCaptor.getValue();
            assertThat(savedActivity.getBillingStatus()).isEqualTo(BillingStatus.BILLING_STARTED);

            verify(billingClient).undoBillingApproval(activityId, billingPeriodEnds);

            ArgumentCaptor<AssignmentRequest> assignmentRequestCaptor = ArgumentCaptor.forClass(AssignmentRequest.class);
            verify(issueClient).assign(eq(issueId), assignmentRequestCaptor.capture());
            AssignmentRequest assignmentRequest = assignmentRequestCaptor.getValue();
            assertThat(assignmentRequest.getAssignee()).isEqualTo(currentUsername);
            assertThat(assignmentRequest.getAssigneeRoles()).isEqualTo(rolesList);
        }
    }

    @Test
    public void whenUndoBillingApprovalWithNoDisbursements_thenShouldSkipBillingClientUndo() {
        var issueId = 1L;
        var activityId = 10L;
        var matterId = 20L;
        var billingPeriodEnds = LocalDate.now();
        var currentUsername = "testUser";
        var rolesList = List.of("ROLE_USER");
        var rolesSet = new HashSet<>(rolesList);
        var invoiceApprover = "approverUser";

        var activity = Activity.builder()
                .id(activityId)
                .matterId(matterId)
                .billingStatus(BillingStatus.APPROVED)
                .billingPeriodEnds(billingPeriodEnds)
                .issues(new ArrayList<>(List.of(issueId)))
                .build();

        var matterDto = MatterDto.builder()
                .id(matterId)
                .invoiceApproval(invoiceApprover)
                .build();

        var matterResponse = MatterResponse.builder()
                .payload(matterDto)
                .build();

        when(activityService.getActivityByIssueId(issueId)).thenReturn(activity);
        when(matterClient.getById(matterId)).thenReturn(matterResponse);

        try (MockedStatic<SecurityUtils> mockedSecurityUtils = mockStatic(SecurityUtils.class)) {
            mockedSecurityUtils.when(SecurityUtils::getCurrentUsername).thenReturn(Optional.of(currentUsername));
            mockedSecurityUtils.when(SecurityUtils::getRoles).thenReturn(rolesSet);

            activityBillingProcessService.undoBillingApproval(issueId);

            verify(activityRepository).save(any(Activity.class));
            verify(billingClient).undoBillingApproval(activityId, billingPeriodEnds);
            verify(issueClient).assign(eq(issueId), any(AssignmentRequest.class));
        }
    }

    @Test
    public void whenHasBillableRecord_thenReturnTrueIfBillableDisbursementOrExpenseExists() {
        var activityId = 1L;
        var activity = Activity.builder()
                .id(activityId)
                .billingPeriodEnds(LocalDate.now())
                .build();

        when(activityService.getActivityById(activityId)).thenReturn(activity);
        when(billingClient.hasBillableExpenseOrDisbursementByBillingPeriodEnds(anyLong(), any(LocalDate.class)))
                .thenReturn(createBooleanResponse(true));

        var result = activityBillingProcessService.hasBillableRecord(activityId);

        assertThat(result).isTrue();
    }

    @Test
    public void whenHasBillableRecord_thenReturnTrueIfBillableTimesheetExists() {
        var activityId = 1L;
        var activity = Activity.builder()
                .id(activityId)
                .billingPeriodEnds(LocalDate.now())
                .build();

        when(activityService.getActivityById(activityId)).thenReturn(activity);
        when(billingClient.hasBillableExpenseOrDisbursementByBillingPeriodEnds(anyLong(), any(LocalDate.class)))
                .thenReturn(createBooleanResponse(false));
        when(billingClient.getUnbilledTimesByActivityId(activityId))
                .thenReturn(TimesheetTotalTimeResponse.builder()
                        .payload(TimesheetTotalTimeResponse.Payload.builder()
                                .totalBillableTime(BigDecimal.TEN)
                                .build())
                        .build());

        var result = activityBillingProcessService.hasBillableRecord(activityId);

        assertThat(result).isTrue();
    }

    @Test
    public void whenHasBillableRecord_thenReturnFalseIfNoExpensesOrDisbursementsAndNoTimesheet() {
        var activityId = 1L;
        var activity = Activity.builder()
                .id(activityId)
                .billingPeriodEnds(LocalDate.now())
                .build();

        when(activityService.getActivityById(activityId)).thenReturn(activity);
        when(billingClient.hasBillableExpenseOrDisbursementByBillingPeriodEnds(anyLong(), any(LocalDate.class)))
                .thenReturn(createBooleanResponse(false));
        when(billingClient.getUnbilledTimesByActivityId(activityId))
                .thenReturn(TimesheetTotalTimeResponse.builder()
                        .payload(TimesheetTotalTimeResponse.Payload.builder()
                                .totalBillableTime(BigDecimal.ZERO)
                                .build())
                        .build());

        var result = activityBillingProcessService.hasBillableRecord(activityId);

        assertThat(result).isFalse();
    }

    private BooleanResponse createBooleanResponse(boolean value) {
        return BooleanResponse.builder().payload(value).build();
    }

    @Test
    public void whenStartBillingProcessByBillingDay_thenFilterOutOppositionOutActivities() {
        var oppositionOutActivity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .type(ActivityType.OPPOSITION_OUT)
                .tsicPackages(List.of())
                .build();
        var normalActivity = Activity.builder()
                .id(2L)
                .billingAccountId(2L)
                .matterId(2L)
                .type(ActivityType.CUSTOMS)
                .tsicPackages(List.of())
                .build();

        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull())
                .thenReturn(List.of(oppositionOutActivity, normalActivity));
        
        var holidayResponse = HolidayResponse.builder()
                .payload(List.of())
                .build();
        when(paramCommandClient.getHolidayList()).thenReturn(holidayResponse);

        activityBillingProcessService.startBillingProcessByBillingDay(LocalDate.now());

        verify(billingClient, never()).isBillable(eq(1L), anyString());
        verify(billingClient, never()).hasBillableDisbursementOrExpense(eq(1L));
    }

    @Test
    public void whenStartBillingProcessByBillingAmount_thenFilterOutOppositionOutActivities() {
        var oppositionOutActivity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .type(ActivityType.OPPOSITION_OUT)
                .build();
        var normalActivity = Activity.builder()
                .id(2L)
                .billingAccountId(2L)
                .matterId(2L)
                .type(ActivityType.CUSTOMS)
                .issues(new ArrayList<>())
                .build();

        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull())
                .thenReturn(List.of(oppositionOutActivity, normalActivity));
        
        var billingAccountDto = BillingAccountDto.builder()
                .id(2L)
                .billingDay("")
                .build();
        var billingAccountListResponse = BillingAccountListResponse.builder()
                .payload(List.of(billingAccountDto))
                .build();
        when(billingClient.getByIds(Set.of(2L))).thenReturn(billingAccountListResponse);

        activityBillingProcessService.startBillingProcessByBillingAmount();

        verify(billingClient, never()).isTotalAmountMoreThanBillingAmount(eq(1L));
        verify(billingClient, never()).getUnbilledTimesByActivityIdAndEndDate(eq(1L), any(LocalDate.class));
    }

    @Test 
    public void whenStartBillingProcessByStatus_thenFilterOutOppositionOutActivities() {
        var oppositionOutActivity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .type(ActivityType.OPPOSITION_OUT)
                .status(ActivityStatus.COMPLETED)
                .updatedAt(LocalDateTime.now())
                .build();
        var normalActivity = Activity.builder()
                .id(2L)
                .billingAccountId(2L)
                .matterId(2L)
                .type(ActivityType.CUSTOMS)
                .status(ActivityStatus.COMPLETED)
                .updatedAt(LocalDateTime.now())
                .issues(new ArrayList<>())
                .build();

        when(activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNullAndUpdatedAtBetweenAndStatusIn(
                any(LocalDateTime.class), any(LocalDateTime.class), any()))
                .thenReturn(List.of(oppositionOutActivity, normalActivity));
        
        when(activityService.getRevisionsByField(eq(2L), eq("status")))
                .thenReturn(List.of(Revision.builder().updatedAt(LocalDateTime.now()).build()));
        
        var billingAccountDto = BillingAccountDto.builder()
                .id(2L)
                .billingDay("")
                .build();
        var billingAccountListResponse = BillingAccountListResponse.builder()
                .payload(List.of(billingAccountDto))
                .build();
        when(billingClient.getByIds(Set.of(2L))).thenReturn(billingAccountListResponse);

        activityBillingProcessService.startBillingProcessByStatus(LocalDate.now());

        verify(billingClient, never()).isTotalAmountMoreThanZero(eq(1L));
        verify(activityService, never()).getRevisionsByField(eq(1L), eq("status"));
    }

    @Test
    public void whenStartManuelBillingProcessWithOppositionOut_thenThrowBillingCannotCreateException() {
        var oppositionOutActivity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .type(ActivityType.OPPOSITION_OUT)
                .issues(new ArrayList<>())
                .tsicPackages(new ArrayList<>())
                .build();

        when(activityService.getActivityById(1L)).thenReturn(oppositionOutActivity);

        Throwable thrown = catchThrowable(() -> activityBillingProcessService.startManuelBillingProcess(1L));
        
        assertThat(thrown).isInstanceOf(BillingCannotCreateException.class);
        verify(issueClient, never()).save(any(IssueCreateDto.class));
    }

    @Test
    public void whenStartManuelBillingProcessWithNormalActivity_thenProceedNormally() {
        var normalActivity = Activity.builder()
                .id(1L)
                .billingAccountId(1L)
                .matterId(1L)
                .type(ActivityType.CUSTOMS)
                .issues(new ArrayList<>())
                .tsicPackages(new ArrayList<>())
                .build();

        when(activityService.getActivityById(1L)).thenReturn(normalActivity);
        when(tsicPackageService.getActivePackage(anyList())).thenReturn(Optional.empty());
        
        var timesheetResponse = TimesheetTotalTimeResponse.builder()
                .payload(TimesheetTotalTimeResponse.Payload.builder()
                        .totalBillableTime(BigDecimal.TEN)
                        .build())
                .build();
        when(billingClient.getUnbilledTimesByActivityId(1L)).thenReturn(timesheetResponse);
        
        var booleanResponse = BooleanResponse.builder()
                .payload(true)
                .build();
        when(billingClient.hasBillableDisbursementOrExpense(1L)).thenReturn(booleanResponse);
        
        mockMatterById();
        var issueResponse = IssueResponse.builder()
                .payload(IssueResponse.Payload.builder().id(1L).build())
                .build();
        when(issueClient.save(any())).thenReturn(issueResponse);
        when(activityRepository.save(any())).thenReturn(normalActivity);
        
        var parameterResponse = getIssueTypeParameterResponse();
        when(paramCommandClient.getIssueTypeWithDates(anyString(), any(LocalDateTime.class)))
                .thenReturn(parameterResponse);
        var holidayResponse = HolidayResponse.builder()
                .payload(List.of())
                .build();
        when(paramCommandClient.getHolidayList()).thenReturn(holidayResponse);

        activityBillingProcessService.startManuelBillingProcess(1L);

        verify(issueClient).save(any(IssueCreateDto.class));
        verify(activityRepository).save(any(Activity.class));
    }

}
