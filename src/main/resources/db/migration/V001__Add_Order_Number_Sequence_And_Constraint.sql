-- Migration to add order number sequence and unique constraint
-- This prevents duplicate order numbers by using atomic sequence generation

-- Step 1: Create sequence for order number generation
-- Start from current max + 1 to avoid conflicts
CREATE SEQUENCE IF NOT EXISTS billing_order_number_seq
    START WITH 1
    INCREMENT BY 1
    NO MAXVALUE
    NO MINVALUE
    CACHE 1;

-- Step 2: Set sequence to start from current max order number + 1
-- Extract numeric part from existing order numbers and set sequence accordingly
DO $$
DECLARE
    max_numeric_part INTEGER := 0;
    current_order_number TEXT;
BEGIN
    -- Get the highest numeric part from existing order numbers
    SELECT order_number INTO current_order_number 
    FROM billing_order 
    WHERE order_number IS NOT NULL 
    ORDER BY id DESC 
    LIMIT 1;
    
    -- If we have existing order numbers, extract numeric part
    IF current_order_number IS NOT NULL THEN
        -- Extract last 6 digits from order number (format: PREFIXYYnnnnnn)
        max_numeric_part := CAST(RIGHT(current_order_number, 6) AS INTEGER);
        -- Set sequence to start from next number
        PERFORM setval('billing_order_number_seq', max_numeric_part + 1, false);
    END IF;
END $$;

-- Step 3: Add unique constraint on order_number column
-- This prevents duplicate order numbers at database level
ALTER TABLE billing_order 
ADD CONSTRAINT uk_billing_order_number UNIQUE (order_number);

-- Step 4: Add index for performance on order_number lookups
CREATE INDEX IF NOT EXISTS idx_billing_order_number 
ON billing_order (order_number) 
WHERE order_number IS NOT NULL;

-- Step 5: Add comment for documentation
COMMENT ON SEQUENCE billing_order_number_seq IS 'Atomic sequence for generating unique billing order numbers';
COMMENT ON CONSTRAINT uk_billing_order_number ON billing_order IS 'Ensures order_number uniqueness across all billing orders';