package com.ipms.firm.service.impl;

import com.ipms.firm.model.LeadAttachment;
import com.ipms.firm.repository.LeadAttachmentRepository;
import com.ipms.firm.service.LeadAttachmentService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@RequiredArgsConstructor
@Service
public class LeadAttachmentServiceImpl implements LeadAttachmentService {
    private final LeadAttachmentRepository repository;

    @Override
    public LeadAttachment save(LeadAttachment attachment) {
        return repository.save(attachment);
    }
}
