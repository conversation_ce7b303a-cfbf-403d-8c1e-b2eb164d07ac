package com.ipms.firm.service.impl;

import com.ipms.firm.exception.CityNotFoundException;
import com.ipms.firm.exception.CountryNotFoundException;
import com.ipms.firm.model.Country;
import com.ipms.firm.repository.CountryRepository;
import com.ipms.firm.service.CountryService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.IteratorUtils;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Optional;

@Service
@RequiredArgsConstructor
public class CountryServiceImpl implements CountryService {
    private final CountryRepository repository;

    @Override
    public List<Country> getAll() {
        var countries=  Optional.of(repository.findAll(Sort.by("name").ascending())).orElseThrow(CountryNotFoundException::new);
        return IteratorUtils.toList(countries.iterator());
    }

    @Override
    public Country getByIso2(String iso2) {
        return repository.findByIso2(iso2).orElseThrow(CityNotFoundException::new);
    }
    
    @Override
    public Country getById(Long id) {
        return repository.findById(id).orElseThrow(CountryNotFoundException::new);
    }
}
