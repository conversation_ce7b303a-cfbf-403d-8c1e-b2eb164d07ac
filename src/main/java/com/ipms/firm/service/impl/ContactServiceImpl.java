package com.ipms.firm.service.impl;

import com.ipms.firm.dto.ContactPageDto;
import com.ipms.firm.exception.ContactNotFoundException;
import com.ipms.firm.mapper.ContactMapper;
import com.ipms.firm.model.Contact;
import com.ipms.firm.repository.ContactRepository;
import com.ipms.firm.service.ContactService;
import com.ipms.firm.service.FirmService;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class ContactServiceImpl implements ContactService {
    private final ContactRepository repository;
    private final FirmService firmService;
    private final ContactMapper mapper;

    @Override
    public Optional<Contact> getOptionalByMailAddress(String mailAddress) {
        return repository.findFirstByMailAddressIgnoreCase(mailAddress);
    }

    @Override
    public void delete(Long id) {
        var contact = getById(id);
        contact.setDeleted(Boolean.TRUE);
        repository.save(contact);
    }

    @Override
    public void delete(Long id, Long firmId) {
        var firm = firmService.getFirmById(firmId);
        var contactSet = firm.getContacts()
                .stream()
                .filter(contact -> !Objects.equals(contact.getId(), id))
                .collect(Collectors.toSet());
        firm.setContacts(contactSet);
        firmService.save(firm);
        var contact = getById(id);
        if (contact.getFirms().isEmpty()) {
            contact.setDeleted(Boolean.TRUE);
        }
        repository.save(contact);
    }

    @Override
    public ContactPageDto getContactListByFirm(Long firmId, int page, int size) {
        var firm = firmService.getFirmById(firmId);
        var contactPage = repository.findByFirmsInOrderByCreatedAtDesc(Set.of(firm), PageRequest.of(page, size));
        return ContactPageDto.builder()
                .contacts(contactPage.getContent()
                .stream()
                .map(mapper::toContactDto)
                .toList())
                .totalElements(contactPage.getTotalElements())
                .totalPages(contactPage.getTotalPages())
                .build();
    }

    @Override
    public Set<Contact> getContactListByIds(List<Long> ids) {
        return repository.findByIdIn(ids);
    }

    @Override
    public List<Contact> getContactListByMailAddress(String mailAddress) {
        return repository.findTop15ByMailAddressContainsOrderByMailAddress(mailAddress.toLowerCase(Locale.ROOT));
    }

    private Contact getById(Long id) {
        return repository.findById(id)
                .orElseThrow(ContactNotFoundException::new);
    }
}
