package com.ipms.firm.service.impl;

import com.ipms.firm.exception.StateNotFoundException;
import com.ipms.firm.model.State;
import com.ipms.firm.repository.StateRepository;
import com.ipms.firm.service.StateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class StateServiceImpl  implements StateService {
    private final StateRepository repository;

    @Override
    public List<State> getByCountry(String countryCode) {
        return repository.findByCountryCodeOrderByNameAsc(countryCode);
    }

    @Override
    public State getByCountryAndIso2(String countryCode,String iso2) {
        return repository.findByCountryCodeAndIso2(countryCode,iso2).orElseThrow(StateNotFoundException::new);
    }
}
