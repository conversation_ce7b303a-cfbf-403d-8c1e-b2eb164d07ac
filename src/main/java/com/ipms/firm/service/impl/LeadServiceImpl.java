package com.ipms.firm.service.impl;

import com.ipms.firm.enums.LeadResponseCode;
import com.ipms.firm.exception.LeadException;
import com.ipms.firm.model.Lead;
import com.ipms.firm.repository.LeadRepository;
import com.ipms.firm.service.LeadService;
import com.ipms.firm.specification.LeadSpecification;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.IteratorUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class LeadServiceImpl implements LeadService {
    private final LeadRepository repository;

    @Override
    public List<Lead> getAll() {
        return IteratorUtils.toList(repository.findAll().iterator());
    }

    @Override
    public Lead getById(Long id) {
        return repository.findById(id).orElseThrow(() -> new LeadException(LeadResponseCode.LEAD_NOT_FOUND));
    }

    @Override
    public Lead save(Lead lead) {
        return repository.save(lead);
    }

    @Override
    public void delete(Long id) {
        var lead = repository.findById(id).orElseThrow(() -> new LeadException(LeadResponseCode.LEAD_NOT_FOUND))
                .toBuilder()
                .isDeleted(true)
                .build();
        repository.save(lead);
    }

    @Override
    public void checkByNameAndPhoneNumber(String nameSurname, String phoneNumber) {
        repository.findByNameSurnameAndPhoneNumber(nameSurname, phoneNumber)
                .ifPresent(lead -> {throw new LeadException(LeadResponseCode.LEAD_ALREADY_EXIST);});
    }

    @Override
    public Page<Lead> getLeadPage(LeadSpecification spec, PageRequest pageable) {
        return repository.findAll(spec, pageable);
    }
}
