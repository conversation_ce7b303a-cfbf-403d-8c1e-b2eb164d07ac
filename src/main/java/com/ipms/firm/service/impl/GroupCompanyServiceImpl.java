package com.ipms.firm.service.impl;

import com.ipms.firm.enums.FirmValidationResponseCode;
import com.ipms.firm.exception.FirmValidationException;
import com.ipms.firm.exception.GroupCompanyNotFound;
import com.ipms.firm.model.GroupCompany;
import com.ipms.firm.repository.GroupCompanyRepository;
import com.ipms.firm.service.GroupCompanyService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.IteratorUtils;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class GroupCompanyServiceImpl implements GroupCompanyService {

    private final GroupCompanyRepository repository;

    @Override
    public GroupCompany getById(Long id) {
        return repository.findById(id).orElseThrow(GroupCompanyNotFound::new);
    }

    @Override
    public List<GroupCompany> getAll() {
        return IteratorUtils.toList(repository.findAll().iterator());
    }

    @Override
    public void checkByName(String name, Long id) {
        repository.findByNameIgnoreCase(name)
                .ifPresent(groupCompany -> {
                    if (!groupCompany.getId().equals(id)) {
                        throw new FirmValidationException(FirmValidationResponseCode.GROUP_COMPANY_ALREADY_EXIST);
                    }
                });
    }

    @Override
    public List<GroupCompany> getByName(String name) {
        return repository.findByNameContainsOrderByName(name);
    }


}
