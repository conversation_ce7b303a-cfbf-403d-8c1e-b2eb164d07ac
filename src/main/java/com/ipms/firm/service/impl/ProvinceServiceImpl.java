package com.ipms.firm.service.impl;

import com.ipms.firm.exception.ProvinceNotFoundException;
import com.ipms.firm.model.Province;
import com.ipms.firm.repository.ProvinceRepository;
import com.ipms.firm.service.ProvinceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@Service
@RequiredArgsConstructor
public class ProvinceServiceImpl implements ProvinceService {
    private final ProvinceRepository repository;

    @Override
    public List<Province> getByCountryAndCity(String countryCode, String cityCode) {
        return repository.findByCountryCodeAndCityCodeOrderByNameAsc(countryCode,cityCode);
    }

    @Override
    public Province getByCountryAndCityAndProvince(String countryCode, String cityCode, String provinceName) {
        return repository.findByCountryCodeAndCityCodeAndName(countryCode,cityCode,provinceName).orElseThrow(ProvinceNotFoundException::new);
    }

    @Override
    public Province getProvince(String countryCode, String cityCode, String provinceName) {
        return repository.findByCountryCodeAndCityCodeAndName(countryCode,cityCode,provinceName).orElse(null);
    }

}
