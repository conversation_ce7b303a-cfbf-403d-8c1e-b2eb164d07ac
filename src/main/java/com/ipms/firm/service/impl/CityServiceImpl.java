package com.ipms.firm.service.impl;

import com.ipms.firm.exception.CityInUseException;
import com.ipms.firm.exception.CityNotFoundException;
import com.ipms.firm.model.City;
import com.ipms.firm.repository.CityRepository;
import com.ipms.firm.service.CityService;
import com.ipms.firm.service.FirmService;
import lombok.Builder;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class CityServiceImpl  implements CityService {
    private final CityRepository repository;
    private final FirmService firmService;

    @Override
    public List<City> getByCountry(String countryCode) {
        return repository.findByCountryCodeOrderByNameAsc(countryCode);
    }

    @Override
    public List<City> getByCountryAndState(String countryCode, String stateCode) {
        return repository.findByCountryCodeAndStateCodeOrderByNameAsc(countryCode,stateCode);
    }

    @Override
    public City getByCountryAndIso2(String countryCode,String iso2) {
        return repository.findByCountryCodeAndIso2(countryCode,iso2).orElseThrow(CityNotFoundException::new);
    }

    @Override
    public List<City> getByIso2List(List<String> iso2) {
        return repository.findByIso2In(iso2);
    }

    @Override
    public Map<String, City> getByIso2Map(List<String> iso2) {
        return getByIso2List(iso2).stream()
                .collect(Collectors.toMap(city -> city.getCountryCode() + city.getIso2(), Function.identity()));
    }

    @Override
    public List<City> getByIso2AndCountryCode(String iso2, String countryCode) {
        return repository.findByIso2AndCountryCode(iso2, countryCode);
    }

    @Override
    public City save(City city) {
        return repository.save(city);
    }

    @Override
    public void delete(Long id) {
        var city = getById(id);
        var firmList = firmService.getByCityAndCountry(city.getIso2(), city.getCountryCode());
        if (!firmList.isEmpty()) {
            throw new CityInUseException();
        }
        city.setDeleted(Boolean.TRUE);
        repository.save(city);
    }

    private City getById(Long id) {
        return repository.findById(id)
                .orElseThrow(CityNotFoundException::new);
    }
}
