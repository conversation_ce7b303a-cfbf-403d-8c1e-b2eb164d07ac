package com.ipms.firm.service.impl;

import com.ipms.firm.exception.SectorNotFoundException;
import com.ipms.firm.model.Sector;
import com.ipms.firm.repository.SectorRepository;
import com.ipms.firm.service.SectorService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.IteratorUtils;
import org.springframework.stereotype.Service;

import java.util.List;
import java.util.Set;

@Service
@RequiredArgsConstructor
public class SectorServiceImpl implements SectorService {
    private final SectorRepository repository;

    @Override
    public Sector getById(Long id) {
        return repository.findById(id)
                .orElseThrow(SectorNotFoundException::new);
    }

    @Override
    public Set<Sector> getByIdList(List<Long> idList) {
        return repository.findByIdIn(idList);
    }

    @Override
    public List<Sector> getAll() {
        return IteratorUtils.toList(repository.findAll().iterator());
    }
}
