package com.ipms.firm.service.impl;

import com.ipms.firm.exception.AgentInfoNotFoundException;
import com.ipms.firm.model.AgentInfo;
import com.ipms.firm.repository.AgentInfoRepository;
import com.ipms.firm.service.AgentInfoService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class AgentInfoServiceImpl implements AgentInfoService {

    private final AgentInfoRepository repository;

    @Override
    public AgentInfo getById(Long id) {
        return repository.findById(id).orElseThrow(AgentInfoNotFoundException::new);
    }

    @Override
    public AgentInfo save(AgentInfo agentInfo) {
        return repository.save(agentInfo);
    }
}
