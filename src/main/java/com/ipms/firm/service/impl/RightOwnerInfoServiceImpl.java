package com.ipms.firm.service.impl;

import com.ipms.firm.exception.RightOwnerNotFoundException;
import com.ipms.firm.model.RightOwnerInfo;
import com.ipms.firm.repository.RightOwnerRepository;
import com.ipms.firm.service.RightOwnerService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

@Service
@RequiredArgsConstructor
public class RightOwnerInfoServiceImpl implements RightOwnerService {

    private final RightOwnerRepository repository;

    @Override
    public RightOwnerInfo getById(Long id) {
        return repository.findById(id).orElseThrow(RightOwnerNotFoundException::new);
    }

    @Override
    public RightOwnerInfo save(RightOwnerInfo rightOwnerInfo) {
        return repository.save(rightOwnerInfo);
    }
}
