package com.ipms.firm.service.impl;

import com.ipms.firm.client.ActivityClient;
import com.ipms.firm.client.MatterClient;
import com.ipms.firm.dto.FirmDto;
import com.ipms.firm.dto.FirmEvent;
import com.ipms.firm.dto.FirmFilterRequest;
import com.ipms.firm.enums.FirmRole;
import com.ipms.firm.enums.FirmValidationResponseCode;
import com.ipms.firm.exception.FirmNotFoundException;
import com.ipms.firm.exception.FirmValidationException;
import com.ipms.firm.model.Firm;
import com.ipms.firm.model.GroupCompany;
import com.ipms.firm.repository.FirmRepository;
import com.ipms.firm.service.AgentInfoService;
import com.ipms.firm.service.FirmService;
import com.ipms.firm.service.GroupCompanyService;
import com.ipms.firm.service.RightOwnerService;
import com.ipms.firm.specification.FirmSpecification;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.StringUtils;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class FirmServiceImpl implements FirmService {

    private final FirmRepository repository;

    private final GroupCompanyService groupCompanyService;

    private final ActivityClient activityClient;

    private final AgentInfoService agentInfoService;

    private final RightOwnerService rightOwnerService;

    private final MatterClient matterClient;

    @Override
    public Firm save(Firm firm) {
        return repository.save(firm);
    }

    @Override
    public List<Firm> getAll(FirmFilterRequest filterRequest) {
        return repository.findAll(mapSpecification(filterRequest));
    }

    private FirmSpecification mapSpecification(FirmFilterRequest filterRequest) {
        return FirmSpecification.builder()
                .prospect(filterRequest.getProspect())
                .billingAccountId(filterRequest.getBillingAccountId())
                .sortDirection(filterRequest.getSortDirection())
                .sortField(filterRequest.getSortField())
                .build();
    }

    @Override
    public void checkByTitleAndCountryAndCity(FirmDto firmDto) {
        repository.findByTitleIgnoreCaseAndCountryAndCity(firmDto.getTitle().trim(), firmDto.getCountry(), firmDto.getCity())
                .ifPresent(firm -> {
                    if (!firm.getId().equals(firmDto.getId())) {
                        throw new FirmValidationException(FirmValidationResponseCode.FIRM_ALREADY_EXIST);
                    }
                });
    }

    @Override
    public void delete(Long id, Long version) {
        var firm = getFirmById(id).toBuilder()
                .version(version)
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(firm);
    }

    @Override
    public List<Firm> getFirmListByGroupCompany(Long groupCompanyId) {
        return repository.findByGroupCompanyIdOrderByTitle(groupCompanyId);
    }

    @Override
    public List<Firm> getFirmListByTitleAndRole(String title, List<FirmRole> roles) {
        return repository.findTop30ByTitleContainsIgnoreCaseAndRoleInOrderByTitle(title, roles);
    }

    @Override
    public List<Firm> getFirmListByTitle(String title) {
        return repository.findTop30ByTitleContainsIgnoreCaseOrderByTitle(title);
    }

    @Override
    public List<Firm> getByIdIn(List<Long> ids) {
        return repository.findByIdIn(ids);
    }

    @Override
    public Firm updateGroupCompany(Long id, GroupCompany groupCompany, Long version) {
        if (StringUtils.isBlank(groupCompany.getName()) && groupCompany.getId() == null) {
            groupCompany = null;
        } else if (groupCompany.getId() != null) {
            groupCompany = groupCompanyService.getById(groupCompany.getId());
        } else if (groupCompany.getName() != null) {
            groupCompanyService.checkByName(groupCompany.getName(), groupCompany.getId());
            groupCompany.setName(groupCompany.getName());
        }
        var firm = getFirmById(id).toBuilder()
                .version(version)
                .groupCompany(groupCompany)
                .build();
        return repository.save(firm);
    }

    @Override
    public void checkTitleForMigration(FirmDto firmDto) {
        var firmList = repository.findByTitle(firmDto.getTitle().trim());
        if (!firmList.isEmpty()) {
            firmList.forEach(firm -> {
                if (firm.getCountry().equals("US")) {
                    if (StringUtils.isBlank(firm.getCity()) || StringUtils.isBlank(firm.getState())) {
                        throw new FirmValidationException(FirmValidationResponseCode.DUPLICATE_RECORD);
                    }
                } else {
                    if (StringUtils.isBlank(firm.getCity())) {
                        throw new FirmValidationException(FirmValidationResponseCode.DUPLICATE_RECORD);
                    }
                }
            });
        }
    }

    @Override
    public void deleteAgent(Long id, Long version) {
        var firm = getByAgentId(id);
        if (firm.getRightOwnerInfo() == null) {
            throw new FirmValidationException(FirmValidationResponseCode.AGENT_AND_RIGHT_OWNER_CAN_NOT_NULL_TOGETHER);
        }
        activityClient.getByFirmId(firm.getId()).used()
                .ifPresent(used -> {
                    throw new FirmValidationException(FirmValidationResponseCode.AGENT_ROLE_CAN_NOT_BE_REMOVED);
                });
        firm.setAgentInfo(null);
        firm.setRole(FirmRole.RIGHT_OWNER);
        var agentInfo = agentInfoService.getById(id);
        agentInfo.setDeleted(Boolean.TRUE);
        agentInfo.setVersion(version);
        save(firm);
        agentInfoService.save(agentInfo);
    }

    @Override
    public Firm getByAgentId(Long id) {
        return repository.findByAgentInfo_Id(id).orElseThrow(FirmNotFoundException::new);
    }

    @Override
    public Firm getByRightOwnerId(Long id) {
        return repository.findByRightOwnerInfo_Id(id).orElseThrow(FirmNotFoundException::new);
    }

    @Override
    public void deleteRightOwner(Long id, Long version) {
        var firm = getByRightOwnerId(id);
        if(firm.getAgentInfo() == null) {
            throw new FirmValidationException(FirmValidationResponseCode.AGENT_AND_RIGHT_OWNER_CAN_NOT_NULL_TOGETHER);
        }
        matterClient.getByFirmId(firm.getId()).used()
                .ifPresent(used -> {
                    throw new FirmValidationException(FirmValidationResponseCode.RIGHT_OWNER_ROLE_CAN_NOT_BE_REMOVED);
                });
        firm.setRightOwnerInfo(null);
        firm.setRole(FirmRole.AGENT);
        var rightOwner = rightOwnerService.getById(id);
        rightOwner.setDeleted(Boolean.TRUE);
        rightOwner.setVersion(version);
        save(firm);
        rightOwnerService.save(rightOwner);
    }

    @Override
    public List<Firm> getByCityAndCountry(String city, String country) {
        return repository.findByCityAndCountry(city, country);
    }

    @Override
    public Firm getFirmById(Long id) {
        return repository.findById(id).orElseThrow(FirmNotFoundException::new);
    }

    @Override
    public void processAssociatedEvent(FirmEvent event) {
        var firms = repository.findByIdIn(event.getIds());
        firms.forEach(firm -> firm.setProspect(false));
        repository.saveAll(firms);
    }

    @Override
    public Page<Firm> getFirmPage(FirmFilterRequest filterRequest, int page, int size) {
        return repository.findAll(mapSpecification(filterRequest), PageRequest.of(page, size));
    }

    @Override
    public Firm linkBillingAccount(Long id, Long billingAccountId) {
        var firm = getFirmById(id);
        if (!firm.getBillingAccountIds().contains(billingAccountId)) {
            firm.getBillingAccountIds().add(billingAccountId);
        }
        return repository.save(firm);
    }
}
