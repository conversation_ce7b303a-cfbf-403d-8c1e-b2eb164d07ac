package com.ipms.firm.service;


import com.ipms.firm.dto.ContactPageDto;
import com.ipms.firm.model.Contact;

import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ContactService {
    Optional<Contact> getOptionalByMailAddress(String mailAddress);
    void delete(Long id);
    void delete(Long id, Long firmId);
    ContactPageDto getContactListByFirm(Long firmId, int page, int size);
    Set<Contact> getContactListByIds(List<Long> ids);
    List<Contact> getContactListByMailAddress(String mailAddress);
}
