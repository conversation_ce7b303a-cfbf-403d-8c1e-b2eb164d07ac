package com.ipms.firm.service;

import com.ipms.firm.model.City;

import java.util.List;
import java.util.Map;

public interface CityService {
    List<City> getByCountry(String countryCode);

    List<City> getByCountryAndState(String countryCode, String stateCode);

    City getByCountryAndIso2(String countryCode, String iso2);

    List<City> getByIso2List(List<String> iso2);

    Map<String, City> getByIso2Map(List<String> iso2);

    List<City> getByIso2AndCountryCode(String iso2, String countryCode);

    City save(City city);

    void delete(Long id);
}
