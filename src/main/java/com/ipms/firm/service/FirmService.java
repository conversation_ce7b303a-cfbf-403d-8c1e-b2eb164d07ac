package com.ipms.firm.service;

import com.ipms.firm.dto.FirmDto;
import com.ipms.firm.dto.FirmEvent;
import com.ipms.firm.dto.FirmFilterRequest;
import com.ipms.firm.enums.FirmRole;
import com.ipms.firm.model.Firm;
import com.ipms.firm.model.GroupCompany;
import org.springframework.data.domain.Page;

import java.util.List;

public interface FirmService {
    Firm save(Firm firm);
    List<Firm> getAll(FirmFilterRequest filterRequest);
    List<Firm> getFirmListByTitle(String title);
    Firm getFirmById(Long id);
    void checkByTitleAndCountryAndCity(FirmDto firmDto);
    void delete(Long id, Long version);
    List<Firm> getFirmListByGroupCompany(Long groupCompanyId);
    List<Firm> getFirmListByTitleAndRole(String title, List<FirmRole> roles);
    List<Firm> getByIdIn(List<Long> ids);
    Firm updateGroupCompany(Long id, GroupCompany groupCompany, Long version);
    void checkTitleForMigration(FirmDto firmDto);
    void deleteAgent(Long id, Long version);
    Firm getByAgentId(Long id);
    Firm getByRightOwnerId(Long id);
    void deleteRightOwner(Long id, Long version);
    List<Firm> getByCityAndCountry(String city, String country);
    void processAssociatedEvent(FirmEvent event);
    Page<Firm> getFirmPage(FirmFilterRequest filterRequest, int page, int size);
    Firm linkBillingAccount(Long id, Long billingAccountId);
}
