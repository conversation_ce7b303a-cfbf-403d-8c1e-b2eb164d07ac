package com.ipms.firm.service;

import com.ipms.firm.model.Lead;
import com.ipms.firm.specification.LeadSpecification;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;

import java.util.List;

public interface LeadService {
    List<Lead> getAll();
    Lead getById(Long id);
    Lead save(Lead lead);
    void delete(Long id);
    void checkByNameAndPhoneNumber(String nameSurname, String phoneNumber);
    Page<Lead> getLeadPage(LeadSpecification spec, PageRequest pageRequest);
}
