package com.ipms.firm.client;

import com.ipms.firm.dto.MatterResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(name = "ipms-matter")
public interface MatterClient {
    @GetMapping("/api/matter/matters?firms={firmId}")
    MatterResponse getByFirmId(@PathVariable Long firmId);
}
