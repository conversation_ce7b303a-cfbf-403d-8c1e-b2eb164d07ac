package com.ipms.firm.client;

import com.ipms.firm.dto.ActivityResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(name = "ipms-activity")
public interface ActivityClient {
    @GetMapping("/api/activity/activities/firm/{firmId}")
    ActivityResponse getByFirmId(@PathVariable Long firmId);
}
