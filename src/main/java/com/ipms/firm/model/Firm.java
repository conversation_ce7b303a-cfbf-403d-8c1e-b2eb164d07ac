package com.ipms.firm.model;

import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import com.ipms.firm.enums.FirmRole;
import com.ipms.firm.enums.FirmType;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;
import org.hibernate.envers.RelationTargetAuditMode;

import javax.persistence.*;
import java.util.List;
import java.util.Set;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "firm")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class Firm extends VersionedEntity {

    @Column
    @Enumerated(EnumType.STRING)
    private FirmType type;

    @Column
    private String title;

    @Column
    private String country;

    @Column
    private String state;

    @Column
    private String city;

    @Column
    private String province;

    @Column
    private String postalCode;

    @Column
    private String address;

    @Column
    @Enumerated(EnumType.STRING)
    private FirmRole role;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn
    @Audited(targetAuditMode = RelationTargetAuditMode.AUDITED)
    private AgentInfo agentInfo;

    @OneToOne(cascade = CascadeType.ALL)
    @JoinColumn
    @Audited(targetAuditMode = RelationTargetAuditMode.AUDITED)
    private RightOwnerInfo rightOwnerInfo;

    @ManyToOne(fetch = FetchType.LAZY, cascade = {CascadeType.MERGE, CascadeType.PERSIST})
    @JoinColumn
    @Audited(targetAuditMode = RelationTargetAuditMode.AUDITED)
    private GroupCompany groupCompany;

    @ManyToMany(cascade=CascadeType.ALL)
    @JoinTable(
            name = "firm_contact",
            joinColumns = @JoinColumn(name = "firm_id"),
            inverseJoinColumns = @JoinColumn(name = "contact_id"))
    private Set<Contact> contacts;

    @Column
    private String legacyId;


    @ColumnDefault("1")
    @Column(columnDefinition = "SMALLINT")
    @Type(type = "org.hibernate.type.NumericBooleanType")
    private Boolean prospect;

    @ElementCollection
    @CollectionTable(name = "firm_billing_account", joinColumns = @JoinColumn(name = "firm_id"))
    @Column(name = "billing_account_id")
    private List<Long> billingAccountIds;

}
