package com.ipms.firm.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "province")
@Entity
@Where(clause = "is_deleted='0'")
public class Province extends VersionedEntity {

    @Column(nullable = false)
    private String name;

    @Column
    private String cityCode;

    @Column
    private String countryCode;

    @Column
    private Long latitude;

    @Column
    private Long longitude;

    @Column
    private int flag;

    @Column
    private String wikiDataId;

}
