package com.ipms.firm.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "country")
@Entity
@Where(clause = "is_deleted='0'")
public class Country extends VersionedEntity {

    @Column(nullable = false)
    private String iso2;

    @Column(nullable = false)
    private String iso3;

    @Column(nullable = false)
    private String name;

    @Column
    private String numericCode;

    @Column(name = "phonecode")
    private String phoneCode;

    @Column
    private String capital;

    @Column
    private String currency;

    @Column
    private String currencyName;

    @Column
    private String currencySymbol;

    @Column
    private String tld;

    @Column(name = "native")
    private String nativeName;

    @Column
    private String region;

    @Column
    private String subregion;

    @Column(length = 10000)
    private String timezones;

    @Column(length = 10000)
    private String translations;

    @Column
    private Long latitude;

    @Column
    private Long longitude;

    @Column
    private String emoji;

    @Column
    private String emojiU;

    @Column
    private int flag;

    @Column
    private String wikiDataId;

    @Column
    private Boolean isStateRequired;

    @Column
    private Boolean isProvinceRequired;

}
