package com.ipms.firm.model;

import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import com.ipms.firm.enums.Quadrant;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "lead")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class Lead extends VersionedEntity {
    @Column
    private String nameSurname;

    @Column
    private String title;

    @Column
    private String country;

    @Column
    private String firm;

    @Column
    private String email;

    @Column
    private String phoneNumber;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn
    private Sector sector;

    @Enumerated(EnumType.STRING)
    @Column
    private Quadrant quadrant;

    @Column
    private String leadOwner;

    @Column
    private String leadStatus;

    @Column
    private String linkedin;

    @Column
    private String source;

    @Column
    private String visibility;

    @OneToMany(mappedBy="lead", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<LeadAttachment> attachments;

    @Column
    private Boolean isLawFirm;

    @Column
    private Boolean isDigital;
}
