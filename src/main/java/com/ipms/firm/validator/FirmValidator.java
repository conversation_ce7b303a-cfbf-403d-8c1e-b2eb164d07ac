package com.ipms.firm.validator;

import com.ipms.firm.dto.FirmDto;
import com.ipms.firm.enums.FirmValidationResponseCode;
import com.ipms.firm.exception.FirmValidationException;
import com.ipms.firm.service.FirmService;
import com.ipms.firm.service.GroupCompanyService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;


@Component
@RequiredArgsConstructor
public class FirmValidator {

    private final FirmService service;
    private final GroupCompanyService groupCompanyService;

    public void validate(Object target) throws FirmValidationException {
        FirmDto firmDto = (FirmDto) target;
        if (firmDto.getCountry() != null && firmDto.getCountry().equals("US") &&
                (firmDto.getState() == null || firmDto.getState().isEmpty())) {
            throw new FirmValidationException(FirmValidationResponseCode.STATE_CAN_NOT_NULL);
        }
        if (firmDto.getRightOwnerInfo() == null && firmDto.getAgentInfo() == null) {
            throw new FirmValidationException(FirmValidationResponseCode.AGENT_AND_RIGHT_OWNER_CAN_NOT_NULL_TOGETHER);
        }
        if (firmDto.getRightOwnerInfo() != null && firmDto.getRightOwnerInfo().getSectorIdList() != null &&
                firmDto.getRightOwnerInfo().getTaxNo() != null && !firmDto.getRightOwnerInfo().getTaxNo().isEmpty()
                && !VknValidator.isVKNV(firmDto.getRightOwnerInfo().getTaxNo())) {
            throw new FirmValidationException(FirmValidationResponseCode.VKN_NOT_VALID);
        }
        service.checkByTitleAndCountryAndCity(firmDto);
        if (firmDto.getGroupCompany() != null && firmDto.getGroupCompany().getName() != null &&
                !firmDto.getGroupCompany().getName().isEmpty()) {
            groupCompanyService.checkByName(firmDto.getGroupCompany().getName(), firmDto.getGroupCompany().getId());
        }
    }
}
