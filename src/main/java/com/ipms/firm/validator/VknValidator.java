package com.ipms.firm.validator;

import org.apache.commons.lang.math.NumberUtils;
import org.springframework.stereotype.Component;

@Component
public class VknValidator {
    private VknValidator() {
    }

    public static boolean isVKNV(String vkn) {
        int tmp;
        int sum = 0;
        if (vkn != null && vkn.length() == 10 && NumberUtils.isDigits(vkn)) {
            int lastDigit = Character.getNumericValue(vkn.charAt(9));
            for (int i = 0; i < 9; i++) {
                int digit = Character.getNumericValue(vkn.charAt(i));
                tmp = (digit + 10 - (i + 1)) % 10;
                sum = (int) (tmp == 9 ? sum + tmp : sum + ((tmp * (Math.pow(2, (10 - (i + 1))))) % 9));
            }
            return lastDigit == (10 - (sum % 10)) % 10;
        }
        return false;
    }
}
