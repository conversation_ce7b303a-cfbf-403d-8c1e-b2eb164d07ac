package com.ipms.firm.dto;

import com.ipms.firm.handler.groups.ContactGroup;
import com.ipms.firm.handler.groups.CreateFirmGroup;
import com.ipms.firm.handler.groups.UpdateFirmGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GroupCompanyDto {
    private Long id;
    @Size(min = 3, groups = {UpdateFirmGroup.class, CreateFirmGroup.class, ContactGroup.class})
    private String name;
}
