package com.ipms.firm.dto;

import com.ipms.firm.handler.groups.ContactGroup;
import com.ipms.firm.handler.groups.CreateFirmGroup;
import com.ipms.firm.handler.groups.UpdateFirmGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RightOwnerInfoDto {
    private Long id;
    @NotNull(groups = {UpdateFirmGroup.class, CreateFirmGroup.class, ContactGroup.class})
    private List<Long> sectorIdList;
    private String taxNo;
    private String taxOffice;
    private List<SectorDto> sectors;
    private Long version;
}
