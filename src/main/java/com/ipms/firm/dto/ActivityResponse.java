package com.ipms.firm.dto;

import lombok.*;

import java.util.List;
import java.util.Optional;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityResponse {
    private int code;
    private List<Payload> payload;

    public Optional<Boolean> used(){
        return payload.isEmpty() ? Optional.empty() : Optional.of(Boolean.TRUE);
    }

    @Getter
    @Setter
    @Builder
    public static class Payload {
    }
}
