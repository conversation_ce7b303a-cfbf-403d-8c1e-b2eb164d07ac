package com.ipms.firm.dto;

import com.ipms.firm.enums.Quadrant;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LeadFilterRequest {
    private List<Long> idList;
    private List<String> statusList;
    private List<Quadrant> quadrantList;
    private List<String> leadOwnerList;
    private List<String> firmList;
    private String sortField;
    private String sortDirection;
}
