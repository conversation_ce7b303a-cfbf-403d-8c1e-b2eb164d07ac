package com.ipms.firm.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum StateResponseCode implements Code {
    STATE_NOT_FOUND(1000, "error.code.firm.state.not_found");

    private final Integer code;
    private final String messageKey;

    StateResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
