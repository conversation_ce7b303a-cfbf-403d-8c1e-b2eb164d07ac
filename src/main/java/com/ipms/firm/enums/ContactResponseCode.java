package com.ipms.firm.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum ContactResponseCode implements Code {
    CONTACT_NOT_FOUND(1000, "error.code.contact.not_found");

    private final Integer code;
    private final String messageKey;

    ContactResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }

}
