package com.ipms.firm.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum FirmValidationResponseCode implements Code {
    STATE_CAN_NOT_NULL(2001, "error.code.firm.validation.state_cannot_null"),
    ADDRESS_CAN_NOT_NULL(2002, "error.code.firm.validation.address_cannot_null"),
    FIRM_ALREADY_EXIST(2003, "error.code.firm.already_exist"),
    VKN_NOT_VALID(2004, "error.code.firm.rightOwner.vkn_not_valid"),
    AGENT_AND_RIGHT_OWNER_CAN_NOT_NULL_TOGETHER(2005, "error.code.firm.rightOwner.agent_can_not_be_null_together"),
    GROUP_COMPANY_ALREADY_EXIST(2006, "error.code.firm.groupCompany.already_exist"),
    DUPLICATE_RECORD(2007, "error.code.firm.duplicate_record"),
    AGENT_ROLE_CAN_NOT_BE_REMOVED(2008, "error.code.firm.agent_info.role_can_not_be_removed"),
    RIGHT_OWNER_ROLE_CAN_NOT_BE_REMOVED(2009, "error.code.firm.right_owner.role_can_not_be_removed");

    private final Integer code;
    private final String messageKey;

    FirmValidationResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
