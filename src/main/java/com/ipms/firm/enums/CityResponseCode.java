package com.ipms.firm.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum CityResponseCode implements Code {
    CITY_NOT_FOUND(1000, "error.code.firm.city.not_found"),
    CITY_IN_USE(1001, "error.code.firm.city.in_use");

    private final Integer code;
    private final String messageKey;

    CityResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
