package com.ipms.firm.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum CountryResponseCode implements Code {
    COUNTRY_NOT_FOUND(1000, "error.code.firm.country.not_found");

    private final Integer code;
    private final String messageKey;

    CountryResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
