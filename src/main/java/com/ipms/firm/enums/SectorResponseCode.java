package com.ipms.firm.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum SectorResponseCode implements Code {
    SECTOR_NOT_FOUND(1100, "error.code.firm.sector.not_found");

    private final Integer code;
    private final String messageKey;

    SectorResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
