package com.ipms.firm.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum AgentResponseCode implements Code {
    AGENT_NOT_FOUND(1400, "error.code.firm.agent.not_found");

    private final Integer code;
    private final String messageKey;

    AgentResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
