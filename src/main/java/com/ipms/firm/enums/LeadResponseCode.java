package com.ipms.firm.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum LeadResponseCode  implements Code {
    LEAD_NOT_FOUND(1000, "error.code.firm.lead.not_found"),
    LEAD_ALREADY_EXIST(1001, "error.code.firm.lead.already_exist");

    private final Integer code;
    private final String messageKey;

    LeadResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
