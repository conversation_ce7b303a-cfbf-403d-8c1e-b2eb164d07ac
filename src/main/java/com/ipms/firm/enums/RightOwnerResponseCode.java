package com.ipms.firm.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum RightOwnerResponseCode implements Code {
    RIGHT_OWNER_NOT_FOUND(1500, "error.code.firm.rightowner.not_found");

    private final Integer code;
    private final String messageKey;

    RightOwnerResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
