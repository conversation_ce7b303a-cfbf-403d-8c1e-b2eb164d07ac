package com.ipms.firm.enums;

import java.util.Arrays;

public enum Quadrant {
    SUPERSTARS("Superstars"),
    POTENTIAL_STARS("Potential stars"),
    LOYAL_BUT_UNKNOWN("Loyal but unknown"),
    BACKSLIDERS("Backsliders"),
    EXISTING_CLIENT("Existing Client");

    private final String name;

    Quadrant(String name) {
        this.name = name;
    }

    public String getName() {
        return name;
    }

    public static Quadrant getEnum(String name) {
        return Arrays.stream(values())
                .filter(v -> v.getName().equalsIgnoreCase(name))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
