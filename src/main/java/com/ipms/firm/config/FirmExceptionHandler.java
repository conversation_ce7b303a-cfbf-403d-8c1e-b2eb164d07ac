package com.ipms.firm.config;

import com.ipms.core.exception.BaseException;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.config.GlobalExceptionHandler;
import com.ipms.core.i18n.service.II18nMessageService;
import com.ipms.firm.exception.FirmValidationException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Locale;

@RestControllerAdvice
public class FirmExceptionHandler extends GlobalExceptionHandler {
    public FirmExceptionHandler(II18nMessageService ii18nMessageService) {
        super(ii18nMessageService);
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({
            FirmValidationException.class
    })
    public BaseResponse<Object> handleMailExceptions(BaseException exception, Locale locale) {
        return buildResponseCode(exception, locale);
    }
}
