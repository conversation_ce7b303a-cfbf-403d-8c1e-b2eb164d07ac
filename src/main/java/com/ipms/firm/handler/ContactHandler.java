package com.ipms.firm.handler;

import com.ipms.firm.dto.ContactDto;
import com.ipms.firm.dto.ContactPageDto;

import java.util.List;

public interface ContactHandler {
    boolean checkByMailAddress (String mailAddress);
    void delete(Long contactId);
    void delete(Long contactId, Long firmId);
    void deleteByEmail(String email);
    ContactPageDto getContactListByFirm(Long firmId, int page, int size);
    List<ContactDto> getContactListByMailAddress(String mailAddress);
}
