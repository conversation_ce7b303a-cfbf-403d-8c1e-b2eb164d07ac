package com.ipms.firm.handler;

import com.ipms.firm.dto.ProvinceDto;

import java.util.List;

public interface ProvinceHandler {
    List<ProvinceDto> getByCountryAndCity(String countryCode, String cityCode);
    ProvinceDto getByCountryAndCityAndProvince(String countryCode, String cityCode, String provinceName);
    ProvinceDto getProvince(String countryCode, String cityCode, String provinceName);
}
