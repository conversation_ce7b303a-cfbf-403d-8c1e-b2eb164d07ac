package com.ipms.firm.handler;

import com.ipms.firm.dto.*;

import java.util.List;

public interface FirmHandler {
    FirmDto save(FirmDto firmDto);
    FirmDto saveWithContact(FirmDto firmDto);
    List<FirmSummaryDto> listFirmSummaryDtoList(String title);
    FirmDto update(FirmDto firmDto, Long id);
    FirmDto getById(Long id);
    List<FirmDto> getAll(FirmFilterRequest filterRequest);
    void delete(Long id, Long version);
    List<FirmDto> getFirmListByGroupCompany(Long groupCompanyId);
    List<FirmSummaryDto> getFirmListByTitleAndRole(String title, String role);
    List<FirmDto> getByIdIn(List<Long> ids);
    FirmDto updateGroupCompany(Long id, GroupCompanyDto groupCompanyDto, Long version);
    void deleteAgent(Long id, Long version);
    void deleteRightOwner(Long rightOwnerId, Long version);
    PageData<FirmSummaryDto> getFirmSummaryPage(FirmFilterRequest filterRequest, int page, int size);
    FirmDto linkBillingAccount(Long id, Long billingAccountId);
}
