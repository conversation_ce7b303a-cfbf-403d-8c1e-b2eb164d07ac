package com.ipms.firm.handler.impl;

import com.ipms.firm.dto.ContactDto;
import com.ipms.firm.dto.ContactPageDto;
import com.ipms.firm.exception.ContactNotFoundException;
import com.ipms.firm.handler.ContactHandler;
import com.ipms.firm.mapper.ContactMapper;
import com.ipms.firm.service.ContactService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ContactHandlerImpl implements ContactHandler {
    private final ContactService service;
    private final ContactMapper mapper;

    @Override
    public boolean checkByMailAddress(String mailAddress) {
        return service.getOptionalByMailAddress(mailAddress).isPresent();
    }

    @Override
    public void delete(Long contactId) {
        service.delete(contactId);
    }

    @Override
    public void delete(Long contactId, Long firmId) {
        service.delete(contactId, firmId);
    }

    @Override
    public void deleteByEmail(String email) {
        var contact = service.getOptionalByMailAddress(email).orElseThrow(ContactNotFoundException::new);
        delete(contact.getId());
    }

    @Override
    public ContactPageDto getContactListByFirm(Long firmId, int page, int size) {
        return service.getContactListByFirm(firmId, page, size);
    }

    @Override
    public List<ContactDto> getContactListByMailAddress(String mailAddress) {
        return service.getContactListByMailAddress(mailAddress)
                .stream()
                .map(mapper::toContactDto)
                .toList();
    }
}
