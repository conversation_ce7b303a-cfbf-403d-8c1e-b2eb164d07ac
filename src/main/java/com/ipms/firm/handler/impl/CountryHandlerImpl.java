package com.ipms.firm.handler.impl;

import com.ipms.firm.dto.CountryDto;
import com.ipms.firm.handler.CountryHandler;
import com.ipms.firm.mapper.CountryMapper;
import com.ipms.firm.model.Country;
import com.ipms.firm.service.CountryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.function.Function;
import java.util.stream.Collectors;

@Component
@RequiredArgsConstructor
public class CountryHandlerImpl implements CountryHandler {
    private final CountryService service;
    private final CountryMapper mapper;

    @Override
    public List<CountryDto> getAll() {
        var countries = service.getAll();
        return mapper.toCountryDtoList(countries);
    }

    @Override
    public CountryDto getByIso2(String iso2) {
        var country = service.getByIso2(iso2);
        return mapper.toCountryDto(country);
    }

    @Override
    public Map<String, Country> getMap() {
        return service.getAll().stream().collect(Collectors.toMap(Country::getIso2, Function.identity()));
    }

    @Override
    public CountryDto getIso2ById(Long id) {
        var country = service.getById(id);
        return mapper.toCountryDto(country);
    }

}
