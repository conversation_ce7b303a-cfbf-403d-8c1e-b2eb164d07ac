package com.ipms.firm.handler.impl;

import com.ipms.firm.dto.GroupCompanyDto;
import com.ipms.firm.handler.GroupCompanyHandler;
import com.ipms.firm.mapper.GroupCompanyMapper;
import com.ipms.firm.service.GroupCompanyService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class GroupCompanyHandlerImpl implements GroupCompanyHandler {

    private final GroupCompanyService service;
    private final GroupCompanyMapper mapper;

    @Override
    public List<GroupCompanyDto> getAll() {
        return service.getAll()
                .stream()
                .map(mapper::toGroupCompanyDto)
                .toList();
    }

    @Override
    public List<GroupCompanyDto> getByName(String name) {
        return service.getByName(name)
                .stream()
                .map(mapper::toGroupCompanyDto)
                .toList();
    }
}
