package com.ipms.firm.handler.impl;

import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.enums.Domain;
import com.ipms.core.common.enums.TransferType;
import com.ipms.core.common.model.TransferEvent;
import com.ipms.firm.dto.*;
import com.ipms.firm.enums.FirmRole;
import com.ipms.firm.handler.*;
import com.ipms.firm.mapper.ContactMapper;
import com.ipms.firm.mapper.FirmMapper;
import com.ipms.firm.mapper.GroupCompanyMapper;
import com.ipms.firm.model.City;
import com.ipms.firm.model.Contact;
import com.ipms.firm.model.Firm;
import com.ipms.firm.service.*;
import com.ipms.firm.validator.FirmValidator;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Map;
import java.util.Optional;
import java.util.Set;

@Component
@RequiredArgsConstructor
public class FirmHandlerImpl implements FirmHandler {
    private final CountryHandler countryHandler;
    private final FirmService service;
    private final FirmMapper mapper;
    private final FirmValidator validator;
    private final CityHandler cityHandler;
    private final ProvinceHandler provinceHandler;
    private final StateHandler stateHandler;
    private final ContactService contactService;
    private final ContactMapper contactMapper;
    private final SectorService sectorService;
    private final GroupCompanyService groupCompanyService;
    private final ProducerService producerService;
    private final CityService cityService;
    private final GroupCompanyMapper groupCompanyMapper;

    @Override
    public FirmDto save(FirmDto firmDto) {
        if (Optional.ofNullable(firmDto.getGroupCompany()).isPresent()) {
            Optional.ofNullable(firmDto.getGroupCompany().getName())
                    .ifPresent(name -> firmDto.getGroupCompany().setName(firmDto.getGroupCompany().getName().trim()));
        }
        service.checkTitleForMigration(firmDto);
        validator.validate(firmDto);
        var firm = mapper.toFirm(firmDto);

        Optional.ofNullable(firmDto.getRightOwnerInfo())
                .ifPresent(owner-> firm.getRightOwnerInfo()
                        .setSectors(sectorService.getByIdList(owner.getSectorIdList())));

        Optional.ofNullable(firmDto.getGroupCompany())
                .flatMap(groupCompany -> Optional.ofNullable(firmDto.getGroupCompany().getId()))
                .ifPresent(id -> firm.setGroupCompany(groupCompanyService.getById(id)));
        if (Optional.ofNullable(firmDto.getContact()).isPresent()) {
            contactService.getOptionalByMailAddress(firmDto.getContact().getMailAddress())
                    .ifPresentOrElse(
                            contact-> firm.setContacts(Set.of(contact)),
                            ()-> firm.setContacts(Set.of(contactMapper.toContact(firmDto.getContact()))));
        }
        firm.setProspect(true);
        var saved = service.save(firm);
        sendTransferEvent(saved.getId(), TransferType.INSERT);
        return mapper.toFirmDto(saved);
    }

    @Override
    public FirmDto saveWithContact(FirmDto firmDto) {
        var saved = save(firmDto);
        sendTransferEvent(saved.getId(), TransferType.INSERT);
        return saved;
    }

    @Override
    public List<FirmSummaryDto> listFirmSummaryDtoList(String title) {
        var firms = service.getFirmListByTitle(title);
        setCityName(firms);
        return mapper.toFirmSummaryDto(firms);
    }

    @Override
    public FirmDto update(FirmDto firmDto, Long id) {
        validator.validate(firmDto);
        var oldFirm = service.getFirmById(id);
        var updated = mapper.toFirm(firmDto);
        updated.setCreatedAt(oldFirm.getCreatedAt());
        updated.setCreatedBy(oldFirm.getCreatedBy());

        Optional.ofNullable(firmDto.getGroupCompany())
                .flatMap(groupCompany -> Optional.ofNullable(firmDto.getGroupCompany().getId()))
                .ifPresent(groupCompanyId -> updated.setGroupCompany(groupCompanyService.getById(groupCompanyId)));

        Optional.ofNullable(firmDto.getRightOwnerInfo())
                .ifPresent(owner-> updated.getRightOwnerInfo()
                        .setSectors(sectorService.getByIdList(owner.getSectorIdList())));

        updateContacts(firmDto, updated);
        var saved = service.save(updated);
        sendTransferEvent(saved.getId(), TransferType.UPDATE);
        return mapper.toFirmDto(saved);
    }

    @Override
    public FirmDto getById(Long id) {
        var firmDto = mapper.toFirmDto(service.getFirmById(id));
        var countryDto = countryHandler.getByIso2(firmDto.getCountry());
        firmDto.setCountryName(countryDto.getName());
        firmDto.setCountryTranslations(countryDto.getTranslations());
        if (countryDto.isStateRequired()) {
            if(StringUtils.isNotBlank(firmDto.getState())) {
                var stateDto = stateHandler.getByCountryAndIso2(firmDto.getCountry(), firmDto.getState());
                firmDto.setStateName(stateDto.getName());
            }
            firmDto.setIsStateRequired(true);
        }
        if(StringUtils.isNotBlank(firmDto.getCity())){
            var cityDto = cityHandler.getByCountryAndIso2(firmDto.getCountry(), firmDto.getCity());
            firmDto.setCityName(cityDto.getName());
        }
        var provinceDto = provinceHandler.getProvince(firmDto.getCountry(), firmDto.getCity(), firmDto.getProvince());
        Optional.ofNullable(provinceDto).ifPresent(dto -> {
            firmDto.setProvinceName(provinceDto.getName());
            firmDto.setIsProvinceRequired(true);
        });
        return firmDto;
    }

    @Override
    public List<FirmDto> getAll(FirmFilterRequest filterRequest) {
        return service.getAll(filterRequest).stream()
                .map(mapper::toFirmDto)
                .toList();
    }

    @Override
    public void delete(Long id, Long version) {
        service.delete(id,version);
        sendTransferEvent(id, TransferType.DELETE);
    }

    @Override
    public List<FirmDto> getFirmListByGroupCompany(Long groupCompanyId) {
        return service.getFirmListByGroupCompany(groupCompanyId)
                .stream()
                .map(mapper::toFirmDto)
                .toList();
    }

    @Override
    public List<FirmSummaryDto> getFirmListByTitleAndRole(String title, String role) {
        List<FirmRole> roles = List.of(FirmRole.ALL, FirmRole.valueOf(role));
        var summaryList = mapper.toFirmSummaryDto(service.getFirmListByTitleAndRole(title, roles));
        summaryList.stream()
                .filter(firm -> StringUtils.isNotBlank(firm.getCity()))
                .forEach(summary -> summary.setCityName(cityService.getByCountryAndIso2(summary.getCountry(), summary.getCity()).getName()));
        return summaryList;
    }

    @Override
    public List<FirmDto> getByIdIn(List<Long> ids) {
        var firms = service.getByIdIn(ids);
        var cityMap = mapCityCode(firms);
        var firmDtoList = firms.stream()
                .map(mapper::toFirmDto)
                .toList();
        enrichCityDetail(firmDtoList, cityMap);
        return firmDtoList;
    }

    @Override
    public FirmDto updateGroupCompany(Long id, GroupCompanyDto groupCompanyDto, Long version) {
        var groupCompany = groupCompanyMapper.toGroupCompany(groupCompanyDto);
        return mapper.toFirmDto(service.updateGroupCompany(id, groupCompany, version));
    }

    @Override
    public void deleteAgent(Long id, Long version) {
        service.deleteAgent(id, version);
        sendTransferEvent(id, TransferType.UPDATE);
    }

    @Override
    public void deleteRightOwner(Long id, Long version) {
        service.deleteRightOwner(id, version);
        sendTransferEvent(id, TransferType.UPDATE);
    }

    @Override
    public FirmDto linkBillingAccount(Long id, Long billingAccountId) {
        var firm = service.linkBillingAccount(id, billingAccountId);
        return mapper.toFirmDto(firm);
    }

    @Override
    public PageData<FirmSummaryDto> getFirmSummaryPage(FirmFilterRequest filterRequest, int page, int size) {
        var firmPage = service.getFirmPage(filterRequest, page, size);
        var firms = firmPage.getContent();
        setCityName(firms);
        setCountryName(firms);
        return PageData.<Firm>builder()
                .content(firms)
                .totalElements(firmPage.getTotalElements())
                .totalPages(firmPage.getTotalPages())
                .build().mapData(mapper::toFirmSummaryDto);
    }

    private void sendTransferEvent(Long id, TransferType transferType) {
        producerService.sendTransfer(TransferEvent.builder()
                .type(transferType)
                .domainObjectId(id)
                .domain(Domain.IPMS_FIRM)
                .build());
    }

    private void setCountryName(List<Firm> firmList) {
        var countryMap = countryHandler.getMap();
        firmList.stream()
                .filter(firm -> StringUtils.isNotBlank(firm.getCountry()))
                .forEach(firm -> firm.setCountry(countryMap.get(firm.getCountry()).getName()));
    }

    private void setCityName(List<Firm> firmList) {
        var cityMap = mapCityCode(firmList);
        firmList.stream()
                .filter(firm -> StringUtils.isNotBlank(firm.getCity()))
                .forEach(firm -> firm.setCity(cityMap.get(firm.getCountry() + firm.getCity()).getName()));
    }

    private Map<String, City> mapCityCode(List<Firm> firmList) {
        List<String> iso2List = firmList.stream()
                .map(Firm::getCity)
                .distinct()
                .toList();
        return cityService.getByIso2Map(iso2List);
    }

    private void enrichCityDetail(List<FirmDto> firmDtoList, Map<String, City> cityMap) {
        firmDtoList.stream()
                .filter(firmDto -> StringUtils.isNotBlank(firmDto.getCity()))
                .forEach(firmDto -> {
                    var city = cityMap.get(firmDto.getCountry() + firmDto.getCity());
                    firmDto.setCityName(city.getName());
                });
    }

    private void updateContacts(FirmDto firmDto, Firm firm) {
        if (CollectionUtils.isNotEmpty(firmDto.getContacts())) {
            var contactIdList = firmDto.getContacts()
                    .stream()
                    .map(ContactDto::getId)
                    .toList();
            var contactSet = contactService.getContactListByIds(contactIdList);
            firm.setContacts(contactSet);
            firmDto.getContacts()
                    .stream()
                    .filter(contactDto -> contactDto.getId() == null)
                    .forEach(contactDto -> {
                        var isContactExist = contactService.getOptionalByMailAddress(contactDto.getMailAddress()).isPresent();
                        if (!isContactExist) {
                            var contact = Contact.builder()
                                    .mailName(contactDto.getMailName())
                                    .mailAddress(contactDto.getMailAddress())
                                    .build();
                            contactSet.add(contact);
                        }
                    });
        }
    }
}
