package com.ipms.firm.handler.impl;

import com.ipms.firm.dto.SectorDto;
import com.ipms.firm.handler.SectorHandler;
import com.ipms.firm.mapper.SectorMapper;
import com.ipms.firm.service.SectorService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class SectorHandlerImpl implements SectorHandler {
    private final SectorService service;
    private final SectorMapper mapper;

    @Override
    public List<SectorDto> getAll() {
        return service.getAll()
                .stream()
                .map(mapper::toSectorDto)
                .toList();
    }
}
