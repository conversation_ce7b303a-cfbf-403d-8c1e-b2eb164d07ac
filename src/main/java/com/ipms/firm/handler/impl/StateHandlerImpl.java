package com.ipms.firm.handler.impl;

import com.ipms.firm.dto.StateDto;
import com.ipms.firm.handler.StateHandler;
import com.ipms.firm.mapper.StateMapper;
import com.ipms.firm.service.StateService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class StateHandlerImpl implements StateHandler {
    private final StateService service;
    private final StateMapper mapper;

    @Override
    public List<StateDto> getByCountry(String countryCode) {
        var states = service.getByCountry(countryCode);
        return mapper.toStateDtoList(states);
    }

    @Override
    public StateDto getByCountryAndIso2(String countryCode, String iso2) {
        var state = service.getByCountryAndIso2(countryCode,iso2);
        return mapper.toStateDto(state);
    }

}