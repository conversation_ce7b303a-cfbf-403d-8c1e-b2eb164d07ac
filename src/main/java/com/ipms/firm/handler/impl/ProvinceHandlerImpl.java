package com.ipms.firm.handler.impl;

import com.ipms.firm.dto.ProvinceDto;
import com.ipms.firm.handler.ProvinceHandler;
import com.ipms.firm.mapper.ProvinceMapper;
import com.ipms.firm.service.ProvinceService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class ProvinceHandlerImpl implements ProvinceHandler {
    private final ProvinceService service;
    private final ProvinceMapper mapper;
    
    @Override
    public List<ProvinceDto> getByCountryAndCity(String countryCode, String cityCode) {
        var provinces = service.getByCountryAndCity(countryCode,cityCode);
        return mapper.toProvinceDtoList(provinces);
    }

    @Override
    public ProvinceDto getByCountryAndCityAndProvince(String countryCode, String cityCode, String provinceName) {
        var province = service.getByCountryAndCityAndProvince(countryCode,cityCode,provinceName);
        return mapper.toProvinceDto(province);
    }

    @Override
    public ProvinceDto getProvince(String countryCode, String cityCode, String provinceName) {
        var province = service.getProvince(countryCode,cityCode,provinceName);
        return mapper.toProvinceDto(province);
    }
}
