package com.ipms.firm.handler.impl;

import com.ipms.firm.dto.LeadDto;
import com.ipms.firm.dto.LeadFilterRequest;
import com.ipms.firm.dto.LeadPageDto;
import com.ipms.firm.handler.LeadHandler;
import com.ipms.firm.mapper.LeadMapper;
import com.ipms.firm.service.LeadService;
import com.ipms.firm.service.SectorService;
import com.ipms.firm.specification.LeadSpecification;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Component;

import java.util.List;
import java.util.Optional;

@Component
@RequiredArgsConstructor
public class LeadHandlerImpl implements LeadHandler {
    private final LeadService service;
    private final LeadMapper mapper;
    private final SectorService sectorService;

    @Override
    public LeadDto save(LeadDto leadDto) {
        service.checkByNameAndPhoneNumber(leadDto.getNameSurname(), leadDto.getPhoneNumber());
        var lead = mapper.toLead(leadDto);

        Optional.ofNullable(leadDto.getSector())
                .flatMap(sector -> Optional.ofNullable(sector.getId()))
                .ifPresent(id -> lead.setSector(sectorService.getById(id)));

        var saved = service.save(lead);
        return mapper.toLeadDto(saved);
    }

    @Override
    public List<LeadDto> getAll() {
        return mapper.toDtoList(service.getAll());
    }

    @Override
    public LeadDto update(LeadDto leadDto, Long id) {
        var oldLead = service.getById(id).toBuilder().build();
        var updated = mapper.toLeadFromDto(leadDto, oldLead);

        updated.setId(oldLead.getId());
        updated.setCreatedAt(oldLead.getCreatedAt());
        updated.setCreatedBy(oldLead.getCreatedBy());

        if (leadDto.getSector() == null) {
            updated.setSector(null);
        } else {
            Optional.ofNullable(leadDto.getSector().getId())
                    .ifPresent(sectorId -> updated.setSector(sectorService.getById(sectorId)));
        }

        var saved = service.save(updated);
        return mapper.toLeadDto(saved);
    }

    @Override
    public void delete(Long id) {
        service.delete(id);
    }

    @Override
    public LeadPageDto getAllPageable(LeadFilterRequest filterRequest, int page, int size) {
        var leadPage = service.getLeadPage(mapSpecification(filterRequest), PageRequest.of(page, size));
        return LeadPageDto.builder()
                .leadDtos(leadPage.getContent().stream()
                        .map(mapper::toLeadDto)
                        .toList())
                .totalElements(leadPage.getTotalElements())
                .totalPages(leadPage.getTotalPages())
                .build();
    }

    @Override
    public LeadDto getById(Long id) {
        var lead = service.getById(id);
        return mapper.toLeadDto(lead);
    }

    private LeadSpecification mapSpecification(LeadFilterRequest filterRequest) {
        return LeadSpecification.builder()
                .ids(filterRequest.getIdList())
                .statusList(filterRequest.getStatusList())
                .quadrantList(filterRequest.getQuadrantList())
                .leadOwnerList(filterRequest.getLeadOwnerList())
                .firmList(filterRequest.getFirmList())
                .sortField(filterRequest.getSortField())
                .sortDirection(filterRequest.getSortDirection())
                .build();
    }
}
