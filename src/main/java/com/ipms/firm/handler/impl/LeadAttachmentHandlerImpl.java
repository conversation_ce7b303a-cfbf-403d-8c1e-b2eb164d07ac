package com.ipms.firm.handler.impl;

import com.ipms.config.storage.service.StorageService;
import com.ipms.firm.dto.LeadAttachmentDto;
import com.ipms.firm.handler.LeadAttachmentHandler;
import com.ipms.firm.mapper.LeadAttachmentMapper;
import com.ipms.firm.model.LeadAttachment;
import com.ipms.firm.service.LeadAttachmentService;
import com.ipms.firm.service.LeadService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.io.FilenameUtils;
import org.springframework.stereotype.Component;

import java.util.UUID;

@Component
@RequiredArgsConstructor
public class LeadAttachmentHandlerImpl implements LeadAttachmentHandler {
    private final LeadAttachmentService leadAttachmentService;
    private final LeadService leadService;
    private final LeadAttachmentMapper mapper;
    private final StorageService storageService;

    @Override
    public LeadAttachmentDto save(LeadAttachmentDto dto) {
        var attachment = mapper.toAttachment(dto);
        var lead = leadService.getById(dto.getLeadId());
        attachment.setLead(lead);
        attachment.setFileUniqueName(prepareDocumentName(attachment.getFileName()));
        var saved = leadAttachmentService.save(attachment);
        return toAttachmentDto(saved);
    }

    public static String prepareDocumentName(String fileName) {
        return  "q" + UUID.randomUUID() + "." + FilenameUtils.getExtension(fileName);
    }

    private LeadAttachmentDto toAttachmentDto(LeadAttachment attachment) {
        var attachmentDto = mapper.toAttachmentDto(attachment);
        var url = storageService.generateSas(mapper.toStorageFile(attachmentDto));
        attachmentDto.setFileSignedUrl(url);
        return attachmentDto;
    }
}
