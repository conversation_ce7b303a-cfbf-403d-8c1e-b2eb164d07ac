package com.ipms.firm.handler.impl;

import com.ipms.firm.dto.CityDto;
import com.ipms.firm.handler.CityHandler;
import com.ipms.firm.mapper.CityMapper;
import com.ipms.firm.model.City;
import com.ipms.firm.service.CityService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import java.util.List;

@Component
@RequiredArgsConstructor
public class CityHandlerImpl implements CityHandler {
    private final CityService service;
    private final CityMapper mapper;

    @Override
    public List<CityDto> getByCountry(String countryCode) {
        var cities = service.getByCountry(countryCode);
        return mapper.toCityDtoList(cities);
    }

    @Override
    public List<CityDto> getByCountryAndState(String countryCode,String stateCode) {
        var cities = service.getByCountryAndState(countryCode,stateCode);
        return mapper.toCityDtoList(cities);
    }

    @Override
    public CityDto getByCountryAndIso2(String countryCode, String iso2) {
        var city = service.getByCountryAndIso2(countryCode,iso2);
        return mapper.toCityDto(city);
    }

    @Override
    public CityDto save(CityDto dto) {
        var city = mapper.toCity(dto);
        generateIso2(city);
        var saved = service.save(city);
        return mapper.toCityDto(saved);
    }

    @Override
    public void delete(Long id) {
        service.delete(id);
    }

    private void generateIso2(City city) {
        var iso2 = city.getName().substring(0, 2) + 1;
        while (!service.getByIso2AndCountryCode(iso2, city.getCountryCode()).isEmpty()) {
            var lastChar = iso2.substring(iso2.length() - 1);
            iso2 = iso2.substring(0, 2) + (Integer.parseInt(lastChar) + 1);
        }
        city.setIso2(iso2);
    }
}