package com.ipms.firm.handler;

import com.ipms.firm.dto.LeadDto;
import com.ipms.firm.dto.LeadFilterRequest;
import com.ipms.firm.dto.LeadPageDto;

import java.util.List;

public interface LeadHandler {
    LeadDto save(LeadDto leadDto);
    List<LeadDto> getAll();
    LeadDto update(LeadDto leadDto, Long id);
    void delete(Long id);
    LeadPageDto getAllPageable(LeadFilterRequest filterRequest, int page, int size);
    LeadDto getById(Long id);
}
