package com.ipms.firm.mapper;

import com.ipms.firm.dto.LeadDto;
import com.ipms.firm.model.Lead;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring")
public interface LeadMapper {
    LeadDto toLeadDto(Lead lead);
    List<LeadDto> toDtoList(List<Lead> leadList);
    Lead toLead(LeadDto leadDto);
    @Mapping(target = "sector", ignore = true)
    Lead toLeadFromDto(LeadDto dto, @MappingTarget Lead lead);

}
