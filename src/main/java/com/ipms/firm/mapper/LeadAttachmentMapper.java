package com.ipms.firm.mapper;

import com.ipms.config.storage.model.StorageFile;
import com.ipms.firm.dto.LeadAttachmentDto;
import com.ipms.firm.model.LeadAttachment;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface LeadAttachmentMapper {
    LeadAttachmentDto toAttachmentDto(LeadAttachment leadAttachment);
    LeadAttachment toAttachment(LeadAttachmentDto leadAttachmentDto);

    StorageFile toStorageFile(LeadAttachmentDto leadAttachmentDto);
}
