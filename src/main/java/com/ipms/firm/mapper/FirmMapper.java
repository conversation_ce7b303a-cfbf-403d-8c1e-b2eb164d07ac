package com.ipms.firm.mapper;

import com.ipms.firm.dto.FirmDto;
import com.ipms.firm.dto.FirmSummaryDto;
import com.ipms.firm.model.Firm;
import com.ipms.firm.model.Sector;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.Named;

import java.util.List;
import java.util.Set;

@Mapper(componentModel = "spring")
public interface FirmMapper {
    @Mapping(source = "rightOwnerInfo.sectors", target = "rightOwnerInfo.sectorIdList", qualifiedByName = "setSectorIdList")
    FirmDto toFirmDto(Firm firm);
    Firm toFirm(FirmDto firmDto);
    FirmSummaryDto toFirmSummaryDto(Firm firm);
    List<FirmSummaryDto> toFirmSummaryDto(List<Firm> firm);

    @Named("setSectorIdList")
    static List<Long> setSectorIdList(Set<Sector> sectors) {
        return sectors.stream()
                .map(Sector::getId)
                .toList();
    }
}
