package com.ipms.firm.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.firm.dto.GroupCompanyDto;
import com.ipms.firm.handler.GroupCompanyHandler;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("group-company")
@Tag(name = "group-company", description = "This endpoint contains group-company APIs")
public class GroupCompanyController {

    private final GroupCompanyHandler handler;

    @GetMapping
    public BaseResponse<List<GroupCompanyDto>> groupCompanyList() {
        return BaseResponse.<List<GroupCompanyDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(handler.getAll())
                .build();
    }

    @GetMapping("/search")
    public BaseResponse<List<GroupCompanyDto>> getByName(@RequestParam(name = "name") String name) {
        return BaseResponse.<List<GroupCompanyDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(handler.getByName(name))
                .build();
    }
}
