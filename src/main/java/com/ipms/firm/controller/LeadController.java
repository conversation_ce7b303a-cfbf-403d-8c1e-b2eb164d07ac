package com.ipms.firm.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.firm.dto.LeadDto;
import com.ipms.firm.dto.LeadFilterRequest;
import com.ipms.firm.dto.LeadPageDto;
import com.ipms.firm.handler.LeadHandler;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping( "lead")
@Tag(name = "lead", description = "This endpoint contains lead APIs ")
@Validated
public class LeadController {
    private final LeadHandler handler;

    @GetMapping
    public BaseResponse<List<LeadDto>> leadList() {
        return BaseResponse.<List<LeadDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(handler.getAll())
                .build();
    }

    @PostMapping
    public BaseResponse<LeadDto> save(@Valid @RequestBody LeadDto leadDto){
        return BaseResponse.<LeadDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.save(leadDto))
                .build();
    }

    @PutMapping(value = "/{id}")
    public BaseResponse<LeadDto> update(@RequestBody @Valid LeadDto dto, @PathVariable Long id) {
        var leadDto = handler.update(dto, id);
        return BaseResponse.<LeadDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(leadDto)
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        handler.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/{page}/{size}")
    public BaseResponse<LeadPageDto> getAllPageable(@PathVariable int page,
                                                    @PathVariable int size,
                                                    LeadFilterRequest filterRequest) {
        var dto = handler.getAllPageable(filterRequest, page, size);
        return BaseResponse.<LeadPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @GetMapping(value = "/{id}")
    public BaseResponse<LeadDto> getLeadById(@PathVariable Long id){
        return BaseResponse.<LeadDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(handler.getById(id))
                .build();
    }

}
