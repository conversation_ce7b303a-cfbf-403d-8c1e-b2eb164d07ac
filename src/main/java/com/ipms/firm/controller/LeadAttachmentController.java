package com.ipms.firm.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.firm.dto.LeadAttachmentDto;
import com.ipms.firm.handler.LeadAttachmentHandler;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

@RequiredArgsConstructor
@RestController
@RequestMapping("lead/attachments")
public class LeadAttachmentController {
    private final LeadAttachmentHandler handler;

    @PostMapping
    public BaseResponse<LeadAttachmentDto> save(@RequestBody LeadAttachmentDto leadAttachmentDto) {
        return BaseResponse.<LeadAttachmentDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.save(leadAttachmentDto))
                .build();
    }
}
