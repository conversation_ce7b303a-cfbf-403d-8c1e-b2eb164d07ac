package com.ipms.firm.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.firm.dto.SectorDto;
import com.ipms.firm.handler.SectorHandler;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping( "sector")
@Tag(name = "sector", description = "This endpoint contains sector APIs ")
@Validated
public class SectorController {
    private final SectorHandler handler;

    @GetMapping
    public BaseResponse<List<SectorDto>> sectorList() {
        return BaseResponse.<List<SectorDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(handler.getAll())
                .build();
    }
}
