package com.ipms.firm.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.firm.dto.ProvinceDto;
import com.ipms.firm.handler.ProvinceHandler;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("province")
@RequiredArgsConstructor
@Tag(name = "province", description = "This endpoints contain province APIs province details.")
public class ProvinceController {
    private final ProvinceHandler handler;

    @GetMapping("/{countryCode}/{cityCode}")
    public BaseResponse<List<ProvinceDto>> getByCountryAndCity(@Valid @PathVariable String countryCode, @Valid @PathVariable String cityCode) {
        return BaseResponse.<List<ProvinceDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getByCountryAndCity(countryCode,cityCode))
                .build();
    }

    @GetMapping("/{countryCode}/{cityCode}/{provinceName}")
    public BaseResponse<ProvinceDto> getByCountryAndCityAndProvince(@Valid @PathVariable String countryCode, @Valid @PathVariable String cityCode, @Valid @PathVariable String provinceName) {
        return BaseResponse.<ProvinceDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getByCountryAndCityAndProvince(countryCode,cityCode,provinceName))
                .build();
    }

}
