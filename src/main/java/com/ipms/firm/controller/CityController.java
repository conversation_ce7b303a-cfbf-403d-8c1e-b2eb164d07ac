package com.ipms.firm.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.firm.dto.CityDto;
import com.ipms.firm.handler.CityHandler;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("city")
@RequiredArgsConstructor
@Tag(name = "city", description = "This endpoints contain city APIs city details.")
public class CityController {
    private final CityHandler handler;

    @GetMapping("/{countryCode}")
    public BaseResponse<List<CityDto>>getByCountry(@Valid @PathVariable String countryCode) {
        return BaseResponse.<List<CityDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getByCountry(countryCode))
                .build();
    }
    @GetMapping("/{countryCode}/{stateCode}")
    public BaseResponse<List<CityDto>> getByCountryAndState(@Valid @PathVariable String countryCode, @Valid @PathVariable String stateCode) {
        return BaseResponse.<List<CityDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getByCountryAndState(countryCode,stateCode))
                .build();
    }

    @GetMapping("/countryAndIso2/{countryCode}/{iso2}")
    public BaseResponse<CityDto> getByCountryAndIso2(@Valid @PathVariable String countryCode, @Valid @PathVariable String iso2) {
        return BaseResponse.<CityDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getByCountryAndIso2(countryCode,iso2))
                .build();
    }

    @PostMapping
    public BaseResponse<CityDto> save(@Valid @RequestBody CityDto dto) {
        return BaseResponse.<CityDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.save(dto))
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        handler.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

}
