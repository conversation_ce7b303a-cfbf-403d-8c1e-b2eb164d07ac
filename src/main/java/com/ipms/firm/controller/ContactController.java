package com.ipms.firm.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.firm.dto.ContactDto;
import com.ipms.firm.dto.ContactPageDto;
import com.ipms.firm.handler.ContactHandler;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;
import javax.validation.Valid;
import javax.validation.constraints.Size;
import java.util.List;

@RestController
@RequestMapping("contact")
@RequiredArgsConstructor
@Tag(name = "contact", description = "This endpoints contain contact APIs country details.")
@Validated
public class ContactController {
    private final ContactHandler handler;

    @GetMapping("/check/{mailAddress}")
    public BaseResponse<Boolean> checkByMailAddress(@Valid @PathVariable String mailAddress) {
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.checkByMailAddress(mailAddress))
                .build();
    }

    @DeleteMapping("/{contactId}/{firmId}")
    public BaseResponse<Void> delete(@PathVariable Long contactId, @PathVariable Long firmId){
        handler.delete(contactId, firmId);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @DeleteMapping("/email/{email}")
    public BaseResponse<Void> delete(@PathVariable String email){
        handler.deleteByEmail(email);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/{firmId}/{page}/{size}")
    public BaseResponse<ContactPageDto> getByFirm(@PathVariable int page,
                                                  @PathVariable int size,
                                                  @PathVariable Long firmId) {
        return BaseResponse.<ContactPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getContactListByFirm(firmId, page, size))
                .build();
    }

    @GetMapping("/{mailAddress}")
    public BaseResponse<List<ContactDto>> getByMailAddress(@PathVariable @Size(min = 3) String mailAddress) {
        return BaseResponse.<List<ContactDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getContactListByMailAddress(mailAddress))
                .build();
    }
}
