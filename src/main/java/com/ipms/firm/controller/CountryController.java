package com.ipms.firm.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.firm.dto.CountryDto;
import com.ipms.firm.handler.CountryHandler;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("country")
@RequiredArgsConstructor
@Tag(name = "country", description = "This endpoints contain country APIs country details.")
public class CountryController {

    private final CountryHandler handler;

    @GetMapping
    public BaseResponse<List<CountryDto>> getAll() {
        return BaseResponse.<List<CountryDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getAll())
                .build();
    }

    @GetMapping("/iso2/{iso2}")
    public BaseResponse<CountryDto> getByIso2(@Valid @PathVariable String iso2) {
        return BaseResponse.<CountryDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getByIso2(iso2))
                .build();
    }

    @GetMapping("/{id}/iso2")
    public BaseResponse<CountryDto> getIso2ById(@Valid @PathVariable Long id) {
        return BaseResponse.<CountryDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getIso2ById(id))
                .build();
    }

}
