package com.ipms.firm.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.firm.dto.*;
import com.ipms.firm.handler.FirmHandler;
import com.ipms.firm.handler.groups.ContactGroup;
import com.ipms.firm.handler.groups.CreateFirmGroup;
import com.ipms.firm.handler.groups.UpdateFirmGroup;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.constraints.Size;
import java.util.List;

@RequiredArgsConstructor
@RestController
@Tag(name = "firm", description = "This endpoint contains firm APIs ")
@Validated
public class FirmController {

    private final FirmHandler handler;

    @PostMapping
    public BaseResponse<FirmDto> save(@RequestBody @Validated(CreateFirmGroup.class) FirmDto dto) {
        var firmDto = handler.save(dto);
        return BaseResponse.<FirmDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(firmDto)
                .build();
    }

    @GetMapping(value = "/summary")
    public BaseResponse<List<FirmSummaryDto>> getFirmListByTitle(@RequestParam(name = "title") @Size(min = 3) String title){
        return BaseResponse.<List<FirmSummaryDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(handler.listFirmSummaryDtoList(title))
                .build();
    }

    @PutMapping(value = "/{id}")
    public BaseResponse<FirmDto> update(@RequestBody @Validated(UpdateFirmGroup.class) FirmDto dto,
                                        @PathVariable Long id) {
        var firmDto = handler.update(dto, id);
        return BaseResponse.<FirmDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(firmDto)
                .build();
    }

    @GetMapping(value = "/{id}")
    public BaseResponse<FirmDto> getFirmById(@PathVariable Long id){
        return BaseResponse.<FirmDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(handler.getById(id))
                .build();
    }

    @GetMapping(value = "/firms")
    public BaseResponse<List<FirmDto>> getFirm(FirmFilterRequest filterRequest){
        return BaseResponse.<List<FirmDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(handler.getAll(filterRequest))
                .build();
    }

    @GetMapping(value = "/firms/summary/{page}/{size}")
    public BaseResponse<PageData<FirmSummaryDto>> getFirmSummaryPage(FirmFilterRequest filterRequest,
                                                           @PathVariable int page,
                                                           @PathVariable int size){
        return BaseResponse.<PageData<FirmSummaryDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(handler.getFirmSummaryPage(filterRequest, page, size))
                .build();
    }

    @PostMapping("request-assignment")
    public BaseResponse<FirmDto> requestAssignment(@RequestBody @Validated({CreateFirmGroup.class, ContactGroup.class})
                                                FirmDto dto) {
        var firmDto = handler.saveWithContact(dto);
        return BaseResponse.<FirmDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(firmDto)
                .build();
    }

    @PostMapping("/{id}/billing-account/{billingAccountId}")
    public BaseResponse<FirmDto> linkBillingAccount(@PathVariable Long id, @PathVariable Long billingAccountId) {
        var firmDto = handler.linkBillingAccount(id, billingAccountId);
        return BaseResponse.<FirmDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(firmDto)
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        handler.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping(value = "/group-companies/{groupCompanyId}")
    public BaseResponse<List<FirmDto>> getFirmListByGroupCompany(@PathVariable Long groupCompanyId){
        return BaseResponse.<List<FirmDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(handler.getFirmListByGroupCompany(groupCompanyId))
                .build();
    }

    @GetMapping(value = "/summary/{title}/{role}")
    public BaseResponse<List<FirmSummaryDto>> getFirmListByTitleAndRole(@PathVariable @Size(min = 3) String title,
                                                                        @PathVariable String role){
        return BaseResponse.<List<FirmSummaryDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(handler.getFirmListByTitleAndRole(title, role))
                .build();
    }

    @GetMapping("/ids/{ids}")
    public BaseResponse<List<FirmDto>> getFirmList(@PathVariable List<Long> ids){
        return BaseResponse.<List<FirmDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getByIdIn(ids))
                .build();
    }

    @PutMapping(value = "/{firmId}/{version}")
    public BaseResponse<FirmDto> updateGroupCompanyFromFirm(@RequestBody GroupCompanyDto dto,
                                                            @PathVariable Long firmId,
                                                            @PathVariable Long version) {
        return BaseResponse.<FirmDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(handler.updateGroupCompany(firmId, dto, version))
                .build();
    }

    @DeleteMapping("/agent/{agentId}/{version}")
    public BaseResponse<Void> deleteAgent(@PathVariable Long agentId, @PathVariable Long version) {
        handler.deleteAgent(agentId, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @DeleteMapping("/right-owner/{rightOwnerId}/{version}")
    public BaseResponse<Void> deleteRightOwner(@PathVariable Long rightOwnerId, @PathVariable Long version) {
        handler.deleteRightOwner(rightOwnerId, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }
}