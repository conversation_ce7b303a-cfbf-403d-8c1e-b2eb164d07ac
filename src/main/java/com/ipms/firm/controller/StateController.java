package com.ipms.firm.controller;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.firm.dto.StateDto;
import com.ipms.firm.handler.StateHandler;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequestMapping("state")
@RequiredArgsConstructor
@Tag(name = "state", description = "This endpoints contain state APIs state details.")
public class StateController {
    private final StateHandler handler;

    @GetMapping("/{countryCode}")
    public BaseResponse<List<StateDto>> getByCountry(@Valid @PathVariable String countryCode) {
        return BaseResponse.<List<StateDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getByCountry(countryCode))
                .build();
    }

    @GetMapping("/countryAndIso2/{countryCode}/{iso2}")
    public BaseResponse<StateDto> getByCountryAndIso2(@Valid @PathVariable String countryCode, @Valid @PathVariable String iso2) {
        return BaseResponse.<StateDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(handler.getByCountryAndIso2(countryCode,iso2))
                .build();
    }

}
