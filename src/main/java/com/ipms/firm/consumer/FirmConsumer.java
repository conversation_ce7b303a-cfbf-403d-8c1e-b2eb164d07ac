package com.ipms.firm.consumer;

import com.ipms.core.common.utils.ObjectUtils;
import com.ipms.firm.dto.FirmEvent;
import com.ipms.firm.service.FirmService;
import lombok.RequiredArgsConstructor;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
public class FirmConsumer {
    private final FirmService service;

    @KafkaListener(topics = "${kafka.firm-associated-topic}")
    public void consumeUpdatedTopic(String eventStr) {
        service.processAssociatedEvent(ObjectUtils.fromJson(eventStr, FirmEvent.class));
    }
}
