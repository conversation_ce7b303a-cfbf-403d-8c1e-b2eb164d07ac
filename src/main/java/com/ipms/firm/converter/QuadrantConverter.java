package com.ipms.firm.converter;

import com.ipms.firm.enums.Quadrant;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class QuadrantConverter implements AttributeConverter<Quadrant, String> {
    @Override
    public String convertToDatabaseColumn(Quadrant quadrant) {
        return quadrant == null ? null : quadrant.getName();
    }

    @Override
    public Quadrant convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : Quadrant.getEnum(s);
    }
}
