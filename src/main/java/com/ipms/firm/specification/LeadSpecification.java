package com.ipms.firm.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.firm.enums.Quadrant;
import com.ipms.firm.model.Lead;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class LeadSpecification extends AbstractSpecification<Lead> {
    private final List<String> statusList;
    private final List<Quadrant> quadrantList;
    private final List<String> leadOwnerList;
    private final List<String> firmList;

    @Override
    public Predicate toPredicate(Root<Lead> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);

        Optional.ofNullable(statusList)
                .ifPresent(value -> predicate.getExpressions().add(root.get("leadStatus").in(statusList)));
        Optional.ofNullable(quadrantList)
                .ifPresent(value -> predicate.getExpressions().add(root.get("quadrant").in(quadrantList)));
        Optional.ofNullable(leadOwnerList)
                .ifPresent(value -> predicate.getExpressions().add(root.get("leadOwner").in(leadOwnerList)));
        Optional.ofNullable(firmList)
                .ifPresent(value -> predicate.getExpressions().add(root.get("firm").in(firmList)));

        return predicate;
    }
}
