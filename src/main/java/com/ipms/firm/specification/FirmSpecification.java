package com.ipms.firm.specification;

import com.ipms.core.specification.AbstractSpecification;
import com.ipms.firm.model.Firm;
import lombok.Setter;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.Optional;

@SuperBuilder
@Setter
public class FirmSpecification extends AbstractSpecification<Firm> {
    private Boolean prospect;
    private Long billingAccountId;

    @Override
    public Predicate toPredicate(Root<Firm> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);

        Optional.ofNullable(prospect)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("prospect"), value)));
        Optional.ofNullable(billingAccountId)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.join("billingAccountIds").in(value))));

        return predicate;
    }


}
