package com.ipms.firm.repository;

import com.ipms.firm.model.Lead;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.stereotype.Repository;

import java.util.Optional;

@Repository
public interface LeadRepository
        extends JpaRepository<Lead, Long>, JpaSpecificationExecutor<Lead>{
    Optional<Lead> findByNameSurnameAndPhoneNumber(String nameSurname, String phoneNumber);
}
