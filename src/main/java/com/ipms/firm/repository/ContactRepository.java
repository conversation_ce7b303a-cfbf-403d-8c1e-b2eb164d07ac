package com.ipms.firm.repository;

import com.ipms.firm.model.Contact;
import com.ipms.firm.model.Firm;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;
import java.util.Set;

@Repository
public interface ContactRepository extends PagingAndSortingRepository<Contact, Long> {
    Optional<Contact> findFirstByMailAddressIgnoreCase(String mailAddress);
    Page<Contact> findByFirmsInOrderByCreatedAtDesc(Set<Firm> firms, Pageable pageable);
    Set<Contact> findByIdIn(List<Long> ids);
    List<Contact> findTop15ByMailAddressContainsOrderByMailAddress(String mailAddress);
}
