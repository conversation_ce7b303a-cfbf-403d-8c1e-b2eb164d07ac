package com.ipms.firm.repository;

import com.ipms.firm.enums.FirmRole;
import com.ipms.firm.model.Firm;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface FirmRepository extends CrudRepository<Firm, Long>, JpaSpecificationExecutor<Firm> {
    Optional<Firm> findByTitleIgnoreCaseAndCountryAndCity(String title, String country, String city);
    List<Firm> findTop30ByTitleContainsIgnoreCaseOrderByTitle(String title);
    List<Firm> findByGroupCompanyIdOrderByTitle(Long groupCompanyId);
    List<Firm> findTop30ByTitleContainsIgnoreCaseAndRoleInOrderByTitle(String title, List<FirmRole> roles);
    List<Firm> findByIdIn (List<Long> ids);
    List<Firm> findByTitle(String title);
    Optional<Firm> findByAgentInfo_Id(Long id);
    Optional<Firm> findByRightOwnerInfo_Id(Long id);
    List<Firm> findByCityAndCountry(String city, String country);
}
