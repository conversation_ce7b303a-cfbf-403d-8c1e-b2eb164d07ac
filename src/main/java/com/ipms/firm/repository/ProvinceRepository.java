package com.ipms.firm.repository;

import com.ipms.firm.model.Province;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface ProvinceRepository  extends CrudRepository<Province, Long> {
    List<Province> findByCountryCodeAndCityCodeOrderByNameAsc(String countryCode, String cityCode);
    Optional<Province> findByCountryCodeAndCityCodeAndName(String countryCode, String cityCode, String provinceName);
}