package com.ipms.firm.repository;

import com.ipms.firm.model.City;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface CityRepository  extends CrudRepository<City, Long> {
    List<City> findByCountryCodeOrderByNameAsc(String countryCode);
    List<City> findByCountryCodeAndStateCodeOrderByNameAsc(String countryCode,String stateCode);
    Optional<City> findByCountryCodeAndIso2(String countryCode,String iso2);
    List<City> findByIso2In(List<String> iso2);
    List<City> findByIso2AndCountryCode(String iso2, String countryCode);
}
