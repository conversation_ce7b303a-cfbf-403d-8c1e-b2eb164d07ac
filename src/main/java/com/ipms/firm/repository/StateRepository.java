package com.ipms.firm.repository;

import com.ipms.firm.model.State;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface StateRepository  extends CrudRepository<State, Long> {
    List<State> findByCountryCodeOrderByNameAsc(String countryCode);
    Optional<State> findByCountryCodeAndIso2(String countryCode,String iso2);
}
