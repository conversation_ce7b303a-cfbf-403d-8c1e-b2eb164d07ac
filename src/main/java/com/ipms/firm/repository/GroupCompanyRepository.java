package com.ipms.firm.repository;

import com.ipms.firm.model.GroupCompany;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface GroupCompanyRepository extends CrudRepository<GroupCompany, Long> {
    Optional<GroupCompany> findByNameIgnoreCase(String name);
    List<GroupCompany> findByNameContainsOrderByName(String name);
}
