package com.ipms.mail.dto;

import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@ToString
@AllArgsConstructor
@NoArgsConstructor
public class MailDto {
    private Long id;
    private String fromAddress;
    private String fromName;
    private String subject;
    @ToString.Exclude
    private String body;
    private LocalDateTime receivedDate;
    private String conversationId;
    private String internetMessageId;
    @Singular
    private List<RecipientDto> recipients;
    @Singular
    private List<AttachmentDto> attachments;
    private boolean hasAttachment;
    private String folder;

    @NotNull
    private List<Long> attachmentIdList;

    private boolean draft;

    @ToString.Exclude
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime updatedAt;

    private Long version;
    private Long issueId;
}
