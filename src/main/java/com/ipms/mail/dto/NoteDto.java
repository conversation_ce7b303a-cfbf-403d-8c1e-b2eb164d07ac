package com.ipms.mail.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDateTime;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class NoteDto {
    private Long id;
    @Size(min = 3, max = 750)
    private String text;
    private LocalDateTime createdAt;
    private String createdBy;
    @NotNull
    private Long correspondenceId;
    private Long correspondenceEmailId;

}
