package com.ipms.document.enums;

import java.util.Arrays;

public enum DocumentType {
    AGREEMENT("1"),
    SEARCH_EXAMINITION_REPORT("2"),
    SEARCH_REPORT("3"),
    EXPERTS_REPORT("4"),
    STATEMENT_FOR_EXP_REP("5"),
    OBJECTION_TO_EXPERTS_REPORT("6"),
    POWER_OF_ATTOR_FR_COURT_ACTION("7"),
    EVIDENCE("8"),
    DEED_OF_ASSIGNMENT("9"),
    REJONDER("10"),
    LISTING_OF_GOODS("11"),
    GENERAL("12"),
    LEGAL_ADVICE("13"),
    PRELIMINARY_INVESTIGATION_DOC("14"),
    OFFICIAL_DECISION("15"),
    CEASE_AND_DESIST_LETTER("16"),
    EXAMINATION_REPORT("17"),
    APPEAL_DECISION_RCA("18"),
    DECISION("19"),
    LICENCE_AGREEMENT("20"),
    TM_RENEWAL_CERTIFICATE("21"),
    CONSENT_LETTER("22"),
    DECLATORY_REPORT_OF_NOTARY("23"),
    PARTNERSHIP_POA_GENERAL("24"),
    PARTNERSHIP_POA_LIMITED("25"),
    PARTNERSHIPPOA_W_OUT_DELEG_POW("26"),
    REPLICATION("27"),
    AGREEMENT_DECLARATION("28"),
    SPECIFICATION("29"),
    NEW_TR_POWER_OF_ATTORNEY_JSC("30"),
    INJECTION_DOCUMENTS("31"),
    COPYRIGHT_CERTIFICATE("32"),
    POA_COURT_ACT_W_DELEG_POWER("33"),
    TR_REGISTRATION_CERTIFICATE("34"),
    SUPREME_COURT_DECISION("35"),
    LEGACY_POWER_OF_ATTOR_FR_COURT_ACTION("36"),
    NOTIFICATION("37"),
    DISTRIBUTION_FORM("38"),
    PRELIMINARY_PROCEEDINGS_REPORT("39"),
    INTERIM_DECISION("40"),
    REPLY_PETITION("41"),
    WRIT_OF_SUMMONS("42"),
    REPLY_TO_REPLY_PETITION("43"),
    SECOND_REPLY_PETITION("44"),
    STATEMENT_PETITION("45"),
    STATEMENT_PETITION_COUNTER_PARTY("46"),
    MINUTES_OF_HEARING("47"),
    MINUTES_OF_PRELIMINARY_EXAMINATION_HEARING("48"),
    MINUTES_OF_REFERRAL_TO_EXPERT("49"),
    APPEAL_PETITION_BEFORE_THE_REGIONAL_APPELLATE_COURT("50"),
    APPEAL_PETITION_BEFORE_THE_REGIONAL_APPELLATE_COURT_COUNTER_PARTY("51"),
    REPLY_TO_APPEAL_PETITION_BEFORE_THE_REGIONAL_APPELLATE_COURT("52"),
    REPLY_TO_APPEAL_PETITION_BEFORE_THE_REGIONAL_APPELLATE_COURT_COUNTER_PARTY("53"),
    AUTHORIZATION_DOCUMENT("54"),
    APPEAL_PETITION_BEFORE_THE_SUPREME_COURT("55"),
    FINALIZATION_STATEMENT("56"),
    REPLY_TO_APPEAL_PETITION_BEFORE_THE_SUPREME_COURT("57"),
    EVIDENCE_PETITION("58"),
    LETTER_OF_UNDERTAKING("59"),
    MINUTES_OF_EXPERT_EXAMINATION ("60"),
    COURT_WARRANT("61"),
    REPLY_TO_COURT_WARRANT("62"),
    INJUNCTION_MINUTES("63"),
    STATEMENT_FOR_EXP_REP_COUNTER_PARTY("64"),
    OBJECTION_TO_EXPERTS_REPORT_COUNTER_PARTY("65"),
    OPPOSITION_TO_PANEL_OF_EXPERTS("66"),
    OPPOSITION_TO_PANEL_OF_EXPERTS_COUNTER_PARTY("67"),
    EXCUSE_PETITION("68"),
    IMPARLANCE("69"),
    MOTION_FOR_PRELIMINARY_INJUNCTION("70"),
    REQUEST_FOR_RETURN_OF_COLLATERAL("71"),
    SPECIFIC_REQUEST("72"),
    UDRP_COMPLAINT("73"),
    APPEAL_PETITION_BEFORE_THE_SUPREME_COURT_COUNTER_PARTY("74"),
    REPLY_C_D_LETTER_COUNTER_PARTY("75"),
    REPLY_C_D_LETTER("76"),
    INDICTMENT("77"),
    TRAINING_PRESENTATION("78"),
    SUSPENSION_OF_RELEASE("79"),
    COMPLAINT_PETITION("80"),
    OBJECTION_PETITION("81"),
    REPLY_TO_OPPOSITION("82"),
    PETITION_FOR_INTERVENTION("83"),
    DEADLINE_SUSPENTION_REQUEST("84"),
    REPLY_TO_SETTLEMENT_INTIVATION("85"),
    CUSTOMS_REGISTRATION("86"),
    MINUTES_OF_POLICE("87"),
    PETITION_FOR_RECONSIDERATION("88"),
    PETITION_FOR_REPLY_TO_RECONSIDERATION("89"),
    JUDICIARY_PAYMENT_ORDER("90"),
    MINUTES_OF_DETERMINATION("91"),
    PETITION_FOR_WITHDRAWAL_OF_THE_COMPLAINT("92"),
    MINUTES_OF_DESTRUCTION("93"),
    CONSILIDATION_DECISION("94"),
    OBJECTION_PETITION_COUNTER_PARTY("95"),
    OLD_POA_AS("96"),
    NORTHERN_CYPRUS_POA("97"),
    FOREIGN_COUNTRY_POA("98");

    private final String value;

    DocumentType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static DocumentType getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }

}
