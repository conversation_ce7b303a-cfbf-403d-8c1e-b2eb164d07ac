package com.ipms.document.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum DocumentResponseCode implements Code {
    DOCUMENT_NOT_FOUND(1000, "error.code.document.not_found"),
    DOCUMENT_CAN_NOT_REMOVE(1001, "error.code.document.can_not_remove");
    private final Integer code;
    private final String messageKey;

    DocumentResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
