package com.ipms.document.enums;

import java.util.Arrays;

public enum EvidenceType {
    TEST("1"),
    SAMPLE_COURT_DECISION("2"),
    PRIVATE_EXPORT_REPORT("3"),
    PRECEDENT_EXPORT_REPORT("4"),
    FINANCIAL_DATA("5"),
    REGISTRATION_CERT("6"),
    DOC_ON_NOTORIETY("7"),
    OFFICIAL_DECISION("8"),
    ADVERTISEMENT_DATA("9"),
    AGREEMENT("10"),
    OTHERS("11"),
    TASK_DOCUMENT("12"),
    PRIOR_ART("14"),
    PRECEDENT_PETITION("15"),
    TR_GOOD_SERVICES_INVOICE("16"),
    INT_GOOD_SERVICES_INVOICE("17"),
    TR_VIEWER_SALES_NUMBERS("18"),
    INT_VIEWER_SALES_NUMBERS("19"),
    INT_REGISTRATION_CERTIFICATE("20"),
    DISTRIBUTOR_LIST("21"),
    CATALOGUE_PRICE_LIST_PRODUCT_CODE("22"),
    PRODUCT_PACKAGE_SIGNBOARD("23"),
    ADVERTISEMENT("24"),
    PUBLIC_POOL_SURVEY("25"),
    ADVERTISEMENT_INVOICE("26"),
    FAIRS_TRADE_SHOWS("27"),
    TRADE_ACTIVITY_AUTHORIZATION_LICENCE("28"),
    COPYRIGHT("29"),
    PRECEDENT_TURK_PATENT_DECISION("30"),
    AFFIDAVIT("31"),
    DESTRUCTION_MINUTES("32");

    private final String value;

    EvidenceType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static EvidenceType getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
