package com.ipms.document.enums;

import java.util.Arrays;

public enum MediaType {

    TEST("0"),
    VIDEO("1"),
    IMAGE_PHOTO("2"),
    PDF("3"),
    WORD("4"),
    POWERPOINT("5"),
    EXCEL("6"),
    PHYSICAL_PRODUCT("7"),
    DRAWING("8");

    private final String value;

    MediaType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static MediaType getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
