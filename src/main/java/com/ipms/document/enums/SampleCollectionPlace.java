package com.ipms.document.enums;

import java.util.Arrays;

public enum SampleCollectionPlace {

    CLIENT("1"),
    PURCHASE("2"),
    NOTARY_DETERMINATION("3"),
    INVESTIGATION_COMPANY("4"),
    RAID("5"),
    CUSTOM("6"),
    COURT("7");

    private final String value;

    SampleCollectionPlace(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static SampleCollectionPlace getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
