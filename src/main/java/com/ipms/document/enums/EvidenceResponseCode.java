package com.ipms.document.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum EvidenceResponseCode implements Code {

    EVIDENCE_CAN_NOT_REMOVE(1000, "error.code.evidence.can_not_remove"),
    EVIDENCE_NOT_FOUND(1001, "error.code.evidence.not_found");

    private final Integer code;
    private final String messageKey;

    EvidenceResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
