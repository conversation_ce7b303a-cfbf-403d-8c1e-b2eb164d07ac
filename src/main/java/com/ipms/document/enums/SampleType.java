package com.ipms.document.enums;

import java.util.Arrays;

public enum SampleType {

    VALUABLE_ITEM("1"),
    CHEMICALS("2"),
    PHARMACEUTICAL("3"),
    MACHINE("4"),
    TEXTILE("5"),
    ELECTRONIC_ITEM("6"),
    <PERSON>THER("7");

    private final String value;

    SampleType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static SampleType getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
