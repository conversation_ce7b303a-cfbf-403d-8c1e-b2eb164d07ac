package com.ipms.document.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum SampleResponseCode implements Code {
    SAMPLE_CAN_NOT_REMOVE(1000, "error.code.sample.can_not_remove"),
    SAMPLE_NOT_FOUND(1001, "error.code.sample.not_found");

    private final Integer code;
    private final String messageKey;

    SampleResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
