package com.ipms.document.enums;

import java.util.Arrays;

public enum SampleLocation {

    COURT("1"),
    ASSOCIATE("2"),
    DERYA_HAN_OPEN_CABINET("3"),
    DERYA_HAN_CABINET_WITH_DRAWERS("4"),
    DERYA_HAN_CLOSED_CABINET("5"),
    THROWN_AWAY("6"),
    CLIENT("7"),
    TRUSTEE("8");

    private final String value;

    SampleLocation(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static SampleLocation getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
