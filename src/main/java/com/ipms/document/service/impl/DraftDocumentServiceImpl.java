package com.ipms.document.service.impl;

import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.enums.Domain;
import com.ipms.core.common.enums.NotificationDestinationType;
import com.ipms.core.common.enums.NotificationType;
import com.ipms.core.common.model.NotificationEvent;
import com.ipms.document.client.ActivityClient;
import com.ipms.document.client.MailClient;
import com.ipms.document.client.MatterClient;
import com.ipms.document.dto.*;
import com.ipms.document.exception.DocumentNotFoundException;
import com.ipms.document.mapper.DraftDocumentMapper;
import com.ipms.document.model.DraftDocument;
import com.ipms.document.repository.DraftDocumentRepository;
import com.ipms.document.service.DraftDocumentService;
import com.ipms.document.specification.DraftDocumentSpecification;
import com.ipms.document.util.DocumentUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.Nullable;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
@Slf4j
public class DraftDocumentServiceImpl implements DraftDocumentService {
    
    private static final String PTT_EMAIL_SUBJECT_KEYWORD = "Yeni Elektronik Tebligat";
    private static final String DRAFT_DOCUMENT_ASSIGNEE_NOTIFICATION = "draftDocumentAssignee";
    private final DraftDocumentRepository repository;
    private final MatterClient matterClient;
    private final ActivityClient activityClient;
    private final DraftDocumentMapper mapper;
    private final ProducerService producerService;
    private final MailClient mailClient;

    @Override
    public DraftDocumentPageDto getAll(DraftDocumentFilterRequest request, int page, int size) {
        var fromNames = request.getFromNames();
        var filteredIds = getFilteredDraftDocumentIds(request, fromNames);
        
        var draftDocumentPage = repository.findAll(DraftDocumentSpecification.builder()
                .ids(filteredIds)
                .sortField(request.getSortField())
                .sortDirection(request.getSortDirection())
                .build(), PageRequest.of(page, size));

        return DraftDocumentPageDto.builder()
                .totalElements(draftDocumentPage.getTotalElements())
                .totalPages(draftDocumentPage.getTotalPages())
                .draftDocuments(draftDocumentPage.getContent()
                        .stream()
                        .map(mapper::toDto)
                        .toList())
                .build();
    }
    
    private List<Long> getFilteredDraftDocumentIds(DraftDocumentFilterRequest request, List<String> fromNames) {
        var draftDocuments = repository.findAll(DraftDocumentSpecification.builder()
                .assignees(request.getAssignees())
                .isDocumentIdNull(request.getIsDocumentIdNull())
                .build());

        if (fromNames == null || fromNames.isEmpty()) {
            return draftDocuments.stream().map(DraftDocument::getId).toList();
        }

        var mailIds = getMailIdsByFromNames(draftDocuments, fromNames);
        return draftDocuments.stream()
                .filter(draftDocument -> mailIds.contains(draftDocument.getEmailId()))
                .map(DraftDocument::getId)
                .toList();
    }
    
    private List<Long> getMailIdsByFromNames(List<DraftDocument> draftDocuments, List<String> fromNames) {
        var allMailIds = draftDocuments.stream().map(DraftDocument::getEmailId).toList();
        if (allMailIds.isEmpty()) {
            return List.of();
        }
        
        return mailClient.getMailSummary(allMailIds).getPayload().stream()
                .filter(payload -> fromNames.contains(payload.getFromName()))
                .map(MailResponse.Payload::getId)
                .toList();
    }

    public DraftDocument saveDraftDocument(Long mailEventId, String mailEventSubject) {
        String docketNo;

        if (mailEventSubject.contains(PTT_EMAIL_SUBJECT_KEYWORD)) {
            var mailBody = mailClient.get(mailEventId).getPayload().getBody();
            docketNo = DocumentUtils.extractDocketNoForPTT(mailBody);
        } else {
            docketNo = DocumentUtils.extractDocketNoForUyap(mailEventSubject);
        }

        log.debug("Processing mail event - ID: {}, docketNo: {}, subject: {}", mailEventId, docketNo, mailEventSubject);

        ActivityResponse activityResponse = getActivityResponse(docketNo);
        String paralegal = getParalegal(activityResponse);
        Long activityId = getActivityId(activityResponse);

        return repository.save(DraftDocument.builder()
                .emailId(mailEventId)
                .assignee(paralegal)
                .activityId(activityId)
                .build());
    }

    @Override
    public DraftDocument processMailEvent(MailEvent mailEvent) {
        var mailEventId = mailEvent.getId();
        var mailEventSubject = mailEvent.getSubject();

        return saveDraftDocument(mailEventId, mailEventSubject);
    }

    @Override
    public DraftDocumentDto assignToParalegal(Long id, DraftDocumentDto dto) {
        var draftDocument = repository.findById(id).orElseThrow(DocumentNotFoundException::new);
        draftDocument.setAssignee(dto.getAssignee());
        var saved = repository.save(draftDocument);

        sendNotification(id, draftDocument.getAssignee());
        return mapper.toDto(saved);
    }

    private void sendNotification(Long id, String assignee) {
        producerService.sendNotification(NotificationEvent.builder()
                .domain(Domain.IPMS_DOCUMENT)
                .domainObjectId(id)
                .destinationType(NotificationDestinationType.USER)
                .destination(assignee)
                .type(NotificationType.ASSIGNMENT)
                .detail(DRAFT_DOCUMENT_ASSIGNEE_NOTIFICATION)
                .build());
    }

    private String getParalegal(ActivityResponse activityResponse) {
        if (activityResponse == null) return null;

        return matterClient.getParalegalByMatterId(activityResponse.getPayload().getMatterId()).getPayload();
    }

    private Long getActivityId(ActivityResponse activityResponse) {
        if (activityResponse == null) return null;

        return activityResponse.getPayload().getId();
    }

    @Nullable
    private ActivityResponse getActivityResponse(String docketNo) {
        if (StringUtils.isBlank(docketNo)) {
            return null;
        }

        ActivityResponse activityResponse = activityClient.getMatterIdByDocketNo(docketNo);
        if (activityResponse.getPayload() == null) {
            return null;
        }
        return activityResponse;
    }

    @Override
    public DraftDocumentDto linkToDocument(Long id, DraftDocumentDto dto) {
        var draftDocument = repository.findById(id).orElseThrow(DocumentNotFoundException::new);
        draftDocument.setDocumentId(dto.getDocumentId());
        var saved = repository.save(draftDocument);

        return mapper.toDto(saved);
    }

    @Override
    public void delete(Long id, Long version) {
        var draftDocument = getDraftDocumentById(id).toBuilder()
                .version(version)
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(draftDocument);
    }

    private DraftDocument getDraftDocumentById(Long id) {
        return repository.findById(id).orElseThrow(DocumentNotFoundException::new);
    }
}
