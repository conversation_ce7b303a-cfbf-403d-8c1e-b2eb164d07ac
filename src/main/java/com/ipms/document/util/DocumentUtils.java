package com.ipms.document.util;

import lombok.experimental.UtilityClass;

import java.util.regex.Matcher;
import java.util.regex.Pattern;

@UtilityClass
public class DocumentUtils {
    public String extractDocketNoForUyap(String input) {
        Pattern pattern = Pattern.compile("#ES-(.*)\\s-");
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }

    public String extractDocketNoForPTT(String input) {
        Pattern pattern = Pattern.compile("\\ \\[(\\d*/\\d*)\\]\\ \\[");
        Matcher matcher = pattern.matcher(input);

        if (matcher.find()) {
            return matcher.group(1);
        }
        return null;
    }
}
