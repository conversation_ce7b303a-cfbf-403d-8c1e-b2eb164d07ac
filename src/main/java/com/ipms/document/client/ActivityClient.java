package com.ipms.document.client;


import com.ipms.document.dto.ActivityLinkedResponse;
import com.ipms.document.dto.ActivityResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ipms-activity")
public interface ActivityClient {
    @PutMapping("/api/activity/document/{activityId}/{documentId}")
    ActivityResponse addDocument(@PathVariable Long activityId,
                                 @PathVariable Long documentId,
                                 @RequestParam(name = "created") <PERSON><PERSON><PERSON> created);

    @GetMapping("/api/activity/document/{documentId}")
    ActivityLinkedResponse checkDocumentLinked(@PathVariable Long documentId);

    @PutMapping("/api/activity/evidence/{activityId}/{evidenceId}")
    ActivityResponse addEvidence(@PathVariable Long activityId,
                                 @PathVariable Long evidenceId);

    @GetMapping("/api/activity/evidence/{evidenceId}")
    ActivityLinkedResponse checkEvidenceLinked(@PathVariable Long evidenceId);

    @PutMapping("/api/activity/sample/{activityId}/{sampleId}")
    ActivityResponse addSample(@PathVariable Long activityId,
                               @PathVariable Long sampleId);

    @GetMapping("/api/activity/sample/{sampleId}")
    ActivityLinkedResponse checkSampleLinked(@PathVariable Long sampleId);

    @GetMapping("/api/activity/get-by-docket-no")
    ActivityResponse getMatterIdByDocketNo(@RequestParam String docketNo);
}
