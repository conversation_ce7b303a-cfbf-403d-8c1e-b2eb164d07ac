package com.ipms.document.client;

import com.ipms.document.dto.MatterResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.RequestParam;

@FeignClient(name = "ipms-matter", path = "/api/matter")
public interface MatterClient {

    @GetMapping("/paralegal")
    MatterResponse getParalegalByMatterId(@RequestParam Long matterId);
}
