package com.ipms.document.client;

import com.ipms.document.dto.MailDto;
import com.ipms.document.dto.MailExtendedResponse;
import com.ipms.document.dto.MailResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

import java.util.List;

@FeignClient(name = "ipms-mail")
public interface MailClient {
    @GetMapping("/api/mail/summaries/{ids}")
    MailResponse getMailSummary(@PathVariable List<Long> ids);

    @GetMapping("/api/mail/{id}")
    MailExtendedResponse get(@PathVariable Long id);
}
