package com.ipms.document.model;

import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import com.ipms.document.enums.DelegationPowerType;
import com.ipms.document.enums.DocumentResult;
import com.ipms.document.enums.DocumentType;
import com.ipms.document.enums.POALimited;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "document")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class Document extends VersionedEntity {
    @Column
    private DocumentType type;

    @Column
    private LocalDate date;

    @Column
    private LocalDate expiryDate;

    @Column
    private LocalDate notificationDate;

    @OneToMany(mappedBy="document", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<Attachment> attachments;

    @ElementCollection
    @CollectionTable(name = "document_firms", joinColumns = @JoinColumn(name = "document_id", referencedColumnName = "id"))
    @Column(name = "firm")
    private List<Long> firms;

    @Column
    private String physicalArchiveNo;

    @Column(columnDefinition = "TEXT")
    private String description;

    @Column
    private POALimited poaLimited;

    @Column
    private DelegationPowerType delegationPowerType;

    @ElementCollection
    @CollectionTable(name = "document_experts", joinColumns = @JoinColumn(name = "document_id", referencedColumnName = "id"))
    @Column(name = "expert")
    private List<Long> experts;

    @Enumerated(EnumType.STRING)
    @Column
    private DocumentResult result;

    @ElementCollection
    @CollectionTable(name = "document_local_attorneys", joinColumns = @JoinColumn(name = "document_id", referencedColumnName = "id"))
    @Column(name = "local_attorney")
    private List<String> localAttorneys;

    @Column
    private Boolean noTransfer;
}
