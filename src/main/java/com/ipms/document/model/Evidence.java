package com.ipms.document.model;

import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import com.ipms.document.enums.EvidenceType;
import com.ipms.document.enums.MediaType;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "digital_evidence")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class Evidence extends VersionedEntity {

    @Column(columnDefinition = "TEXT")
    private String name;

    @Column
    private EvidenceType evidenceType;

    @Column
    private MediaType mediaType;

    @Column(columnDefinition = "TEXT")
    private String description;

    @ElementCollection
    @CollectionTable(name = "digital_evidence_firms", joinColumns = @JoinColumn(name = "evidence_id", referencedColumnName = "id"))
    @Column(name = "firm")
    private List<Long> firms;

    @OneToMany(mappedBy="evidence", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<EvidenceAttachment> attachments;
}
