package com.ipms.document.model;

import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import com.ipms.document.enums.SampleCollectionPlace;
import com.ipms.document.enums.SampleLocation;
import com.ipms.document.enums.SampleType;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Type;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "sample")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class Sample extends VersionedEntity {

    @Column
    private String name;

    @Column
    private String description;

    @ElementCollection
    @CollectionTable(name = "sample_firms", joinColumns = @JoinColumn(name = "sample_id", referencedColumnName = "id"))
    @Column(name = "firm")
    private List<Long> firms;

    @Column
    private SampleType sampleType;

    @Column
    private SampleCollectionPlace collectionPlace;

    @Column
    private Long quantity;

    @OneToMany(mappedBy="sample", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<SampleAttachment> attachments;

    @Column
    private SampleLocation sampleLocation;

    @Column
    private String associate;

    @Column
    private LocalDateTime deliveryDate;

    @Column
    private String shelfInfo;

    @Column(columnDefinition = "TEXT")
    private String note;

    @Column
    private LocalDate arrivalDate;

    @Column
    private Long receiptInvoiceNo;

    @Column(name = "is_original", columnDefinition = "SMALLINT")
    @Type(type = "org.hibernate.type.NumericBooleanType")
    private boolean original;
}
