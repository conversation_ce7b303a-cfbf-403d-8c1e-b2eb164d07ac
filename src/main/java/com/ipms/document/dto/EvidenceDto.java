package com.ipms.document.dto;

import com.ipms.document.dto.groups.CreateGroup;
import com.ipms.document.dto.groups.UpdateGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvidenceDto {

    @NotNull(groups = UpdateGroup.class)
    private Long id;
    @NotNull(groups = UpdateGroup.class)
    private Long version;
    @NotBlank(groups = {UpdateGroup.class, CreateGroup.class})
    private String name;
    @NotEmpty(groups = {UpdateGroup.class, CreateGroup.class})
    private List<Long> firms;
    @NotBlank(groups = {UpdateGroup.class, CreateGroup.class})
    private String evidenceType;
    @NotBlank(groups = {UpdateGroup.class, CreateGroup.class})
    private String mediaType;
    private String description;
    private String createdBy;
    private List<EvidenceAttachmentDto> attachments;
    private LocalDateTime createdAt;
    @NotNull(groups = CreateGroup.class)
    private Long activityId;
    private boolean hasMailAttachment;
}
