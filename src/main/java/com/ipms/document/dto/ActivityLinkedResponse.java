package com.ipms.document.dto;

import lombok.*;

import java.util.List;
import java.util.Optional;

@Getter
@Setter
@AllArgsConstructor
@NoArgsConstructor
@Builder
public class ActivityLinkedResponse {
    private int code;
    private List<ActivityResponse.Payload> payload;

    public Optional<Boolean> linked() {
        return payload.isEmpty() ? Optional.empty() : Optional.of(Boolean.TRUE);
    }
}
