package com.ipms.document.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DraftDocumentFilterRequest {
    private String assignee;
    private List<String> assignees;
    private Boolean isDocumentIdNull;
    private String sortField;
    private String sortDirection;
    private List<String> fromNames;
}
