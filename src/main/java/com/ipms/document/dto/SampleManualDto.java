package com.ipms.document.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SampleManualDto {
    @NotBlank
    private String name;
    private String description;
    @NotNull
    private List<Long> firms;
    @NotBlank
    private String sampleType;
    @NotBlank
    private String collectionPlace;
    @NotNull
    private Long quantity;
    @NotBlank
    private String sampleLocation;
    private String associate;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime deliveryDate;
    private String shelfInfo;
    private String note;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate arrivalDate;
    private Long receiptInvoiceNo;
    private boolean original;
    @NotNull
    private Long activityId;
}
