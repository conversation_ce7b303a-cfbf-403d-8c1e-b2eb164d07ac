package com.ipms.document.dto;

import com.ipms.document.annotation.StringListSize;
import com.ipms.document.enums.DocumentResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentDto {
    private Long id;
    @NotNull
    private Long version;
    @NotEmpty
    private String type;
    private LocalDate date;
    private LocalDate expiryDate;
    private LocalDate notificationDate;
    @NotEmpty
    private List<Long> firms;
    private String createdBy;
    private LocalDateTime createdAt;
    private String updatedBy;
    private LocalDateTime updatedAt;
    private List<AttachmentDto> attachments;
    private boolean hasMailAttachment;
    private String physicalArchiveNo;
    private String description;
    private String poaLimited;
    private String delegationPowerType;
    private List<Long> experts;
    private DocumentResult result;
    @StringListSize(max = 50)
    private List<String> localAttorneys;
}
