package com.ipms.document.dto;

import lombok.*;

import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class MailResponse {
    private int code;
    private List<Payload> payload;

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Payload {
        private Long id;
        private String fromAddress;
        private String fromName;
        private String subject;
        private boolean hasAttachment;
        private String toAddress;
        private String conversationId;
        private String folder;
    }
}
