package com.ipms.document.dto;

import com.ipms.document.dto.groups.CreateGroup;
import com.ipms.document.dto.groups.UseEvidenceGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class EvidenceCreateDto {
    @NotNull(groups = CreateGroup.class)
    private String contentType;
    @NotNull(groups = CreateGroup.class)
    private String fileName;
    @NotNull(groups = CreateGroup.class)
    private String fileUniqueName;
    @NotBlank
    private String name;
    @NotEmpty
    private List<Long> firms;
    @NotBlank
    private String evidenceType;
    @NotBlank
    private String mediaType;
    private String description;
    @NotNull
    private Long activityId;
    @NotNull(groups = UseEvidenceGroup.class)
    private List<EvidenceAttachmentDto> attachments;
}
