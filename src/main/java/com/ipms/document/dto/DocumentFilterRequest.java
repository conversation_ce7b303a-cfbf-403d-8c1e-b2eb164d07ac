package com.ipms.document.dto;

import com.ipms.document.enums.DocumentResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentFilterRequest {
    private List<String> types;
    private List<Long> ids;
    private Long firmId;
    private Long expertId;
    private String localAttorneyId;
    private String sortField;
    private String sortDirection;
    private List<DocumentResult> results;
}
