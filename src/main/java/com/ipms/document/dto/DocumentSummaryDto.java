package com.ipms.document.dto;

import com.ipms.document.enums.DocumentResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentSummaryDto {
    private Long id;
    private LocalDate date;
    private DocumentResult result;
    private LocalDate expiryDate;
    private List<String> localAttorneys;
}
