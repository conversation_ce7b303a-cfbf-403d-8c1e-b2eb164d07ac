package com.ipms.document.mapper;


import com.ipms.config.storage.model.StorageFile;
import com.ipms.document.dto.*;
import com.ipms.document.model.Attachment;
import com.ipms.document.model.Document;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DocumentMapper {
    DocumentDto toDto(Document document);
    DocumentSummaryDto toDocumentSummaryDto(Document document);

    @Mapping(target = "date", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "notificationDate", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "expiryDate", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "physicalArchiveNo", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "poaLimited", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "experts",nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "result", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    @Mapping(target = "localAttorneys", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.SET_TO_NULL)
    Document toDocumentFromDto(DocumentDto dto, @MappingTarget Document document);
    StorageFile toStorageFile(AttachmentDto attachmentDto);
    Attachment toAttachment(DocumentCreateDto documentCreateDto);
    Document toDocument(DocumentCreateDto documentCreateDto);
    Document toDocument(DocumentManualDto documentManualDto);
}
