package com.ipms.document.mapper;

import com.ipms.config.storage.model.StorageFile;
import com.ipms.document.dto.SampleAttachmentDto;
import com.ipms.document.dto.SampleDto;
import com.ipms.document.dto.SampleManualDto;
import com.ipms.document.model.Sample;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface SampleMapper {
    SampleDto toDto(Sample sample);
    Sample toSampleFromDto(SampleDto dto, @MappingTarget Sample sample);
    StorageFile toStorageFile(SampleAttachmentDto attachmentDto);
    Sample toSample(SampleManualDto dto);
}
