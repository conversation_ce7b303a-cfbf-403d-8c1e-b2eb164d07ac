package com.ipms.activity.mapper;

import com.ipms.activity.dto.RenewalHistoryDto;
import com.ipms.activity.model.RenewalHistory;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

import java.util.List;

@Mapper(componentModel = "spring")
public interface RenewalHistoryMapper {
    RenewalHistoryDto toDto(RenewalHistory dto);
    List<RenewalHistoryDto> toDtoList(List<RenewalHistory> renewalHistoryList);

    @Mapping(target = "id", ignore = true)
    RenewalHistory toRenewalHistoryFromDto(RenewalHistoryDto dto, @MappingTarget RenewalHistory renewalHistory);}
