package com.ipms.activity.service;

import com.ipms.activity.dto.RenewalHistoryDto;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.RenewalHistory;

import java.util.List;

public interface RenewalHistoryService {
    RenewalHistory save(RenewalHistory dto);
    List<RenewalHistoryDto> getByRenewalId(Long renewalId);
    RenewalHistoryDto update(Long id, RenewalHistoryDto dto);
    Long calculateNextPeriod(Activity renewalActivity);
}
