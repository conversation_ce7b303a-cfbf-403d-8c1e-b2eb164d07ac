package com.ipms.activity.service.impl;

import com.ipms.activity.dto.RenewalHistoryDto;
import com.ipms.activity.enums.RenewalHistoryResponseCode;
import com.ipms.activity.exception.RenewalHistoryException;
import com.ipms.activity.mapper.RenewalHistoryMapper;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.RenewalHistory;
import com.ipms.activity.repository.RenewalHistoryRepository;
import com.ipms.activity.service.RenewalHistoryService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.util.List;

@RequiredArgsConstructor
@Service
public class RenewalHistoryServiceImpl implements RenewalHistoryService {
    private final RenewalHistoryRepository repository;
    private final RenewalHistoryMapper mapper;

    @Override
    public List<RenewalHistoryDto> getByRenewalId(Long renewalId) {
        var renewalHistoryList = repository.findByRenewal_IdOrderByPeriodAsc(renewalId);
        return mapper.toDtoList(renewalHistoryList);
    }

    @Override
    public RenewalHistoryDto update(Long id, RenewalHistoryDto dto) {
        var renewalHistory = mapper.toRenewalHistoryFromDto(dto, getRenewalHistoryById(id).toBuilder().build());
        return mapper.toDto(repository.save(renewalHistory));
    }

    @Override
    public Long calculateNextPeriod(Activity renewalActivity) {
        return repository.countByRenewal(renewalActivity.getRenewal()) + 1;
    }

    private RenewalHistory getRenewalHistoryById(Long id) {
        return repository.findById(id)
                .orElseThrow(() -> new RenewalHistoryException(RenewalHistoryResponseCode.NOT_FOUND));
    }

    @Override
    public RenewalHistory save(RenewalHistory renewalHistory) {
        return repository.save(renewalHistory);
    }
}
