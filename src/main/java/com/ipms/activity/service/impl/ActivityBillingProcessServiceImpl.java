package com.ipms.activity.service.impl;

import com.ipms.activity.client.*;
import com.ipms.activity.dto.*;
import com.ipms.activity.enums.ActivityResponseCode;
import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.enums.BillingStatus;
import com.ipms.activity.exception.*;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.ActivityRevisionProjection;
import com.ipms.activity.model.TSICPackage;
import com.ipms.activity.repository.ActivityRepository;
import com.ipms.activity.service.ActivityBillingProcessService;
import com.ipms.activity.service.ActivityService;
import com.ipms.activity.service.TSICPackageService;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.config.security.utils.SecurityUtils;
import com.ipms.core.common.utils.DateUtils;
import com.ipms.core.common.utils.NumberUtils;
import com.ipms.core.common.utils.ObjectUtils;
import com.ipms.core.entity.Revision;
import com.ipms.core.service.RevisionService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
@Slf4j
public class ActivityBillingProcessServiceImpl implements ActivityBillingProcessService {
    public static final String BILLING_ISSUE_TYPE = "BILLING";
    private final ActivityService activityService;
    private final ActivityRepository activityRepository;
    private final TSICPackageService tsicPackageService;
    private final IssueClient issueClient;
    private final BillingClient billingClient;
    private final ParamCommandClient paramCommandClient;
    private final MatterClient matterClient;
    private final ParameterClient parameterClient;
    private final ProducerService producerService;
    private final RevisionService<ActivityRevisionProjection> revisionService;

    @Value("${param.auto-billing-time}")
    private String autoBillingTimeParam;

    @Value("${param.auto-billing-month}")
    private String autoBillingMonthParam;

    @Value("${param.before-billing-day}")
    private int beforeBillingDay;

    @Value("${kafka.billing-approved-topic}")
    private String billingApprovedTopic;

    @Value("${kafka.invoice-is-ready-topic}")
    private String invoiceIsReadyTopic;

    @Value("${kafka.close-issue-topic}")
    private String closeIssueTopic;


    @Override
    public void startBillingProcessByBillingDay(LocalDate processDate) {
        var activities = activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull().stream()
                .filter(this::isBillableActivityType)
                .toList();
        var holidays = getHolidays();
        activities.forEach(activity -> {
            try {
                var parameters = getTypeParametersWithDates(activity.getType().name());
                var startDate = addWorkDay(processDate, holidays, beforeBillingDay);
                var favorableDates = calculateFavorableDates(startDate, holidays);
                var billableBillingAccount = getBillableBillingAccount(activity, favorableDates);
                if (BooleanUtils.isTrue(billableBillingAccount.getIsBillable())) {
                    var tsicPackageOptional = tsicPackageService.getActivePackage(activity.getTsicPackages());

                    if (hasBillableRecordByBillingDay(activity, tsicPackageOptional)) {
                        createIssueWithDueDate(activity, parameters, billableBillingAccount.getBillingDay());
                        tsicPackageOptional
                                .ifPresent(lastPackage -> updateIncludedInCharge(activity, lastPackage));
                    }
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    private boolean hasBillableRecordByBillingDay(Activity activity, Optional<TSICPackage> tsicPackageOptional) {
        return hasBillableExpenseOrDisbursement(activity) || hasBillableTimesForBillingDay(activity, tsicPackageOptional);
    }

    private boolean hasBillableTimesForBillingDay(Activity activity, Optional<TSICPackage> lastPackageOptional) {
        return lastPackageOptional
                .map(tsicPackage -> hasBillableTimesheetByTsicPackageForBillingDay(activity, tsicPackage))
                .orElseGet(() -> hasBillableTimesheet(activity));
    }

    private boolean hasBillableTimesheetByTsicPackageForBillingDay(Activity activity, TSICPackage tsicPackage) {
        var totalBillableTime = getTotalBillableTimeByTsicPackage(activity, tsicPackage);
        var totalBillableTimeForFds = getTotalBillableTimeByTsicPackageForFds(activity, tsicPackage);
        if (totalBillableTime == null || totalBillableTimeForFds == null) {
            return false;
        }

        boolean isBilled = BooleanUtils.isTrue(tsicPackage.getInvoiced());
        boolean isUnbilled = BooleanUtils.isNotTrue(tsicPackage.getInvoiced());
        boolean isTotalBillableTimeGreaterThanZero = totalBillableTime.compareTo(BigDecimal.ZERO) > 0;
        boolean isTotalBillableTimeGreaterThanBillableTime
                = totalBillableTimeForFds.compareTo(tsicPackage.getIncludedCharge()) > 0;

        return (isUnbilled && isTotalBillableTimeGreaterThanZero)
                || (isBilled && isTotalBillableTimeGreaterThanBillableTime);
    }

    @Override
    public void startBillingProcessByBillingAmount() {
        var activities = activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNull().stream()
                .filter(this::isBillableActivityType)
                .toList();
        var activitiesWithEmptyBillingDay = getActivitiesWithEmptyBillingDay(activities);
        activitiesWithEmptyBillingDay.forEach(activity -> {
            try {
                var tsicPackageOptional = tsicPackageService.getActivePackage(activity.getTsicPackages());

                if (hasBillableRecordByAmount(activity, tsicPackageOptional)) {
                    createIssue(activity);
                    tsicPackageOptional
                            .ifPresent(lastPackage -> updateIncludedInCharge(activity, lastPackage));
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    private boolean hasBillableRecordByAmount(Activity activity, Optional<TSICPackage> tsicPackageOptional) {
        return isTotalAmountOfExpenseAndDisbursementMoreThanBillingAmount(activity)
                || hasBillableTimesheetByAmount(activity, tsicPackageOptional);
    }

    private boolean isTotalAmountOfExpenseAndDisbursementMoreThanBillingAmount(Activity activity) {
        return billingClient.isTotalAmountMoreThanBillingAmount(activity.getId()).isPayload();
    }

    @NotNull
    private Boolean hasBillableTimesheetByAmount(Activity activity, Optional<TSICPackage> tsicPackageOptional) {
        return tsicPackageOptional
                .map(tsicPackage -> hasBillableTimesheetByTsicPackage(activity, tsicPackage,
                                NumberUtils.parseBigDecimal(getParamValue(autoBillingTimeParam))))
                .orElseGet(() -> hasBillableTimesheetForEndDate(activity));
    }

    private boolean hasBillableTimesheetForEndDate(Activity activity) {
        var endDate = beforeMonthsByParam();
        var timesheetResponse = billingClient.getUnbilledTimesByActivityIdAndEndDate(activity.getId(), endDate);
        return isTotalBillableTimeMoreThanZero(timesheetResponse);
    }

    private @NotNull LocalDate beforeMonthsByParam() {
        return LocalDate.now().minusMonths(Long.parseLong(getParamValue(autoBillingMonthParam)));
    }

    @Override
    public void startBillingProcessByStatus(LocalDate processDate) {
        var activities = getActivitiesForBillingStatus(processDate);
        var activitiesWithEmptyBillingDay = getActivitiesWithEmptyBillingDay(activities);
        activitiesWithEmptyBillingDay.forEach(activity -> {
            try {
                var tsicPackageOptional = tsicPackageService.getActivePackage(activity.getTsicPackages());
                if (isTotalAmountMoreThanZero(activity)) {
                    createIssue(activity);
                    tsicPackageOptional
                            .ifPresent(lastPackage -> updateIncludedInCharge(activity, lastPackage));
                }
            } catch (Exception e) {
                log.error(e.getMessage(), e);
            }
        });
    }

    private boolean isTotalAmountMoreThanZero(Activity activity) {
        return billingClient.isTotalAmountMoreThanZero(activity.getId()).isPayload();
    }

    private boolean hasBillableTimesheet(Activity activity) {
        var timesheetResponse = billingClient.getUnbilledTimesByActivityId(activity.getId());
        return isTotalBillableTimeMoreThanZero(timesheetResponse);
    }

    private List<Activity> getActivitiesForBillingStatus(LocalDate processDate) {
        return activityRepository.findByBillingAccountIdIsNotNullAndBillingStatusIsNullAndUpdatedAtBetweenAndStatusIn(
                        processDate.atStartOfDay(),
                        processDate.plusDays(1).atStartOfDay(),
                        ActivityStatus.AUTO_BILLING_STATUSES).stream()
                .filter(this::isBillableActivityType)
                .filter(this::isStatusUpdated)
                .toList();
    }

    private boolean isStatusUpdated(Activity activity) {
        var revisions = activityService.getRevisionsByField(activity.getId(), "status");
        return revisions.stream()
                .anyMatch(revision ->
                        revision.getUpdatedAt().toLocalDate().equals(activity.getUpdatedAt().toLocalDate()));
    }

    @Transactional
    @Override
    public void startManuelBillingProcess(Long activityId) {
        var activity = activityService.getActivityById(activityId);
        var lastPackageOptional = tsicPackageService.getActivePackage(activity.getTsicPackages());
        if (activity.getBillingStatus() != null) {
            throw new BillingCannotCreateException(ActivityResponseCode.BILLING_ALREADY_STARTED);
        }
        if (!isBillableActivityType(activity)) {
            throw new BillingCannotCreateException(ActivityResponseCode.BILLING_CANNOT_CREATE);
        }
        var hasBillableExpenseOrDisbursement = hasBillableExpenseOrDisbursement(activity);
        var hasBillableTimes = hasBillableTimesForManuel(activity, lastPackageOptional);
        var hasBillableRecord = hasBillableExpenseOrDisbursement || hasBillableTimes;

        if (!hasBillableRecord) {
            throw new BillingCannotCreateException(ActivityResponseCode.BILLING_CANNOT_CREATE);
        }

        var issueCreateDto = getIssueCreateDto(activity);
        var parameters = getTypeParametersWithDates(activity.getType().name());
        parameters.ifPresent(parameter -> issueCreateDto.setSize(parameter.getSize()));
        issueCreateDto.setTargetDate(
                DateUtils.plusWorkDay(LocalDate.now(), getHolidays(), beforeBillingDay).atStartOfDay());
        createIssue(activity.getId(), issueCreateDto);
        lastPackageOptional
                .ifPresent(lastPackage -> updateIncludedInCharge(activity, lastPackage));
    }

    private void updateIncludedInCharge(Activity activity, TSICPackage lastPackage) {
        var includedInChargeRequest = TimesheetIncludedInChargeRequest.builder()
                .activityId(activity.getId())
                .packageStartDate(lastPackage.getStartDate())
                .tsicIncludedCharge(lastPackage.getIncludedCharge())
                .build();
        billingClient.calculateIncludedInCharge(includedInChargeRequest);
    }

    private IssueCreateDto getIssueCreateDto(Activity activity) {
        var matterResponse = matterClient.getById(activity.getMatterId());
        return IssueCreateDto.builder()
                .assignee(matterResponse.getPayload().getInvoiceApproval())
                .type(BILLING_ISSUE_TYPE)
                .read(Boolean.FALSE)
                .build();
    }

    private boolean hasBillableExpenseOrDisbursement(Activity activity) {
        return billingClient.hasBillableDisbursementOrExpense(activity.getId()).isPayload();
    }

    @Nullable
    private static String getJoined(List<Long> longList) {
        return StringUtils.join(longList, ",");
    }

    private boolean hasBillableTimesForManuel(Activity activity, Optional<TSICPackage> lastPackageOptional) {
        return lastPackageOptional
                .map(tsicPackage -> hasBillableTimesheetByTsicPackageForManuel(activity, tsicPackage))
                .orElseGet(() -> hasBillableTimesheet(activity));
    }

    @Override
    public void approveBilling(Long issueId) {
        var activity = activityService.getActivityByIssueId(issueId);
        var currentStatus = activity.getBillingStatus();
        if (!BillingStatus.isApprovable(currentStatus)) {
            throw new BillingCannotBeApprovedException();
        }

        activity.setBillingStatus(BillingStatus.APPROVED);
        activityRepository.save(activity);

        var approveBillingRequest = ApproveBillingRequest.builder()
                .activityId(activity.getId())
                .build();

        try {
            billingClient.approveBilling(approveBillingRequest);

            if (!hasBillableExpenseOrDisbursement(activity)) {
                throw new NoExpenseRecordToBeInvoicedException();
            }

            sendBillingApprovedEvent(activity);
        } catch (Exception e) {
            activity.setBillingStatus(currentStatus);
            activityRepository.save(activity);
            throw e;
        }
    }

    private void sendBillingApprovedEvent(Activity activity) {
        var billingApprovedEvent = BillingApprovedEvent.builder()
                .activityId(activity.getId())
                .billingAccountId(activity.getBillingAccountId())
                .matterId(activity.getMatterId())
                .billingPeriodEnds(activity.getBillingPeriodEnds().toString())
                .agentReference(activity.getAgentReference())
                .build();
        producerService.send(billingApprovedTopic, ObjectUtils.toJson(billingApprovedEvent));
    }

    private List<LocalDate> calculateFavorableDates(LocalDate startDate, LocalDate endDate) {
        List<LocalDate> dates = new ArrayList<>();
        LocalDate currentDate = startDate;

        while (!currentDate.isEqual(endDate)) {
            dates.add(currentDate);
            currentDate = currentDate.plusDays(1);
        }

        return dates;
    }

    private LocalDate addWorkDay(LocalDate startDate, List<LocalDate> holidays, int beforeBillingDay) {
        return DateUtils.plusWorkDay(startDate, holidays, beforeBillingDay);
    }

    private boolean isTotalBillableTimeMoreThanZero(TimesheetTotalTimeResponse timesheetTotalTimeResponse) {
        var totalBillableTime = timesheetTotalTimeResponse.getPayload().getTotalBillableTime();
        return totalBillableTime != null && totalBillableTime.compareTo(BigDecimal.ZERO) > 0;
    }

    private boolean hasBillableTimesheetByTsicPackageForManuel(Activity activity, TSICPackage tsicPackage) {
        var totalBillableTime = getTotalBillableTimeByTsicPackage(activity, tsicPackage);
        if (totalBillableTime == null) {
            return false;
        }

        boolean isUnbilled = BooleanUtils.isNotTrue(tsicPackage.getInvoiced());
        boolean isBilled = BooleanUtils.isTrue(tsicPackage.getInvoiced());
        boolean isTotalBillableTimeGreaterThanZero = totalBillableTime.compareTo(BigDecimal.ZERO) > 0;
        boolean isTotalBillableTimeGreaterThanIncludedCharge = totalBillableTime
                .compareTo(tsicPackage.getIncludedCharge()) > 0;

        return (isUnbilled && isTotalBillableTimeGreaterThanZero)
                || (isBilled && isTotalBillableTimeGreaterThanIncludedCharge);
    }

    private boolean hasBillableTimesheetByTsicPackage(Activity activity, TSICPackage tsicPackage,
                                                      BigDecimal billableTime) {
        var totalBillableTime = getTotalBillableTimeByTsicPackage(activity, tsicPackage);
        var totalBillableTimeForFds = getTotalBillableTimeByTsicPackageForFds(activity, tsicPackage);
        if (totalBillableTime == null || totalBillableTimeForFds == null) {
            return false;
        }

        boolean isUnbilled = BooleanUtils.isNotTrue(tsicPackage.getInvoiced());
        boolean isBilled = BooleanUtils.isTrue(tsicPackage.getInvoiced());
        boolean isTotalBillableTimeGreaterThanBillableTime = totalBillableTime.compareTo(billableTime) > 0;
        boolean isTotalBillableTimeGreaterThanIncludedCharge = totalBillableTimeForFds
                .compareTo(tsicPackage.getIncludedCharge()) > 0;

        return (isUnbilled && isTotalBillableTimeGreaterThanBillableTime)
                || (isBilled && isTotalBillableTimeGreaterThanIncludedCharge);
    }

    private @Nullable BigDecimal getTotalBillableTimeByTsicPackage(Activity activity, TSICPackage tsicPackage) {
        var tsicStart = tsicPackage.getStartDate();
        var timesheetResponse = billingClient.getUnbilledTimesByActivityIdAndStartDate(activity.getId(), tsicStart);
        return timesheetResponse.getPayload().getTotalBillableTime();
    }

    private @Nullable BigDecimal getTotalBillableTimeByTsicPackageForFds(Activity activity, TSICPackage tsicPackage) {
        var tsicStart = tsicPackage.getStartDate();
        var timesheetResponse = billingClient.getUnbilledTimesByActivityIdAndStartDateForFds(activity.getId(), tsicStart);
        return timesheetResponse.getPayload().getTotalBillableTime();
    }

    private Optional<IssueTypeParameterResponse.TypeParameter> getTypeParametersWithDates(String activityType) {
        var key = activityType + BILLING_ISSUE_TYPE;
        var response = paramCommandClient.getIssueTypeWithDates(key, LocalDateTime.now());
        return response.getParameters();
    }

    @Override
    public List<ActivityDto> getActivitiesForBillingCart(String assignee) {
        var issueIds = activityService.getIssuesByBillingStatus(BillingStatus.WAITING_IN_BILLING_CART.name());
        if (issueIds.isEmpty()) {
            return Collections.emptyList();
        }
        var issueFilterRequest = IssueFilterRequest.builder()
                .ids(issueIds)
                .assignee(assignee)
                .statusNot("CLOSED")
                .build();
        var issueIdsForBillingCart = issueClient.getAll(issueFilterRequest)
                .getPayload().stream()
                .map(IssueListResponse.Payload::getId)
                .toList();
        return activityService.getAllByIssueIds(issueIdsForBillingCart);
    }


    @Transactional
    @Override
    public void setOrderCreatedStatus(Long activityId) {
        var activity = activityService.getActivityById(activityId);
        activity.setBillingStatus(BillingStatus.ORDER_CREATED);
        activityRepository.save(activity);
    }

    @Transactional
    @Override
    public void undoBillingApproval(Long issueId) {
        var activity = activityService.getActivityByIssueId(issueId);
        undoBillingApprovalActivity(activity);
        billingClient.undoBillingApproval(activity.getId(), activity.getBillingPeriodEnds());
        var assignmentRequest = AssignmentRequest.builder()
                .assignee(SecurityUtils.getCurrentUsername().orElseThrow())
                .assigneeRoles(SecurityUtils.getRoles().stream().toList())
                .build();
        issueClient.assign(issueId, assignmentRequest);
    }

    @Override
    public void processOrderIntegrationSuccess(String orderNumber) {
        var activities = getActivitiesByOrderNo(orderNumber);
        activities.stream()
                .filter(activity -> isEligibleForIntegrationSuccess(activity.getBillingStatus()))
                .forEach(activity -> {
                    activity.setBillingStatus(BillingStatus.ORDER_INTEGRATED);
                    activityRepository.save(activity);
                });
    }

    private boolean isEligibleForIntegrationSuccess(BillingStatus status) {
        return status == BillingStatus.ORDER_CREATED
                || status == BillingStatus.ERROR_IN_ORDER;
    }

    @Override
    public void processOrderIntegrationError(String orderNumber) {
        var activities = getActivitiesByOrderNo(orderNumber);
        activities.stream()
                .filter(activity -> activity.getBillingStatus().equals(BillingStatus.ORDER_CREATED))
                .forEach(activity -> {
                    activity.setBillingStatus(BillingStatus.ERROR_IN_ORDER);
                    activityRepository.save(activity);
                });
    }

    private List<Activity> getActivitiesByOrderNo(String orderNumber) {
        List<Long> ids = getActivityIdsByOrderNo(orderNumber);
        return activityRepository.findByIdIn(ids);
    }

    @Transactional
    @Override
    public void cancelBillingOrder(Long activityId) {
        activityRepository.findById(activityId).ifPresent(activity -> {
            updateBillingStatusForCancellation(activity);
            activityRepository.save(activity);
        });
    }

    private void updateBillingStatusForCancellation(Activity activity) {
        var newStatus = isBillingOrderCancellationUnsafe(activity)
                ? BillingStatus.BILLING_STARTED
                : BillingStatus.APPROVED;
        activity.setBillingStatus(newStatus);
    }

    @Override
    @Transactional
    public void processInvoiceUpdated(Set<Long> activityIds) {
        activityRepository.findByIdIn(activityIds.stream().toList()).stream()
                .filter(activity -> activity.getBillingStatus().equals(BillingStatus.ORDER_INTEGRATED))
                .forEach(activity -> {
                    var invoiceCompleted = billingClient.isInvoiceCompleted(activity.getId(), activity.getBillingPeriodEnds()).isPayload();
                    if (invoiceCompleted) {
                        activity.setBillingStatus(BillingStatus.INVOICE_IS_READY);
                        activityRepository.save(activity);

                        sendInvoiceIsReadyEvent(activity);
                    }
                });
    }

    @Override
    public void consumeBillingIssueClosedTopic(Long issueId) {
        activityRepository.findAllByIssues(issueId).forEach(activity -> {
            activity.setBillingStatus(null);
            activity.setBillingPeriodEnds(null);
            activity.setProcessStartDate(null);
            activityRepository.save(activity);
        });
    }

    @Override
    public List<Revision> getBillingRevisions(Long activityId) {
        return revisionService.getAll(ActivityRevisionProjection.class, activityId);
    }

    private void sendInvoiceIsReadyEvent(Activity activity) {
        var invoiceIsReadyEvent = InvoiceIsReadyEvent.builder()
                .issueIds(activity.getIssues())
                .billingAccountId(activity.getBillingAccountId())
                .matterId(activity.getMatterId())
                .activityAgentRef(activity.getAgentReference())
                .build();
        producerService.send(invoiceIsReadyTopic, ObjectUtils.toJson(invoiceIsReadyEvent));
    }

    private void createIssueWithDueDate(Activity activity,
                                        Optional<IssueTypeParameterResponse.TypeParameter> parameters,
                                        LocalDate dueDate) {
        var mattersResponse = matterClient.getById(activity.getMatterId());
        var issueCreateDto = IssueCreateDto.builder()
                .assignee(mattersResponse.getPayload().getInvoiceApproval())
                .type(BILLING_ISSUE_TYPE)
                .read(Boolean.FALSE)
                .dueDate(dueDate.atStartOfDay())
                .build();
        createBillingIssueWithTargetDate(activity.getId(), parameters, issueCreateDto);
    }

    private void createBillingIssueWithTargetDate(Long activityId,
                                                  Optional<IssueTypeParameterResponse.TypeParameter> parameters,
                                                  IssueCreateDto issueCreateDto) {
        parameters.ifPresent(parameter -> {
            issueCreateDto.setTargetDate(parameter.getTargetDate());
            issueCreateDto.setSize(parameter.getSize());
        });
        createIssue(activityId, issueCreateDto);
    }

    private void createIssue(Long activityId, IssueCreateDto issueCreateDto) {
        issueCreateDto.setLinkedActivity(true);
        var savedIssue = issueClient.save(issueCreateDto);

        var activity = activityService.getActivityById(activityId);
        activityService.getActivityById(activityId).setBillingStatus(BillingStatus.BILLING_STARTED);
        activity.setProcessStartDate(LocalDateTime.now());
        activity.setBillingPeriodEnds(LocalDate.now());
        activity.getIssues().add(savedIssue.getPayload().getId());
        activityRepository.save(activity);
    }

    private List<Long> getActivityIdsByOrderNo(String orderNumber) {
        return billingClient.getActivityIdsByOrderNo(orderNumber)
                .getPayload().stream()
                .map(ExpenseResponse.Payload::getActivityId)
                .toList();
    }

    private void undoBillingApprovalActivity(Activity activity) {
        var mattersResponse = matterClient.getById(activity.getMatterId());
        if (SecurityUtils.getCurrentUsername().orElseThrow().equals(mattersResponse.getPayload().getInvoiceApproval())) {
            activity.setBillingStatus(BillingStatus.BILLING_STARTED);
        } else {
            activity.setBillingStatus(BillingStatus.REVISION_REQUESTED);
        }
        activityRepository.save(activity);
    }

    private List<LocalDate> getHolidays() {
        return paramCommandClient.getHolidayList().getPayload();
    }

    private List<LocalDate> calculateFavorableDates(LocalDate processDate, List<LocalDate> holidays) {
        var nextDay = processDate.plusDays(1);
        var isNextDayHoliday = DateUtils.isHoliday(nextDay, holidays);
        var favorableDates = List.of(processDate);
        if (isNextDayHoliday) {
            var nextWorkDay = DateUtils.nextWorkDay(processDate, holidays);
            favorableDates = calculateFavorableDates(processDate, nextWorkDay);
        }
        return favorableDates;
    }

    private String getParamValue(String key) {
        return parameterClient.getByKey(key).getPayload().getValue();
    }

    private List<Activity> getActivitiesWithEmptyBillingDay(List<Activity> activities) {
        if (CollectionUtils.isEmpty(activities)) {
            return activities;
        }
        var billingAccountDtos = getBillingAccountDtos(activities);
        var billingAccountsWithEmptyBillingDay = getBillingAccountIdsWithEmptyBillingDay(billingAccountDtos);
        return activities.stream()
                .filter(activity -> billingAccountsWithEmptyBillingDay.contains(activity.getBillingAccountId()))
                .toList();
    }

    private @NotNull List<Long> getBillingAccountIdsWithEmptyBillingDay(List<BillingAccountDto> billingAccountDtos) {
        if (billingAccountDtos == null || billingAccountDtos.isEmpty()) {
            return Collections.emptyList();
        }
        return billingAccountDtos.stream()
                .filter(ActivityBillingProcessServiceImpl::isBillingDayEmpty)
                .map(BillingAccountDto::getId)
                .toList();
    }

    private static boolean isBillingDayEmpty(BillingAccountDto billingAccountDto) {
        return StringUtils.isEmpty(billingAccountDto.getBillingDay()) || "NA".equals(billingAccountDto.getBillingDay());
    }

    private List<BillingAccountDto> getBillingAccountDtos(List<Activity> activities) {
        var billingAccountIds = activities.stream()
                .map(Activity::getBillingAccountId)
                .collect(Collectors.toSet());

        return billingClient.getByIds(billingAccountIds).getPayload();
    }

    private BillableBillingAccount getBillableBillingAccount(Activity activity, List<LocalDate> favorableDates) {
        var favorableDatesString = favorableDates.stream()
                .map(String::valueOf)
                .collect(Collectors.joining(","));
        return billingClient.isBillable(activity.getBillingAccountId(), favorableDatesString).getPayload();
    }

    private void createIssue(Activity activity) {
        var issueCreateDto = getIssueCreateDto(activity);
        var parameters = getTypeParametersWithDates(activity.getType().name());
        createBillingIssueWithTargetDate(activity.getId(), parameters, issueCreateDto);
    }

    @Override
    public Boolean checkBillingCancellable(Long activityId) {
        var activity = activityService.getActivityById(activityId);
        if (billingClient.hasAnyBillable(activity.getId(), activity.getBillingPeriodEnds()).isPayload()) {
            throw new BillingIssueCannotBeCanceledException(activityId,
                    ActivityResponseCode.BILLING_ISSUE_CAN_NOT_BE_CANCELED);
        }
        return true;
    }

    @Override
    public boolean isBillingOrderCancellationSafe(String orderNo) {
        return getActivitiesByOrderNo(orderNo).stream()
                .noneMatch(this::isBillingOrderCancellationUnsafe);
    }

    @Override
    public void cancelBilling(Long activityId, Long issueId, boolean fillEmptyExpenseDates) {
        if (!fillEmptyExpenseDates) {
            sendCloseIssueEvent(issueId);
            return;
        }
        var activity = activityService.getActivityById(activityId);
        validateBillingCancellation(activity);

        updateExpenseDate(activity);
        sendCloseIssueEvent(issueId);
    }

    @Override
    public boolean hasBillableRecord(Long id) {
        var activity = activityService.getActivityById(id);
        return hasBillableExpenseOrDisbursements(activity.getId(), activity.getBillingPeriodEnds()) || hasBillableTimesheet(activity);
    }

    private void validateBillingCancellation(Activity activity) {
        var activePackage = tsicPackageService.getActivePackage(activity.getTsicPackages());
        if (activePackage.isPresent()
                && BooleanUtils.isFalse(activePackage.get().getFixedFee())
                && isTimesheetsTotalTimesExceedActivePackage(activity.getId())) {
            throw new BillingCannotBeCanceledException(ActivityResponseCode.BILLING_ISSUE_CAN_NOT_BE_CANCELED);
        }
    }

    private void sendCloseIssueEvent(Long issueId) {
        var eventMap = new HashMap<String, Long>();
        eventMap.put("issueId", issueId);
        producerService.send(closeIssueTopic, ObjectUtils.toJson(eventMap));
    }

    private void updateExpenseDate(Activity activity) {
        billingClient.cancelBilling(activity.getId(), activity.getBillingPeriodEnds());
    }

    private boolean isTimesheetsTotalTimesExceedActivePackage(Long activityId) {
        var tsicRemaining = billingClient.getUnbilledTimesByActivityId(activityId).getPayload().getTsicRemaining();
        return tsicRemaining != null && tsicRemaining.compareTo(BigDecimal.ZERO) < 0;
    }

    private boolean isBillingOrderCancellationUnsafe(Activity activity) {
        return BillingStatus.isNullifyExpensesStatus(activity.getBillingStatus()) && hasUnapprovedItems(activity);
    }

    private boolean hasUnapprovedItems(Activity activity) {
        return billingClient.hasAnyUnapproved(activity.getId(), activity.getBillingPeriodEnds()).isPayload();
    }

    private boolean hasBillableExpenseOrDisbursements(Long activityId, LocalDate billingPeriodEnds) {
        return billingClient.hasBillableExpenseOrDisbursementByBillingPeriodEnds(activityId, billingPeriodEnds).isPayload();
    }

    private boolean isBillableActivityType(Activity activity) {
        return !ActivityType.NON_BILLABLE_ACTIVITY_TYPES.contains(activity.getType());
    }
}
