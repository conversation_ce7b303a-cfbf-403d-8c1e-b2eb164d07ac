package com.ipms.activity.service.impl;

import com.ipms.activity.client.BillingClient;
import com.ipms.activity.dto.TSICPackageUpdatedEvent;
import com.ipms.activity.dto.TimesheetDto;
import com.ipms.activity.model.TSICPackage;
import com.ipms.activity.repository.TSICPackageRepository;
import com.ipms.activity.service.TSICOvertimeCalculationService;
import com.ipms.core.common.utils.DateUtils;
import com.ipms.core.common.utils.NumberUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.context.event.EventListener;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Service
@RequiredArgsConstructor
public class TSICOvertimeCalculationServiceImpl implements TSICOvertimeCalculationService {
    private final BillingClient billingClient;
    private final TSICPackageRepository repository;


    @EventListener
    public void handleTSICPackageUpdatedEvent(TSICPackageUpdatedEvent event) {
        updateOvertimeForAllActivePackages(event.getActivityId());
    }

    @Override
    public void updateOvertimeForAllActivePackages(Long activityId) {
        var timesheetResponse = billingClient.getAll(activityId);
        var tsicPackages = repository.findAllByActivityId(activityId);
        calculateOverTime(tsicPackages, timesheetResponse.getTimesheets());
    }

    private void calculateOverTime(List<TSICPackage> tsicPackages, List<TimesheetDto> timesheetDtos) {
        tsicPackages.forEach(tsicPackage -> calculateOverTime(timesheetDtos, tsicPackage));
    }

    private void calculateOverTime(List<TimesheetDto> timesheetDtos, TSICPackage tsicPackage) {
        if (BooleanUtils.isNotTrue(tsicPackage.getFixedFee())) {
            tsicPackage.setOverTime(null);
        } else {
            var eligibleTimesheets = findEligibleTimesheetsForCalculation(timesheetDtos, tsicPackage);
            var totalBillableTime = sumBillableHours(eligibleTimesheets);
            tsicPackage.setOverTime(isExceedingPackageLimit(tsicPackage.getIncludedCharge(), totalBillableTime));
        }
        repository.save(tsicPackage);
    }

    @NotNull
    private List<TimesheetDto> findEligibleTimesheetsForCalculation(List<TimesheetDto> timesheetDtos, TSICPackage tsicPackage) {
        return getTimesheetsInPackage(timesheetDtos, tsicPackage).stream()
                .filter(timesheetDto -> timesheetDto.getExpenseDate() == null
                        || BooleanUtils.isTrue(timesheetDto.getIncludedInCharge()))
                .toList();
    }

    @NotNull
    private BigDecimal sumBillableHours(List<TimesheetDto> timesheetDtos) {
        return timesheetDtos.stream()
                .map(TimesheetDto::getBillableTime)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @NotNull
    private List<TimesheetDto> getTimesheetsInPackage(List<TimesheetDto> timesheetDtos, TSICPackage tsicPackage) {
        return timesheetDtos.stream()
                .filter(timesheetDto -> isInPackage(tsicPackage, timesheetDto.getDate()))
                .toList();
    }

    private boolean isInPackage(TSICPackage tsicPackage, LocalDate timesheetDate) {
        return DateUtils.isDateInRange(timesheetDate, tsicPackage.getStartDate(), tsicPackage.getEndDate());
    }

    private boolean isExceedingPackageLimit(BigDecimal includedCharge, BigDecimal totalBillableTime) {
        return totalBillableTime.compareTo(NumberUtils.hoursToMinutes(includedCharge)) > 0;
    }

}
