package com.ipms.activity.service.impl;

import com.ipms.activity.client.MatterClient;
import com.ipms.activity.client.ParamCommandClient;
import com.ipms.activity.dto.MatterDto;
import com.ipms.activity.dto.PeriodicOperationItemDto;
import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.enums.PeriodicOperationPeriodType;
import com.ipms.activity.model.Activity;
import com.ipms.activity.model.Renewal;
import com.ipms.activity.model.RenewalHistory;
import com.ipms.activity.service.RenewalHistoryService;
import com.ipms.activity.service.RenewalService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.Comparator;
import java.util.List;

@RequiredArgsConstructor
@Service
public class RenewalServiceImpl implements RenewalService {

    private final MatterClient matterClient;
    private final ParamCommandClient paramCommandClient;
    private final RenewalHistoryService renewalHistoryService;

    @Override
    public LocalDate getRenewalDate(Activity activity) {
        var matter = getMatter(activity);
        var operations = getPeriodicOperations(matter);
        if (operations.isEmpty()) return null;

        var selectedValidPeriodicOperation = selectValidPeriodicOperation(operations, matter);
        return calculateRenewalDate(selectedValidPeriodicOperation, matter);
    }

    @Override
    public Activity createRenewalActivityFromRegistrationActivity(Activity registrationActivity) {
        return Activity.builder()
                .type(ActivityType.RENEWAL)
                .status(ActivityStatus.PENDING)
                .matterId(registrationActivity.getMatterId())
                .firmId(registrationActivity.getFirmId())
                .billingAccountId(registrationActivity.getBillingAccountId())
                .agentReference(registrationActivity.getAgentReference())
                .localAgent(registrationActivity.getLocalAgent())
                .renewal(buildRenewal(registrationActivity))
                .build();
    }

    @Override
    public void createRenewalHistoryFromActivity(Activity renewalActivity) {
        var matter = getMatter(renewalActivity);
        var operations = getPeriodicOperations(matter);
        var selectedValidPeriodicOperation = selectValidPeriodicOperation(operations, matter);

        var renewalHistory = RenewalHistory.builder()
                .renewal(renewalActivity.getRenewal())
                .period(renewalHistoryService.calculateNextPeriod(renewalActivity))
                .dueDate(renewalActivity.getRenewal().getRenewalDate())
                .graceDate(calculateGraceDate(renewalActivity, selectedValidPeriodicOperation))
                .build();

        renewalHistoryService.save(renewalHistory);
    }

    private MatterDto getMatter(Activity activity) {
        return matterClient.getById(activity.getMatterId()).getPayload();
    }

    private List<PeriodicOperationItemDto> getPeriodicOperations(MatterDto matter) {
        var ipOfficeId = matter.getMark().getIpOfficeId();
        return (ipOfficeId == null)
                ? paramCommandClient.getPeriodicOperationByCountry(matter.getMark().getCountryId()).getPeriodicOperations()
                : paramCommandClient.getPeriodicOperationByIpOffice(ipOfficeId).getPeriodicOperations();
    }

    private PeriodicOperationItemDto selectValidPeriodicOperation(List<PeriodicOperationItemDto> operations, MatterDto matter) {
        if (operations.isEmpty()) return null;
        if (operations.size() == 1) return operations.get(0);

        return operations.stream()
                .filter(op -> op.getConditionOfValidityDate() != null && isAfterConditionDate(op, matter))
                .max(Comparator.comparing(PeriodicOperationItemDto::getConditionOfValidityDate))
                .orElseGet(() -> operations.stream()
                        .filter(op -> op.getConditionOfValidityDate() == null)
                        .findFirst()
                        .orElse(null));
    }

    private boolean isAfterConditionDate(PeriodicOperationItemDto operation, MatterDto matter) {
        var baseDate = getBaseDate(operation, matter);
        return baseDate != null && baseDate.isAfter(operation.getConditionOfValidityDate());
    }

    private LocalDate calculateRenewalDate(PeriodicOperationItemDto operation, MatterDto matter) {
        LocalDate baseDate = getBaseDate(operation, matter);
        if (operation == null || baseDate == null) return null;

        return addPeriod(baseDate, operation.getPeriod(), operation.getValue());
    }

    private LocalDate getBaseDate(PeriodicOperationItemDto operation, MatterDto matter) {
        if (operation == null || operation.getBaseDate() == null) return null;

        return switch (operation.getBaseDate()) {
            case APPLICATION_DATE -> matter.getMark().getRegistration().getApplicationDate();
        };
    }


    private LocalDate calculateGraceDate(Activity renewalActivity, PeriodicOperationItemDto operation) {
        if (operation == null || renewalActivity.getRenewal().getRenewalDate() == null) return null;

        return addPeriod(
                renewalActivity.getRenewal().getRenewalDate(),
                operation.getAdditionalTimePeriod(),
                operation.getAdditionalTimePeriodValue()
        );
    }

    private LocalDate addPeriod(LocalDate date, PeriodicOperationPeriodType period, int value) {
        return switch (period) {
            case YEAR -> date.plusYears(value);
            case MONTH -> date.plusMonths(value);
        };
    }

    private Renewal buildRenewal(Activity registrationActivity) {
        return Renewal.builder()
                .bulletinNumber(registrationActivity.getRegistration().getBulletinNumber())
                .bulletinPublicationDate(registrationActivity.getRegistration().getBulletinPublicationDate())
                .decisionDate(registrationActivity.getRegistration().getDecisionDate())
                .notificationDate(registrationActivity.getRegistration().getNotificationDate())
                .renewalDate(getRenewalDate(registrationActivity))
                .build();
    }
}