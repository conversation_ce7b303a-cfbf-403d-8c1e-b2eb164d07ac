package com.ipms.activity.service.impl;

import com.ipms.activity.client.BillingClient;
import com.ipms.activity.client.DerisClient;
import com.ipms.activity.client.DocumentClient;
import com.ipms.activity.client.MatterClient;
import com.ipms.activity.dto.*;
import com.ipms.activity.dto.turkpatent.MarkInformationResponse;
import com.ipms.activity.enums.*;
import com.ipms.activity.exception.ActivityCannotBeUpdatedException;
import com.ipms.activity.exception.ActivityNotFoundException;
import com.ipms.activity.mapper.ActivityMapper;
import com.ipms.activity.model.*;
import com.ipms.activity.repository.ActivityRepository;
import com.ipms.activity.service.*;
import com.ipms.activity.specification.ActivitySpecification;
import com.ipms.activity.validator.ActivityValidator;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.enums.Domain;
import com.ipms.core.common.enums.TransferType;
import com.ipms.core.common.model.TransferEvent;
import com.ipms.core.common.utils.ObjectUtils;
import com.ipms.core.entity.Revision;
import com.ipms.core.service.RevisionService;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.ListUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Sort;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.lang.reflect.Field;
import java.math.BigDecimal;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.Stream;

@RequiredArgsConstructor
@Service
public class ActivityServiceImpl implements ActivityService {

    private final ActivityRepository repository;
    private final ActivityMapper mapper;
    private final ProducerService producerService;
    private final MatterClient matterClient;
    private final BillingClient billingClient;
    private final ActivityValidator validator;
    private final InvestigationFirmService investigationFirmService;
    private final PreliminaryInjunctionService piService;
    private final RevisionService<Activity> revisionService;
    private final RevisionService<CDLetter> cdLetterRevisionService;
    private final RevisionService<UseInvestigation> useInvestigationRevisionService;
    private final CounterRightOwnerService counterRightOwnerService;
    private final SearchActivityServiceImpl searchActivityService;
    private final ComponentCourtService componentCourtService;
    private final CustomsSuspensionService customsSuspensionService;
    private final DocumentClient documentClient;
    private final DerisClient derisClient;
    private final ActivityDueDateService activityDueDateService;
    private final RenewalService renewalService;
    private final BulletinRelatedActivityService bulletinRelatedActivityService;
    private final CounterRightOwnerCardService counterRightOwnerCardService;

    @Value("${kafka.issue-linked-topic}")
    private String issueLinkedTopic;

    @Value("${kafka.document-updated-topic}")
    private String documentUpdatedTopic;

    @Value("${kafka.firm-associated-topic}")
    private String firmAssociatedTopic;

    @Value("${kafka.matter-update-topic}")
    private String matterUpdateTopic;

    @Override
    public ActivityDto save(ActivityDto activityDto) {
        validator.validate(activityDto);
        var activity = mapper.toActivity(activityDto);
        Optional.ofNullable(activity.getCdLetter()).ifPresent(cdLetter -> cdLetter.setActivity(activity));
        Optional.ofNullable(activity.getCourtAction()).ifPresent(courtAction -> courtAction.setActivity(activity));
        Optional.ofNullable(activity.getDeterminationAction())
                .ifPresent(determinationAction -> determinationAction.setActivity(activity));
        Optional.ofNullable(activity.getPreliminaryInjunction()).ifPresent(preliminaryInjunction -> {
            piService.save(preliminaryInjunction, activityDto.getPreliminaryInjunction());
            preliminaryInjunction.setActivity(activity);
        });
        Optional.ofNullable(activity.getCriminalAction()).ifPresent(criminalAction -> {
            criminalAction.setActivity(activity);
            Optional.ofNullable(criminalAction.getProducts())
                    .ifPresent(products -> products.forEach(p -> p.setCriminalAction(criminalAction)));
        });

        Optional.ofNullable(activity.getUseInvestigation()).ifPresent(useInvestigation -> {
            var investigationFirms = investigationFirmService.saveList(
                    activityDto.getUseInvestigation().getInvestigationFirms());
            useInvestigation.setInvestigationFirms(investigationFirms);
            useInvestigation.setActivity(activity);
        });

        Optional.ofNullable(activity.getCustomsSuspension())
                .ifPresent(customsSuspension ->
                        activity.setCustomsSuspension(customsSuspensionService
                                .setCustomSuspensionToSaveActivity(activityDto, activity)));
        Optional.ofNullable(activity.getCustomsRecordal())
                .ifPresent(customsRecordal -> customsRecordal.setActivity(activity));
        Optional.ofNullable(activity.getNotaryDetermination())
                .ifPresent(notaryDetermination -> notaryDetermination.setActivity(activity));
        setOppositionOut(activity);
        setRegistration(activity);
        setOppositionIn(activity);
        setObjection(activity);
        setRenewal(activity);

        activityDueDateService.calculateAndSetStatusDueDate(activity);
        createTsicPackage(activityDto, activity);

        var saved = repository.save(activity);

        if (isRegistrationCompleted(saved)) {
            createRenewalActivityFrom(saved);
        }

        sendTransferEvent(saved.getId(), TransferType.INSERT);
        Optional.ofNullable(activity.getFirmId())
                .ifPresent(this::sendFirmAssociatedEvent);
        return toDto(saved);
    }

    private void setOppositionOut(Activity activity) {
        Optional.ofNullable(activity.getOppositionOut())
                .ifPresent(oppositionOut -> oppositionOut.setActivity(activity));
    }

    private void setRegistration(Activity activity){
        Optional.ofNullable(activity.getRegistration())
                .ifPresent(registration -> registration.setActivity(activity));
    }

    private void setOppositionIn(Activity activity) {
        Optional.ofNullable(activity.getOppositionIn())
                .ifPresent(oppositionIn -> oppositionIn.setActivity(activity));
    }

    private void setObjection(Activity activity) {
        Optional.ofNullable(activity.getObjection())
                .ifPresent(objection -> objection.setActivity(activity));
    }

    private void setRenewal(Activity activity) {
        Optional.ofNullable(activity.getRenewal())
                .ifPresent(renewal -> {
                    renewal.setActivity(activity);
                    var renewalDate = renewalService.getRenewalDate(activity);
                    renewal.setRenewalDate(renewalDate);
                });
    }

    private void createTsicPackage(ActivityDto activityDto, Activity activity) {
        if (activityDto.getIncludedCharge() != null && activityDto.getIncludedCharge().compareTo(BigDecimal.ZERO) > 0) {
            var tsicPackage = TSICPackage.builder()
                    .includedCharge(activityDto.getIncludedCharge())
                    .startDate(activityDto.getInstructionDate())
                    .activity(activity)
                    .build();
            activity.setTsicPackages(List.of(tsicPackage));
        }
    }

    private boolean isRegistrationCompleted(Activity activity) {
        return ActivityType.REGISTRATION.equals(activity.getType()) &&
                ActivityStatus.COMPLETED.equals(activity.getStatus());
    }

    @Transactional
    @Override
    public ActivityDto update(ActivityDto activityDto, Long activityId) {
        var existingActivity = getActivityById(activityId).toBuilder().build();

        var incomingActivity = mapper.toActivity(activityDto);
        activityDueDateService.updateStatusDueDate(existingActivity, incomingActivity);
        if (isRegistrationCompletedTransition(existingActivity, incomingActivity)) {
            createRenewalActivityFrom(incomingActivity);
        }

        validateBillingStatus(activityDto, existingActivity);

        var oldIssues = List.copyOf(existingActivity.getIssues());
        var updated = mapper.toActivityFromDto(activityDto, existingActivity);

        updateInvestigationFirms(activityDto, updated);
        updatePreliminaryInjunction(activityDto, updated);
        updateCriminalActionProducts(existingActivity);
        updateCustomsSuspension(activityDto, existingActivity, updated);

        var saved = repository.save(updated);
        sendUpdateEvents(saved, existingActivity, oldIssues);

        return toDto(saved);
    }

    private boolean isRegistrationCompletedTransition(Activity previous, Activity current) {
        return ActivityType.REGISTRATION.equals(current.getType()) &&
                ActivityStatus.COMPLETED.equals(current.getStatus()) &&
                !Objects.equals(previous.getStatus(), current.getStatus());
    }

    private void createRenewalActivityFrom(Activity registrationActivity) {
        var matterId = registrationActivity.getMatterId();
        var existingRenewalActivityOpt = repository.findTopByMatterIdAndType(matterId, ActivityType.RENEWAL);
        Activity renewalActivity;

        if (existingRenewalActivityOpt.isPresent()) {
            renewalActivity = existingRenewalActivityOpt.get();
        } else {
            renewalActivity = renewalService.createRenewalActivityFromRegistrationActivity(registrationActivity);
            setRenewal(renewalActivity);
            renewalActivity = repository.save(renewalActivity);
        }
        renewalService.createRenewalHistoryFromActivity(renewalActivity);
    }

    private void updateInvestigationFirms(ActivityDto activityDto, Activity updated) {
        Optional.ofNullable(activityDto.getUseInvestigation()).ifPresent(useInvestigation -> {
            var investigationFirms = investigationFirmService.saveList(useInvestigation.getInvestigationFirms());
            updated.getUseInvestigation().setInvestigationFirms(investigationFirms);
        });
    }

    private void updatePreliminaryInjunction(ActivityDto activityDto, Activity updated) {
        Optional.ofNullable(activityDto.getPreliminaryInjunction())
                .ifPresent(preliminaryInjunctionDto ->
                        piService.update(preliminaryInjunctionDto, updated.getPreliminaryInjunction()));
    }

    private void updateCriminalActionProducts(Activity existingActivity) {
        Optional.ofNullable(existingActivity.getCriminalAction()).ifPresent(criminalAction ->
                Optional.ofNullable(criminalAction.getProducts()).ifPresent(products ->
                        products.forEach(p -> p.setCriminalAction(criminalAction)))
        );
    }

    private void updateCustomsSuspension(ActivityDto activityDto, Activity existingActivity, Activity updated) {
        Optional.ofNullable(existingActivity.getCustomsSuspension()).ifPresent(customsSuspension ->
                updated.setCustomsSuspension(customsSuspensionService
                        .setCustomSuspensionToUpdateActivity(activityDto, existingActivity)));
    }

    private void sendUpdateEvents(Activity saved, Activity existingActivity, List<Long> oldIssues) {
        sendTransferEvent(saved.getId(), TransferType.UPDATE);
        sendLinkIssueEvent(oldIssues, saved.getIssues(), saved.getType());
        Optional.ofNullable(existingActivity.getFirmId())
                .ifPresent(this::sendFirmAssociatedEvent);
    }

    private void validateBillingStatus(ActivityDto activityDto, Activity existingActivity) {
        var billingStatus = EnumSet.of(BillingStatus.APPROVED, BillingStatus.ORDER_CREATED,
                BillingStatus.ORDER_INTEGRATED);
        if (existingActivity.getBillingStatus() != null && billingStatus.contains(existingActivity.getBillingStatus())
                && StringUtils.isBlank(activityDto.getBillingStatus())) {
            throw new ActivityCannotBeUpdatedException();
        }

        if (BillingStatus.isBillingPeriodEndsNotUpdatable(existingActivity.getBillingStatus())
                && !Objects.equals(existingActivity.getBillingPeriodEnds(), activityDto.getBillingPeriodEnds())) {
            throw new ActivityCannotBeUpdatedException();
        }
    }


    private ActivityDto toDto(Activity activity) {
        var activityDto = mapper.toActivityDto(activity);
        Optional.ofNullable(activityDto).ifPresent(dto ->
                piService.setFileSignedUrls(dto.getPreliminaryInjunction()));
        return activityDto;
    }

    @Override
    public ActivityDto getById(Long id) {
        return toDto(getActivityById(id));
    }

    @Override
    public List<ActivityDto> getByIdIn(List<Long> ids) {
        return repository.findByIdIn(ids)
                .stream()
                .map(this::toDto)
                .toList();
    }

    @Override
    public List<ActivityDto> getByFirmId(Long firmId) {
        return repository.findByFirmId(firmId)
                .stream()
                .map(this::toDto)
                .toList();
    }

    @Override
    public ActivityDto changeIssue(Long issueId, Long newIssueId) {
        var activity = repository.findByIssues(issueId)
                .orElseThrow(ActivityNotFoundException::new);
        activity.getIssues().add(newIssueId);
        repository.save(activity);
        return toDto(activity);
    }

    @Override
    public ActivityDto addIssue(Long activityId, Long issueId) {
        var activity = getActivityById(activityId);
        activity.getIssues().add(issueId);
        repository.save(activity);
        return toDto(activity);
    }

    @Override
    public ActivityDto removeIssue(Long id, Long issueId) {
        var activity = getActivityById(id);
        activity.getIssues().remove(issueId);
        var saved = repository.save(activity);
        sendIssueEvent(issueId, activity.getType().name());
        return toDto(saved);
    }

    @Override
    public List<Long> getIssuesByFirmId(Long firmId) {
        var agentIssues = repository.findByFirmId(firmId)
                .stream()
                .map(Activity::getIssues)
                .flatMap(Collection::stream)
                .toList();
        var matters = matterClient.getByFirmId(firmId).getPayload()
                .stream()
                .map(MatterDto::getId)
                .toList();
        var rightOwnerIssues = repository.findByMatterIdIn(matters)
                .stream()
                .map(Activity::getIssues)
                .flatMap(Collection::stream)
                .toList();
        return Stream.of(agentIssues, rightOwnerIssues)
                .flatMap(Collection::stream)
                .distinct()
                .toList();
    }

    @Override
    public List<Long> getIssuesByFirmIds(List<Long> firmIds) {
        return repository.findByFirmIdIn(firmIds)
                .stream()
                .map(IssueIdActivityIdMatterId::issues)
                .toList();
    }

    @Override
    public List<Long> getIssuesByBillingStatus(String billingStatus) {
        return repository.findByBillingStatus(BillingStatus.valueOf(billingStatus)).stream()
                .flatMap(activity -> activity.getIssues().stream())
                .distinct()
                .toList();
    }

    @Override
    public List<Long> getIssuesByBillingStatuses(List<String> billingStatuses) {
        var billingStatusEnums = billingStatuses.stream()
                .map(BillingStatus::valueOf)
                .toList();

        return repository.findByBillingStatusIn(billingStatusEnums).stream()
                .map(MatterIdAndIssues::issues)
                .distinct()
                .toList();
    }

    @Override
    public ActivityDto addDocument(Long id, Long documentId, boolean created) {
        var activity = getActivityById(id);
        activity.getDocuments().add(documentId);
        var saved = repository.save(activity);
        if (!created) {
            sendDocumentEvent(documentId);
        }
        return toDto(saved);
    }

    @Override
    public ActivityDto removeDocument(Long id, Long documentId) {
        var activity = getActivityById(id);
        activity.getDocuments().remove(documentId);
        var saved = repository.save(activity);
        sendDocumentEvent(documentId);
        return toDto(saved);
    }

    @Override
    public List<ActivityDto> getByDocumentId(Long documentId) {
        return repository.findByDocumentsOrderByInstructionDateAsc(documentId)
                .stream()
                .map(this::toDto)
                .toList();
    }

    @Override
    public List<ActivityDto> getByDocumentIdsIn(List<Long> documentIds) {
        return repository.findByDocumentsIn(documentIds)
                .stream()
                .map(this::toDto)
                .toList();
    }

    @Override
    public ActivityDto removeEvidence(Long activityId, Long evidenceId) {
        var activity = getActivityById(activityId);
        activity.getEvidences().remove(evidenceId);
        var saved = repository.save(activity);
        return toDto(saved);
    }

    @Override
    public List<ActivityDto> getByEvidenceId(Long evidenceId) {
        return repository.findByEvidencesOrderByInstructionDateDesc(evidenceId)
                .stream()
                .map(this::toDto)
                .toList();
    }

    @Override
    public ActivityDto addEvidence(Long activityId, Long evidenceId) {
        var activity = getActivityById(activityId);
        activity.getEvidences().add(evidenceId);
        var saved = repository.save(activity);
        return toDto(saved);
    }

    @Override
    public ActivityPageDto getAll(ActivityFilterRequest filterRequest, int page, int size) {

        if (filterRequest.getMatterType() != null) {
            MatterFilterRequest matterFilter = new MatterFilterRequest();
            matterFilter.setType(filterRequest.getMatterType().name());

            List<MatterDto> matters = matterClient.getMatterList(matterFilter).getPayload();
            List<Long> matterIds = matters.stream().map(MatterDto::getId).toList();

            filterRequest.setMatterIds(matterIds);
        }

        var activityPage = repository.findAll(
                mapSpecification(filterRequest),
                PageRequest.of(page, size)
        );

        return ActivityPageDto.builder()
                .activities(activityPage.getContent().stream().map(this::toDto).toList())
                .totalElements(activityPage.getTotalElements())
                .totalPages(activityPage.getTotalPages())
                .build();
    }

    public ActivityDocumentSummaryPageDto getActivityPageForDocuments(ActivityFilterRequest filterRequest, DocumentFilterRequest documentFilterRequest, int page, int size) {
        var matterFilterRequest = new MatterFilterRequest();
        var mFilterAltered = false;

        if (filterRequest.getMatterIds() != null && !filterRequest.getMatterIds().isEmpty()) {
            matterFilterRequest.setIds(filterRequest.getMatterIds());
            mFilterAltered = true;
        }
        if (filterRequest.getCoreBusinesses() != null && !filterRequest.getCoreBusinesses().isEmpty()) {
            matterFilterRequest.setCoreBusinesses(filterRequest.getCoreBusinesses());
            mFilterAltered = true;
        }
        if(filterRequest.getSubjects() != null && !filterRequest.getSubjects().isEmpty()) {
            matterFilterRequest.setSubjects(filterRequest.getSubjects());
            mFilterAltered = true;
        }
        if (mFilterAltered) {
            filterRequest.setMatterIds(matterClient.getMatterIdsList(matterFilterRequest).getPayload());
        }

        var documentSummaryDtos = documentClient.getAllDS(documentFilterRequest).getPayload();
        var documentIds = documentSummaryDtos.stream().map(DocumentSummaryDto::getId).toList();
        filterRequest.setDocumentIds(documentIds);

        var activityPage = repository.findAll(mapSpecification(filterRequest), PageRequest.of(page, size));
        var activities = activityPage.getContent()
                .stream().map(mapper::toActivityDocumentSummaryDto)
                .toList();

        for(ActivityDocumentSummaryDto item: activities) {
            for(DocumentSummaryDto document: documentSummaryDtos) {
                if(item.getDocuments().contains(document.getId())) {
                    item.setDocumentId(document.getId());
                    item.setDocumentDate(document.getDate());
                    item.setDocumentResult(document.getResult() != null ? document.getResult().toString() : "");
                    item.setExpiryDate(document.getExpiryDate());
                    break;
                }
            }
        }

        return ActivityDocumentSummaryPageDto.builder()
                .activities(activities)
                .totalElements(activityPage.getTotalElements())
                .totalPages(activityPage.getTotalPages())
                .build();
    }

    @Override
    public ActivityDocumentSummaryPageDto getAllDSExpert(ActivityFilterRequest filterRequest,
                                                         Long expertId, int page, int size) {
        var documentFilterRequest = new DocumentFilterRequest();
        documentFilterRequest.setExpertId(expertId);
        documentFilterRequest.setResults(filterRequest.getResults());
        filterRequest.setResults(null);

        return getActivityPageForDocuments(filterRequest, documentFilterRequest, page, size);

    }

    @Override
    public ActivityDocumentSummaryPageDto getAllDSLocalAttorney(ActivityFilterRequest filterRequest,
                                                                Long localAttorneyId, int page, int size) {
        var documentFilterRequest = new DocumentFilterRequest();
        documentFilterRequest.setLocalAttorneyId(localAttorneyId);
        documentFilterRequest.setResults(filterRequest.getResults());
        filterRequest.setResults(null);

        return getActivityPageForDocuments(filterRequest, documentFilterRequest, page, size);

    }

    private static ActivitySpecification mapSpecification(ActivityFilterRequest filterRequest) {
        return ActivitySpecification.builder()
                .ids(filterRequest.getIds())
                .types(filterRequest.getTypes())
                .statuses(filterRequest.getStatuses())
                .matterIds(filterRequest.getMatterIds())
                .firmIds(filterRequest.getFirmIds())
                .documentIds(filterRequest.getDocumentIds())
                .agentReference(filterRequest.getAgentReference())
                .instructionDates(filterRequest.getInstructionDates())
                .courtActionStage(filterRequest.getCourtActionStage())
                .decisionNumber(filterRequest.getDecisionNumber())
                .completionDates(filterRequest.getCompletionDates())
                .postalTrackingNumber(filterRequest.getPostalTrackingNumber())
                .cdLetterDates(filterRequest.getCdLetterDates())
                .notificationDates(filterRequest.getNotificationDates())
                .officialDocumentNumber(filterRequest.getOfficialDocumentNumber())
                .courtActions(filterRequest.getCourtActions())
                .complaintDates(filterRequest.getComplaintDates())
                .prosecutionNumber(filterRequest.getProsecutionNumber())
                .quotationIds(filterRequest.getQuotationIds())
                .billingAccountIds(filterRequest.getBillingAccountIds())
                .overTimeChoice(filterRequest.getOverTimeChoice())
                .matterId(filterRequest.getMatterId())
                .statusDueDateStart(filterRequest.getStatusDueDateStart())
                .statusDueDateEnd(filterRequest.getStatusDueDateEnd())
                .sortField(filterRequest.getSortField())
                .sortDirection(filterRequest.getSortDirection())
                .build();
    }


    @Override
    public Set<Long> getMatterIds(ActivityFilterRequest filterRequest, int page, int size) {
        ActivityPageDto pageDto = getAll(filterRequest, page, size);
        List<ActivityDto> activities = pageDto.getActivities();

        return activities.stream()
                .map(ActivityDto::getMatterId)
                .collect(Collectors.toSet());
    }

    @Override
    public List<ActivityDto> getAll(ActivityFilterRequest filterRequest) {
        var activities = repository.findAll(mapSpecification(filterRequest));
        return activities.stream().map(this::toDto).toList();
    }

    public AdvancedSearchDto createAdvancedSearchDto(SearchActivity searchActivity) {
        var advancedSearchDto = new AdvancedSearchDto();
        advancedSearchDto.setActivityId(searchActivity.getId());
        advancedSearchDto.setMatterId(searchActivity.getMatterId());
        advancedSearchDto.setActivityType(searchActivity.getType().name());
        advancedSearchDto.setActivityStatus(searchActivity.getStatus().name());
        advancedSearchDto.setInstructionDate(searchActivity.getInstructionDate());
        advancedSearchDto.setFirmId(searchActivity.getFirmId());
        return advancedSearchDto;
    }

    @Override
    public AdvancedSearchPageDto getAllAdvanced(AdvancedFilterRequest advancedFilterRequest, int page, int size) throws IllegalAccessException {
        Set<Long> filteredActivityIds = new HashSet<>();
        var filteredBefore = false;

        List<Long> counterRightOwnerIds = advancedFilterRequest.getCounterRightOwnerIds();
        List<Long> activityIdsFromCounterRightOwner = new ArrayList<>();
        if (Objects.nonNull(counterRightOwnerIds) && !counterRightOwnerIds.isEmpty()) {
            counterRightOwnerIds.forEach(counterRightOwnerId ->
                    activityIdsFromCounterRightOwner.addAll(
                            counterRightOwnerService.getActivityIds(counterRightOwnerId))
            );

            filteredActivityIds.addAll(activityIdsFromCounterRightOwner);
            filteredBefore = true;
        }

        var componentCourtFilterRequest = setComponentCourtFilterRequest(advancedFilterRequest);
        if (!checkNull(componentCourtFilterRequest)) {
            var componentCourtDtos = componentCourtService.getAllFiltered(componentCourtFilterRequest);
            var idsFromComponentCourt = componentCourtDtos.stream().map(ComponentCourtDto::getActivityId).toList();

            if (filteredActivityIds.isEmpty()) {
                filteredActivityIds.addAll(idsFromComponentCourt);
            }
            filteredActivityIds.retainAll(idsFromComponentCourt);
            filteredBefore = true;
        }

        MatterFilterRequest matterFilterRequest = setToMatterFilterRequest(advancedFilterRequest);
        if (!checkNull(matterFilterRequest)) {
            List<Long> matterIdsFromMatter = matterClient.getMatterIdsList(matterFilterRequest).getPayload();
            var idsFromMatter = repository.findAllByMatterIdIn(matterIdsFromMatter).stream()
                    .map(MatterIdAndActivityStatus::id)
                    .toList();
            if (filteredActivityIds.isEmpty()) {
                filteredActivityIds.addAll(idsFromMatter);
            }
            filteredActivityIds.retainAll(idsFromMatter);
            filteredBefore = true;
        }

        SearchActivityFilterRequest searchActivityFilterRequest = setSearchActivityRequest(advancedFilterRequest);
        if(!filteredActivityIds.isEmpty() || filteredBefore || !checkNull(searchActivityFilterRequest)) {
            if (!filteredActivityIds.isEmpty() || filteredBefore) {
                searchActivityFilterRequest.setIds(filteredActivityIds.stream().toList());
            }
            Page<SearchActivity> searchActivities = searchActivityService.getAll(searchActivityFilterRequest, page, size);
            return AdvancedSearchPageDto.builder()
                    .advancedSearchDtoList(searchActivities
                            .map(this::createAdvancedSearchDto)
                            .toList()
                    )
                    .totalElements(searchActivities.getTotalElements())
                    .totalPages(searchActivities.getTotalPages())
                    .build();
        }

        return AdvancedSearchPageDto.builder()
                .advancedSearchDtoList(Collections.emptyList())
                .totalElements(0)
                .totalPages(0)
                .build();

    }

    private ComponentCourtFilterRequest setComponentCourtFilterRequest(AdvancedFilterRequest filterRequest) {
        return ComponentCourtFilterRequest.builder()
                .docketNos(filterRequest.getDocketNos())
                .decisionNumbers(filterRequest.getDecisionNumbers())
                .filingDates(filterRequest.getComponentCourtFilingDates())
                .build();
    }

    @Override
    public List<MatterId> getMatterIdsByActivityIds(List<Long> activityIds) {
        return repository.findByIdIn(activityIds)
                .stream()
                .map(activity -> MatterId.builder()
                        .matterId(activity.getMatterId())
                        .build())
                .toList();
    }

    @Override
    public ActivityDto addSample(Long activityId, Long sampleId) {
        var activity = getActivityById(activityId);
        activity.getSamples().add(sampleId);
        var saved = repository.save(activity);
        return toDto(saved);
    }

    @Override
    public ActivityDto removeSample(Long activityId, Long sampleId) {
        var activity = getActivityById(activityId);
        activity.getSamples().remove(sampleId);
        var saved = repository.save(activity);
        return toDto(saved);
    }

    @Override
    public List<ActivityDto> getBySampleId(Long sampleId) {
        return repository.findBySamplesOrderByInstructionDateDesc(sampleId)
                .stream()
                .map(this::toDto)
                .toList();
    }

    @Override
    public List<Revision> getRevisions(Long id) {
        return revisionService.getAll(Activity.class, id);
    }

    @Override
    public List<Revision> getRevisionsByField(Long id, String field) {
        return revisionService.getByField(Activity.class, field, id);
    }

    @Override
    public List<Revision> getCdLetterRevisions(Long id) {
        return cdLetterRevisionService.getAll(CDLetter.class, id);
    }

    @Override
    public List<Revision> getUseInvestigationRevisions(Long id) {
        return useInvestigationRevisionService.getAll(UseInvestigation.class, id);
    }

    @Override
    public List<ActivityDto> getByMatterAndType(List<Long> matterIds, ActivityType type) {
        var activities = repository.findAllByMatterIdInAndType(matterIds, type);
        return activities.stream()
                .map(mapper::toActivityDto)
                .toList();
    }


    @Override
    public List<ActivityDto> getByMatter(List<Long> matterIds, String property, String direction) {
        return repository.findByMatterIdIn(matterIds, Sort.by(Sort.Direction.valueOf(direction.toUpperCase(Locale.ROOT)), property))
                .stream()
                .map(this::toDto)
                .toList();
    }

    @Override
    public ActivityDto getByIssueId(Long issueId) {
        var activity = repository.findByIssues(issueId)
                .orElse(null);
        return toDto(activity);
    }

    @Override
    public Activity getActivityByIssueId(Long issueId) {
        return repository.findByIssues(issueId)
                .orElse(null);
    }

    public Activity getActivityById(Long id) {
        return repository.findById(id)
                .orElseThrow(ActivityNotFoundException::new);
    }

    private void sendTransferEvent(Long id, TransferType transferType) {
        producerService.sendTransfer(TransferEvent.builder()
                .type(transferType)
                .domainObjectId(id)
                .domain(Domain.IPMS_ACTIVITY)
                .build());
    }

    private void sendLinkIssueEvent(List<Long> oldIssues, List<Long> issues, ActivityType activityType) {
        if (!ListUtils.emptyIfNull(issues).isEmpty()) {
            issues.stream()
                    .filter(id -> !oldIssues.contains(id))
                    .toList()
                    .forEach(id -> sendIssueEvent(id, activityType.name()));
        }
    }

    private void sendFirmAssociatedEvent(Long firmId) {
        var firmEvent = FirmEvent.builder()
                .ids(List.of(firmId))
                .build();
        producerService.send(firmAssociatedTopic,  ObjectUtils.toJson(firmEvent));
    }

    private void sendIssueEvent(Long issueId, String activityType) {
        var map = new HashMap<String, Object>();
        map.put("issueId", issueId);
        map.put("activityType", activityType);
        producerService.send(issueLinkedTopic, ObjectUtils.toJson(map));
    }

    private void sendDocumentEvent(Long documentId) {
        var map = new HashMap<String, Long>();
        map.put("id", documentId);
        producerService.send(documentUpdatedTopic, ObjectUtils.toJson(map));
    }

    private MatterFilterRequest setToMatterFilterRequest(AdvancedFilterRequest advancedFilterRequest) {
        return MatterFilterRequest.builder()
                .types(advancedFilterRequest.getMatterTypes())
                .countries(advancedFilterRequest.getCountries())
                .name(advancedFilterRequest.getMatterName())
                .no(advancedFilterRequest.getNo())
                .matterNoWithCountryCode(advancedFilterRequest.getMatterNo())
                .subjects(advancedFilterRequest.getSubjects())
                .coreBusinesses(advancedFilterRequest.getCoreBusinesses())
                .firms(advancedFilterRequest.getFirms())
                .build();
    }

    private SearchActivityFilterRequest setSearchActivityRequest(AdvancedFilterRequest advancedFilterRequest) throws IllegalAccessException {
        SearchActivityFilterRequest searchActivityFilterRequest = new SearchActivityFilterRequest();
        searchActivityFilterRequest.setSortField(advancedFilterRequest.getSortField());
        searchActivityFilterRequest.setSortDirection(advancedFilterRequest.getSortDirection());
        searchActivityFilterRequest.setTypes(advancedFilterRequest.getActivityTypes());
        searchActivityFilterRequest.setStatuses(advancedFilterRequest.getStatuses());
        searchActivityFilterRequest.setFirmIds(advancedFilterRequest.getFirmIds());
        searchActivityFilterRequest.setMatterIds(advancedFilterRequest.getMatterIds());
        searchActivityFilterRequest.setAgentReference(advancedFilterRequest.getAgentReference());
        searchActivityFilterRequest.setInstructionDates(advancedFilterRequest.getInstructionDates());


        var courtActionFilter = CourtActionFilter.builder()
                .stages(advancedFilterRequest.getCourtActionStages())
                .types(advancedFilterRequest.getCourtActionTypes())
                .build();
        if (!checkNull(courtActionFilter)) {
            searchActivityFilterRequest.setCourtActionFilter(courtActionFilter);
        }

        var cdLetterFilter = CDLetterFilter.builder()
                .dates(advancedFilterRequest.getCdLetterDates())
                .notificationDates(advancedFilterRequest.getNotificationDates())
                .postalTrackingNumbers(advancedFilterRequest.getPostalTrackingNumbers())
                .officialDocumentNumbers(advancedFilterRequest.getOfficialDocumentNumbers())
                .build();
        if (!checkNull(cdLetterFilter)) {
            searchActivityFilterRequest.setCdLetterFilter(cdLetterFilter);
        }

        var criminalActionFilter = CriminalActionFilter.builder()
                .complaintDate(advancedFilterRequest.getComplaintDates())
                .prosecutionNumber(advancedFilterRequest.getProsecutionNumbers())
                .build();
        if (!checkNull(criminalActionFilter)) {
            searchActivityFilterRequest.setCriminalActionFilter(criminalActionFilter);
        }

        searchActivityFilterRequest.setUseInvestigationCompletionDate(advancedFilterRequest.getCompletionDates());
        searchActivityFilterRequest.setCustomsDecisionNumbers(advancedFilterRequest.getCustomsDecisionNumbers());

        return searchActivityFilterRequest;
    }

    private boolean checkNull(Object o) throws IllegalAccessException {
        for (Field f : o.getClass().getDeclaredFields()) {
            f.setAccessible(true);
            if (f.get(o) != null && !f.getName().equals("sortField") && !f.getName().equals("sortDirection"))
                return false;
        }
        return true;
    }

    public List<IssueIdActivityIdMatterId> getAllByResponsibleMatter(String responsible) {

        var matterIds = matterClient.getMatterIdsList(responsible).getPayload().stream().toList();

        return repository.findAllByMatterIdIn(
                        matterIds,
                        Sort.by(
                                Sort.Direction.valueOf("desc".toUpperCase(Locale.ROOT)),
                                "matterId"))
                .stream()
                .filter(Objects::nonNull)
                .toList();
    }

    @Override
    public ActivityDto getMatterByDocketNo(String docketNo) {
        ComponentCourt componentCourt = componentCourtService.getByDocketNo(docketNo);
        if (componentCourt == null) {
            return null;
        }

        return getMatterByActivityId(componentCourt.getActivityId());
    }

    private ActivityDto getMatterByActivityId(Long activityId) {
        return getById(activityId);
    }

    @Override
    public List<ActivityId> getByCustoms(Long customsId) {
        return repository.findByCustomsSuspensionCustomsId(customsId).stream().toList();
    }

    @Override
    public List<Long> getIdsForTimesheetManuelApproval() {
        var matterIds = matterClient.getMatterIdListForSystemApproval().getPayload();
        return repository.findByTypeInAndMatterIdNotIn(
                        ActivityType.TIMESHEET_MANUEL_APPROVALS,
                        matterIds
                ).stream()
                .map(ActivityId::id)
                .toList();
    }

    public List<Long> getAllByActivity(Long activityId) {
        return repository.findByIdAndIssuesNotNull(activityId)
                .stream()
                .map(MatterIdAndIssues::issues)
                .filter(Objects::nonNull)
                .toList();
    }

    public List<IssueIdActivityIdMatterId> getMatterIdsByIssueIds(List<Long> issueIds) {
        return repository.findAllByIssuesIn(issueIds);
    }

    @Override
    public List<ActivityDto> getAllByIssueIds(List<Long> issueIds) {
        return repository.findByIssuesIn(issueIds).stream()
                .map(mapper::toActivityDto)
                .toList();
    }

    @Override
    public ActivityDto linkBillingAccount(Long activityId, Long billingAccountId) {
        var activity = getActivityById(activityId);
        var billingAccountDto = billingClient.getById(billingAccountId).getPayload();
        setRiskImpact(billingAccountDto, activity);
        activity.setBillingAccountId(billingAccountId);
        var saved = repository.save(activity);
        var activityDto = toDto(saved);
        sendTransferEvent(saved.getId(), TransferType.UPDATE);
        return activityDto;
    }

    private void setRiskImpact(BillingAccountDto billingAccountDto, Activity activity) {
        switch (billingAccountDto.getRiskManagement()) {
            case ADVANCE_PAYMENT_REQUIRED, HIGH_RISK -> activity.setRiskImpact(RiskImpact.LOCKED);
            case IRREGULAR_PAYER -> activity.setRiskImpact(RiskImpact.UNLOCKED);
            case NO_RISK -> activity.setRiskImpact(null);
            default -> throw new IllegalArgumentException(
                    String.format("Unknown risk management value: %s", billingAccountDto.getRiskManagement()));
        }
    }

    @Override
    public ActivityDto unlinkBillingAccount(Long activityId) {
        var activity = getActivityById(activityId);
        activity.setBillingAccountId(null);
        activity.setRiskImpact(null);
        var activityDto = toDto(repository.save(activity));
        sendTransferEvent(activity.getId(), TransferType.UPDATE);
        return activityDto;
    }

    @Override
    public List<Long> getOtherActivityIds(Long activityId) {
        var activity = getActivityById(activityId);
        var activities = repository.findByMatterIdIn(List.of(activity.getMatterId()));
        return activities.stream()
                .map(Activity::getId)
                .filter(id -> !id.equals(activityId))
                .toList();
    }

    @Override
    public void lockRiskImpactForActiveActivities(Long billingAccountId) {
        var activities = repository.findByBillingAccountId(billingAccountId);

        for (Activity activity : activities) {
            if(ActivityStatus.isActive(activity.getStatus())) {
                activity.setRiskImpact(RiskImpact.LOCKED);
                repository.save(activity);
                sendTransferEvent(activity.getId(), TransferType.UPDATE);
            }
        }
    }

    @Override
    public List<Long> getIssuesByMatters(List<Long> matterIds) {
        return repository.findAllByMatterIdIn(matterIds).stream()
                .map(MatterIdAndActivityStatus::issues)
                .distinct()
                .toList();
    }

    @Override
    public List<Long> getIssuesByMatterTypes(List<String> matterTypes) {
        var matterFilterRequest = MatterFilterRequest.builder()
                .types(matterTypes)
                .build();

        var matterIds =  matterClient.getMatterIdsList(matterFilterRequest).getPayload().stream().toList();
        return repository.findAllByMatterIdIn(matterIds).stream()
                .map(MatterIdAndActivityStatus::issues)
                .distinct()
                .toList();
    }

    @Override
    public ActivityDto importFromTurkPatent(ImportFromTpRequest request) {
        var activityDto = request.getActivityDto();
        var markInformationResponse = getMarkInformationFromTurkPatent(request.getApplicationNumber(),
                                                                       request.getRegistrationNumber());

        bulletinRelatedActivityService.apply(activityDto, markInformationResponse);
        var saved = save(activityDto);

        var matter = matterClient.getById(activityDto.getMatterId());
        if (Boolean.TRUE.equals(matter.getPayload().getMark().getIsOpposed())){
            handleCounterRightOwnerImport(saved.getId(), markInformationResponse);
        }

        sendUpdateMatterEvent(saved.getMatterId(), markInformationResponse);
        return saved;
    }

    private MarkInformationResponse getMarkInformationFromTurkPatent(String applicationNo, String registrationNo) {
        var markInfoRequest = MarkInformationRequestParam.builder()
                .appno(applicationNo)
                .registrationNo(registrationNo)
                .req_type("xml")
                .build();

        return derisClient.getMarkInformation(markInfoRequest);
    }

    private void handleCounterRightOwnerImport(Long activityId, MarkInformationResponse response) {
        if (response.getApplicants() != null && response.getApplicants().getApplicantList() != null) {
            response.getApplicants().getApplicantList().forEach(applicant ->
                    counterRightOwnerCardService.processCounterRightOwnerCardFromTurkPatent(activityId, applicant));
        }
    }

    private void sendUpdateMatterEvent(Long matterId, MarkInformationResponse markInformationResponse) {
        var matterUpdateEvent = MatterUpdateEvent.builder()
                .matterId(matterId)
                .name(markInformationResponse.getTrademark())
                .markClasses(markInformationResponse.getNiceClassList())
                .applicationNumber(markInformationResponse.getApplicationNo())
                .applicationDate(markInformationResponse.getApplicationDate())
                .logo(markInformationResponse.getLogo())
                .goodsAndServiceDtos(markInformationResponse.getGoodsAndServices().getGoodsAndServiceDtoList())
                .build();
        producerService.send(matterUpdateTopic, ObjectUtils.toJson(matterUpdateEvent));
    }

    @Override
    public BasisTrademarkDto createBasisTrademarkFromTurkPatent(ImportFromTpRequest request) {
        var markInfoResponse = getMarkInformationFromTurkPatent(request.getApplicationNumber(),
                request.getRegistrationNumber());

        List<SelectedMarkClassDto> markClassList = markInfoResponse.getNiceClassList().stream()
                .map(id -> SelectedMarkClassDto.builder().id(id).build())
                .toList();

        var basisTrademarkDto = BasisTrademarkDto.builder()
                .name(markInfoResponse.getTrademark())
                .applicationNumber(markInfoResponse.getApplicationNo())
                .applicationDate(markInfoResponse.getParsedApplicationDate())
                .classes(markClassList)
                .activityId(request.getActivityDto().getId())
                .matterId(request.getActivityDto().getMatterId())
                .countryId(225L)
                .build();

        var savedBasisTrademark = matterClient.saveImportedBasisTrademark(basisTrademarkDto);
        return savedBasisTrademark.getPayload();
    }

    @Override
    public void applicationDateChanged(MatterApplicationDateEvent event) {
        List<Activity> activities = repository.findByMatterIdIn(List.of(event.getMatterId()));
        activityDueDateService.processApplicationDateChange(activities);
    }

}
