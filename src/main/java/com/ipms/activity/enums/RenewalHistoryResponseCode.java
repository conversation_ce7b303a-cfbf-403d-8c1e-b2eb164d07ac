package com.ipms.activity.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum RenewalHistoryResponseCode implements Code {

    NOT_FOUND(1000, "error.code.renewal_history.not_found");

    private final Integer code;
    private final String messageKey;

    RenewalHistoryResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}