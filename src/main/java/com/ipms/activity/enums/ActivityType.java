package com.ipms.activity.enums;

import java.util.Arrays;
import java.util.EnumSet;
import java.util.Set;
import java.util.stream.Collectors;

public enum ActivityType {
    CEASE_AND_DESIST_LETTER("01"),
    CIVIL_ACTION("02"),
    COURT_ACTION("03"),
    CUSTOMS("04"),
    CUSTOMS_RECORDAL("05"),
    CUSTOMS_SUSPENSION("06"),
    CUSTOMS_TRAINING("07"),
    DETERMINATION_ACTION("08"),
    LEGAL_ADVICE("09"),
    USE_INVESTIGATION("10"),
    SEARCH("11"),
    REGISTRATION("12"),
    ATTESTATION_OF_USE("13"),
    ANNUITY("14"),
    RENEWAL("15"),
    CORRECTION("16"),
    RESTRICTION_OF_GOODS("17"),
    CHANGE_OF_ADDRESS("18"),
    CHANGE_OF_NAME("19"),
    CHANGE_OF_CORPORATE_STATUS("20"),
    ASSIGNMENT("21"),
    MERGER("22"),
    LICENCE("23"),
    CANCELLATION("24"),
    CERTIFIED_COPY("25"),
    OPPOSITION_OUT("26"),
    PUBLICATION_WATCH("27"),
    RENEWAL_OF_LICENCE("28"),
    CRIMINAL_ACTION("29"),
    REGISTRATION_BY_OTHER_AGENT("30"),
    APPLICATION_OR_OBJECTION("31"),
    ADMINISTRATIVE_ADVICE("32"),
    ANNUITY_HONG_KONG("33"),
    MP_REPLACEMENT("34"),
    EP_VALIDATION("35"),
    OFFICIAL_SEARCH("36"),
    PLEDGE_AGREEMENT("37"),
    SUCCESSION("38"),
    BULLETIN_WATCH("39"),
    OPINION("40"),
    OPPOSITION_BY_3RD_PARTY("41"),
    CHANGE_OF_ADDRESS_OF_LICENSEE("42"),
    CHANGE_OF_NAME_OF_LICENSEE("43"),
    CANCELLATION_OF_LICENSE("44"),
    PRIORITY_DOCUMENT("46"),
    DIVISION("47"),
    TMCH_RECORD("48"),
    TMCH_RENEWAL("49"),
    REGISTRATION_PACKAGE("50"),
    ARBITRATION("51"),
    MARKANGEL_WATCH("52"),
    WITHDRAWAL_OF_PLEDGE("53"),
    RIGHT_OWNER_OPPOSITION_TRACKING("54"),
    INT_APPLICATION_WATCH("55"),
    MADRID_PRO_TEVDI_ITIRAZ("56"),
    INTERNATIONAL_REGISTRATION("57"),
    NOTARY_DETERMINATION_ACTION("58"),
    PRELIMINARY_INJUCTION("59"),
    FORWARDING("60"),
    AGREEMENT("61"),
    RESEARCH("62"),
    APPEAL_AGAINST_ASSIGNMENT("63"),
    THIRD_PARTY_OBJECTION("64"),
    APPLICATION_OBJECTION_MONITORING("65"),
    RIGHTS_HOLDER_OBJECTION_MONITORING("66"),
    BULLETIN_MONITORING("67"),
    PUBLICATION_MONITORING("68"),
    GENERAL_BULLETIN_MONITORING("69"),
    USAGE_RESEARCH("70"),
    TRADEMARK_REGISTRATION("71"),
    OPPOSITION_IN("72"),
    OBJECTION("73");

    private final String value;

    ActivityType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static ActivityType getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }

    public static final Set<ActivityType> TIMESHEET_MANUEL_APPROVALS =
            EnumSet.of(LEGAL_ADVICE, COURT_ACTION, CEASE_AND_DESIST_LETTER, DETERMINATION_ACTION);

    public static final Set<ActivityType> TIMESHEET_SYSTEM_APPROVALS =
            Arrays.stream(ActivityType.values())
                    .filter(type -> !TIMESHEET_MANUEL_APPROVALS.contains(type))
                    .collect(Collectors.toSet());

    public static final Set<ActivityType> NON_BILLABLE_ACTIVITY_TYPES =
            EnumSet.of(OPPOSITION_OUT);

}
