package com.ipms.activity.repository;

import com.ipms.activity.dto.ActivityId;
import com.ipms.activity.dto.IssueIdActivityIdMatterId;
import com.ipms.activity.dto.MatterIdAndActivityStatus;
import com.ipms.activity.dto.MatterIdAndIssues;
import com.ipms.activity.enums.ActivityStatus;
import com.ipms.activity.enums.ActivityType;
import com.ipms.activity.enums.BillingStatus;
import com.ipms.activity.model.Activity;
import org.springframework.data.domain.Sort;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;

import java.time.LocalDateTime;
import java.util.List;
import java.util.Optional;
import java.util.Set;

public interface ActivityRepository extends PagingAndSortingRepository<Activity, Long>, JpaSpecificationExecutor<Activity> {
    List<Activity> findByMatterIdIn(List<Long> matterIds, Sort sort);
    Optional<Activity> findByIssues(Long issueId);
    List<Activity> findAllByIssues(Long issueId);
    List<Activity> findByIdIn (List<Long> ids);
    List<MatterIdAndIssues> findByIdAndIssuesNotNull (Long id);
    List<Activity> findByFirmId(Long firmId);
    List<IssueIdActivityIdMatterId> findByFirmIdIn(List<Long> firmIds);
    List<Activity> findByBillingStatus(BillingStatus billingStatus);
    List<MatterIdAndIssues> findByBillingStatusIn(List<BillingStatus> billingStatuses);
    List<Activity> findByMatterIdIn(List<Long> matterIds);
    List<MatterIdAndActivityStatus> findAllByMatterIdIn(List<Long> matterIds);
    List<Activity> findByDocumentsOrderByInstructionDateAsc(Long documentId);
    List<Activity> findByDocumentsIn(List<Long> documentIds);
    List<Activity> findByEvidencesOrderByInstructionDateDesc(Long documentId);
    List<Activity> findBySamplesOrderByInstructionDateDesc(Long sampleId);
    List<Activity> findAllByMatterIdInAndType(List<Long> matterIds, ActivityType type);
    List<IssueIdActivityIdMatterId> findAllByMatterIdIn(List<Long> matterIds, Sort sort);
    List<ActivityId> findByCustomsSuspensionCustomsId(Long customsId);
    List<ActivityId> findByTypeInAndMatterIdNotIn(Set<ActivityType> types, List<Long> matterIds);
    List<IssueIdActivityIdMatterId> findAllByIssuesIn(List<Long> issueId);
    List<Activity> findByIssuesIn(List<Long> issueId);
    List<Activity> findByBillingAccountIdIsNotNullAndBillingStatusIsNull();
    List<Activity> findByBillingAccountIdIsNotNullAndBillingStatusIsNullAndUpdatedAtBetweenAndStatusIn(
            LocalDateTime updatedAtStart, LocalDateTime updatedAtEnd, Set<ActivityStatus> statuses);
    List<Activity> findByBillingAccountId(Long billingAccountId);
    Optional<Activity> findTopByMatterIdAndType(Long matterId, ActivityType type);
}
