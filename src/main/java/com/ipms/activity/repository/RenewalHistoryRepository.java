package com.ipms.activity.repository;

import com.ipms.activity.model.Renewal;
import com.ipms.activity.model.RenewalHistory;
import org.springframework.data.repository.CrudRepository;

import java.util.List;

public interface RenewalHistoryRepository extends CrudRepository<RenewalHistory, Long> {
    Long countByRenewal(Renewal renewal);
    List<RenewalHistory> findByRenewal_IdOrderByPeriodAsc(Long renewalId);
}
