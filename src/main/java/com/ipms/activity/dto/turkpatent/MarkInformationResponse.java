package com.ipms.activity.dto.turkpatent;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.xml.bind.annotation.XmlAccessType;
import javax.xml.bind.annotation.XmlAccessorType;
import javax.xml.bind.annotation.XmlElement;
import javax.xml.bind.annotation.XmlRootElement;
import java.time.LocalDate;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.*;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
@XmlRootElement(name = "marka_basvuru")
@XmlAccessorType(XmlAccessType.FIELD)
public class MarkInformationResponse {

    @XmlElement(name = "basvuru_no")
    private String applicationNo;

    @XmlElement(name = "basvuranlar")
    private Applicants applicants;

    @XmlElement(name = "basvuru_tarihi")
    private String applicationDate;

    @XmlElement(name = "durum")
    private String status;

    @XmlElement(name = "tur")
    private String type;

    @XmlElement(name = "bulten_numarasi")
    private String bulletinNumber;

    @XmlElement(name = "bulten_tarihi")
    private String bulletinDate;

    @XmlElement(name = "evrak_no")
    private String documentNumber;

    @XmlElement(name = "gazete_numarasi")
    private String gazetteNumber;

    @XmlElement(name = "gazete_tarihi")
    private String gazetteDate;

    @XmlElement(name = "koruma_tarihi")
    private String protectionDate;

    @XmlElement(name = "marka")
    private String trademark;

    @XmlElement(name = "nice_siniflari")
    private String niceClasses;

    @XmlElement(name = "tescil_no")
    private String registrationNumber;

    @XmlElement(name = "tescil_tarihi")
    private String registrationDate;

    @XmlElement(name = "uluslararasi_tescil_no")
    private String internationalRegistrationNumber;

    @XmlElement(name = "vekil_bilgileri")
    private String attorneyInfo;

    @XmlElement(name = "vekil_bilgileri_adres")
    private String attorneyAddress;

    @XmlElement(name = "logo")
    private String logo;

    @XmlElement(name = "mal_ve_hizmet_listesi")
    private GoodsAndServices goodsAndServices;

    @XmlElement(name = "yenileme_talebi")
    private String renewalRequest;

    public LocalDate getParsedBulletinDate() {
        return parseDate(bulletinDate);
    }

    public LocalDate getParsedApplicationDate() {
        return parseDate(applicationDate);
    }

    private LocalDate parseDate(String rawDate) {
        return Optional.ofNullable(rawDate)
                .filter(s -> !s.isBlank())
                .map(s -> {
                    try {
                        return LocalDate.parse(s, DateTimeFormatter.ofPattern("dd.MM.yyyy"));
                    } catch (DateTimeParseException e) {
                        return null;
                    }
                })
                .orElse(null);
    }

    public List<Long> getNiceClassList() {
        return Optional.ofNullable(niceClasses)
                .map(String::trim)
                .filter(s -> !s.isEmpty() && !s.equals("-"))
                .map(s -> Arrays.stream(s.split("/"))
                        .map(String::trim)
                        .filter(str -> !str.isEmpty() && str.matches("\\d+"))
                        .map(Long::valueOf)
                        .toList())
                .orElse(Collections.emptyList());
    }
}