package com.ipms.activity.dto;

import com.ipms.activity.dto.groups.LinkActivityGroup;
import com.ipms.activity.dto.groups.UpdateActivityGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SearchActivityDto {
    @NotNull(groups = LinkActivityGroup.class)
    private Long id;
    @NotNull(groups = {UpdateActivityGroup.class, LinkActivityGroup.class})
    private Long version;
    private String type;
    private String status;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate instructionDate;
    private Long firmId;
    private Long matterId;
}
