package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CourtActionDto {
    private Long id;
    private String ourPosition;
    private boolean attendance;
    private List<String> types;
    private List<String> counterActions;
    private String stage;
}