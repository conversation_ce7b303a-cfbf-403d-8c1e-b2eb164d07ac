package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CustomsRecordalDto {
    private Long id;
    private Long version;
    private String matterType;
    private LocalDate renewalDate;
    private String registrationNumber;
}
