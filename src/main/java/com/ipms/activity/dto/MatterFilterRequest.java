package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatterFilterRequest {
    private String country;
    private String type;
    private List<String> types;
    private String name;
    private String coreBusiness;
    private List<String> coreBusinesses;
    private String subject;
    private List<String> partners;
    private List<String> associates;
    private String paralegal;
    private List<Long> firms;
    private String sortField;
    private String sortDirection;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime createdAt;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;
    private List<Long> ids;
    private String responsible;
    private String nameContains;
    private Long no;
    private List<String> countries;
    private List<String> subjects;
    private List<String> activityStatuses;
    private List<String> activityTypes;
    private String matterNoWithCountryCode;
}
