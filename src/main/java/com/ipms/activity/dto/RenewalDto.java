package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RenewalDto {
    private Long id;
    private Long version;
    private String bulletinNumber;
    private LocalDate bulletinPublicationDate;
    private LocalDate decisionDate;
    private LocalDate notificationDate;
    private String applicationNumber;
    private LocalDate renewalDate;
    private String decisionNumber;
    private List<RenewalHistoryDto> renewalHistories;
}
