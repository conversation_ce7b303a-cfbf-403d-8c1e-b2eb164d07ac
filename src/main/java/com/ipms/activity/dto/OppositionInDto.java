package com.ipms.activity.dto;

import com.ipms.activity.enums.AdverseApplicationStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OppositionInDto {
    private Long id;
    private Long version;
    private String bulletinNumber;
    private LocalDate bulletinPublicationDate;
    private LocalDate decisionDate;
    private AdverseApplicationStatus adverseApplicationStatus;
    private LocalDate notificationDate;
    private LocalDate applicationDate;
    private LocalDate registrationDate;
}
