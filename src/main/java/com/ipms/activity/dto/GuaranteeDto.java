package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuaranteeDto {
    private Long id;
    private Long version;
    @Positive
    private Long preliminaryInjunctionId;
    @NotNull
    private BigDecimal guaranteeAmount;
    @NotNull
    private String currencyCode;
    private String guaranteeType;
    private String partyProvidingGuarantee;
    private LocalDate dateOfDepositToCourt;
    private String safeNumber;
    private LocalDateTime createdAt;
    private String paymentStatus;
    private List<AttachmentDto> piMinutesAttachments;
    private List<AttachmentDto> proformaDebitLetterAttachments;
    private List<AttachmentDto> cashierReceiptAttachments;
    private List<AttachmentDto> letterOfGuaranteeAttachments;
    private List<AttachmentDto> receiptAttachments;
}