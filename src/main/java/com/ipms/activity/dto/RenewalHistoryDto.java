package com.ipms.activity.dto;

import com.ipms.activity.enums.ActivityStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RenewalHistoryDto {
    private Long id;
    private Long version;
    private Long period;
    private LocalDate dueDate;
    private LocalDate graceDate;
    private LocalDate actualDate;
    private LocalDate instructionDate;
    private ActivityStatus status;
    private Boolean isPaidBySomeoneElse;
    private Boolean isGracePeriodWithFine;
}
