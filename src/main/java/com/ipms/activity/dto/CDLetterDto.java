package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CDLetterDto {
    private Long id;
    private String ourPosition;
    private LocalDate date;
    private LocalDate notificationDate;
    private String notificationType;
    private String officialDocumentNumber;
    private String notaryNameNumber;
    private String postalTrackingNumber;
}
