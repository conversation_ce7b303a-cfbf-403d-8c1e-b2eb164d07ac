package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdvancedSearchDto {
    private Long activityId;
    private Long matterId;
    private String activityType;
    private String activityStatus;
    private LocalDate instructionDate;
    private Long firmId;
}
