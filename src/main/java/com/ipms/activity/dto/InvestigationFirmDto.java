package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class InvestigationFirmDto {
    private Long id;
    @NotNull
    private String name;
    private List<String> emails;
    private List<String> phones;
    private List<String> faxes;
    @NotNull
    private Long version;
}
