package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import javax.validation.constraints.Positive;
import javax.validation.groups.Default;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class TSICPackageDto {
    private Long id;

    @NotNull(groups = OnUpdate.class)
    private Long version;

    @NotNull
    private LocalDate startDate;

    private LocalDate endDate;

    @Positive
    private BigDecimal includedCharge;

    @NotNull
    private Boolean fixedFee;

    @NotNull
    private Boolean invoiced;

    private Boolean overTime;

    public interface OnUpdate extends Default {}
}
