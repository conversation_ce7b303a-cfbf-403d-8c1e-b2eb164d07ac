package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillingApprovedEvent {
    private Long activityId;
    private Long matterId;
    private Long billingAccountId;
    private String billingPeriodEnds;
    private String agentReference;
    private List<Long> expenseIds;
}
