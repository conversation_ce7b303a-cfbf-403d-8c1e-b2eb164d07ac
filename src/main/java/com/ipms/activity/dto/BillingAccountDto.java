package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillingAccountDto {
    private Long id;
    private String billingDay;
    private RiskManagement riskManagement;

    public enum RiskManagement {
        NO_RISK,
        IRREGULAR_PAYER,
        ADVANCE_PAYMENT_REQUIRED,
        HIGH_RISK,
    }
}
