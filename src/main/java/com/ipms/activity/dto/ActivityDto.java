package com.ipms.activity.dto;

import com.ipms.activity.dto.groups.LinkActivityGroup;
import com.ipms.activity.dto.groups.UpdateActivityGroup;
import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.Valid;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@AllArgsConstructor
public class ActivityDto {
    @NotNull(groups = LinkActivityGroup.class)
    @EqualsAndHashCode.Include
    private Long id;
    @NotNull(groups = {UpdateActivityGroup.class, LinkActivityGroup.class})
    private Long version;
    @NotNull
    private String type;
    @NotNull
    private String status;
    @NotNull
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate instructionDate;
    @NotNull
    private Long firmId;
    @NotNull
    private Long matterId;
    @NotNull(groups = LinkActivityGroup.class)
    private List<Long> issues;
    private BigDecimal includedCharge;
    private String agentReference;
    private Long billingAccountId;
    private String riskImpact;
    private String billingStatus;
    private LocalDateTime processStartDate;
    private LocalDate billingPeriodEnds;
    private String createdBy;
    private String updatedBy;
    private List<Long> documents;
    private List<Long> evidences;
    private List<Long> samples;
    private List<Long> quotations;
    @Valid
    private List<TSICPackageDto> tsicPackages;

    private CDLetterDto cdLetter;
    private UseInvestigationDto useInvestigation;
    private CourtActionDto courtAction;
    private DeterminationActionDto determinationAction;
    @Valid
    private PreliminaryInjunctionDto preliminaryInjunction;
    private CriminalActionDto criminalAction;
    private CustomsSuspensionDto customsSuspension;
    private CustomsRecordalDto customsRecordal;
    private NotaryDeterminationDto notaryDetermination;
    private OppositionOutDto oppositionOut;
    private RegistrationDto registration;
    private OppositionInDto oppositionIn;
    private ObjectionDto objection;
    private RenewalDto renewal;

    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime createdAt;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime updatedAt;
    private Long localAgent;
    private LocalDate statusDueDate;
}
