package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class RegistrationDto {
    private Long id;
    private Long version;
    private String bulletinNumber;
    private LocalDate bulletinPublicationDate;
    private LocalDate decisionDate;
    private LocalDate notificationDate;
}
