package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BasisTrademarkDto {
    private Long id;
    private String name;
    private Long countryId;
    private Long ipOfficeId;
    private String applicationNumber;
    private LocalDate applicationDate;
    private LocalDate useDate;
    private List<SelectedMarkClassDto> classes;
    private Long matterId;
    private Long activityId;
    private Long version;
}
