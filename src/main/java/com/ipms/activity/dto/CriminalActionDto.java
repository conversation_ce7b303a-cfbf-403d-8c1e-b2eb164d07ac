package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;


@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CriminalActionDto {
    private Long id;
    private String ourPosition;
    private Boolean attendance;
    private Long prosecutersOfficeId;
    private LocalDate complaintDate;
    private String prosecutionNumber;
    private Boolean exOfficioRaid;
    private Boolean transfer;
    private Boolean productsDestructed;
    private String deliveryType;
    private CriminalActionTrusteeDto trustee;
    private String receivingOffice;
    private String stage;
    private EnforcementDepartmentDto enforcementDepartment;
    private LocalDate executionDateOfSeizure;
    private String penaltyDecision;
    private List<CriminalActionProductDto> products;
}
