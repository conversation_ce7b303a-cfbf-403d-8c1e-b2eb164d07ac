package com.ipms.activity.dto;

import com.ipms.activity.dto.trademark.MarkDto;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class MatterDto {
    private Long id;
    private String country;
    private String type;
    private String name;
    private String coreBusiness;
    private String subject;
    private List<String> partners;
    private List<String> associates;
    private String paralegal;
    private List<Long> firms;
    private Long version;
    private String typeValue;
    private String timespentApproval;
    private String invoiceApproval;
    private Long no;
    private LocalDateTime createdAt;
    private MarkDto mark;
}
