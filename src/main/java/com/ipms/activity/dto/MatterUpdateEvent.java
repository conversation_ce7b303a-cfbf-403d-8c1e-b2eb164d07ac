package com.ipms.activity.dto;

import com.ipms.activity.dto.turkpatent.GoodsAndServiceDto;
import lombok.Builder;
import lombok.Data;
import lombok.ToString;

import java.util.List;

@Data
@Builder
@ToString
public class MatterUpdateEvent {
    private Long matterId;
    private String name;
    private List<Long> markClasses;
    private String logo;
    private String applicationNumber;
    private String applicationDate;
    private List<GoodsAndServiceDto> goodsAndServiceDtos;
}
