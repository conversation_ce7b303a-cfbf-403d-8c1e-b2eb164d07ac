package com.ipms.activity.dto;

import com.ipms.activity.enums.AdverseApplicationStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OppositionOutDto {
    private Long id;
    private Long version;
    private String bulletinNumber;
    private LocalDate bulletinPublicationDate;
    private LocalDate decisionDate;
    private Boolean basedOnWatch;
    private AdverseApplicationStatus adverseApplicationStatus;
    private LocalDate notificationDate;
    private LocalDate filingDate;
    private LocalDate appealDate;
}
