package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimesheetDto {
    private Long id;
    private Long activityId;
    private LocalDate date;
    private BigDecimal billableTime;
    private Boolean includedInCharge;
    private LocalDate expenseDate;
}
