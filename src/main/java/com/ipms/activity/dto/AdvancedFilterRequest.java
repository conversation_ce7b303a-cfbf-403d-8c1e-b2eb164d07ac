package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class AdvancedFilterRequest {
    //activity filter fields
    private List<String> activityTypes;
    private List<String> statuses;
    private List<Long> matterIds;
    private List<Long> firmIds;
    private String agentReference;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private List<LocalDateTime> instructionDates;
    private List<String> courtActionStages;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private List<LocalDate> componentCourtFilingDates;
    private List<String> decisionNumbers;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private List<LocalDate> completionDates;
    private List<String> postalTrackingNumbers;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private List<LocalDate> cdLetterDates;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private List<LocalDate> notificationDates;
    private List<String> officialDocumentNumbers;
    private List<String> courtActionTypes;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private List<LocalDate> complaintDates;
    private List<String> prosecutionNumbers;
    private List<String> docketNos;
    private List<String> customsDecisionNumbers;

    //matter filter fields
    private List<String> matterTypes;
    private List<String> countries;
    private String matterName;
    private Long no;
    private String matterNo;
    private List<String> subjects;
    private List<String> coreBusinesses;
    private List<Long> firms;

    //counter right owner
    private List<Long> counterRightOwnerIds;

    private String sortField;
    private String sortDirection;
}
