package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class UseInvestigationDto {
    private Long id;
    private String type;
    private LocalDate completionDate;
    private String researcher;
    private List<InvestigationFirmDto> investigationFirms;
    private Long version;
}
