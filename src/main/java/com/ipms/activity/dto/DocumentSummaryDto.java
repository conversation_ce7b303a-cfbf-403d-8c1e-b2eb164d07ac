package com.ipms.activity.dto;

import com.ipms.activity.enums.DocumentResult;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DocumentSummaryDto {
    private Long id;
    private LocalDate date;
    private DocumentResult result;
    private LocalDate expiryDate;
}