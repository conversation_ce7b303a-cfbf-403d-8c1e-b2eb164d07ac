package com.ipms.activity.dto;

import com.ipms.activity.dto.groups.UpdateActivityGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LawyerDto {
    private Long id;
    @NotBlank
    private String type;
    @NotBlank
    private String name;
    private String email;
    private String phoneNumber;
    @Valid
    private AgencyDto agency;
    @NotNull(groups = UpdateActivityGroup.class)
    private Long version;
}
