package com.ipms.activity.dto;

import com.ipms.activity.enums.DocumentResult;
import com.ipms.activity.enums.MatterType;
import com.ipms.activity.enums.OverTimeChoice;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityFilterRequest {
    private List<Long> ids;
    private List<Long> matterIds;
    private List<String> types;
    private List<String> statuses;
    private List<Long> firmIds;
    private String agentReference;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private List<LocalDateTime> instructionDates;
    private String courtActionStage;
    private String decisionNumber;
    private List<LocalDateTime> completionDates;
    private String postalTrackingNumber;
    private List<LocalDateTime> cdLetterDates;
    private List<LocalDateTime> notificationDates;
    private String officialDocumentNumber;
    private List<String> courtActions;
    private List<LocalDateTime> complaintDates;
    private String prosecutionNumber;
    private List<Long> quotationIds;
    private List<Long> expenseIds;
    private List<Long> billingAccountIds;
    private List<Long> documentIds;
    private OverTimeChoice overTimeChoice;
    private Long matterId;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate statusDueDateStart;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate statusDueDateEnd;
    private MatterType matterType;

    private String sortField;
    private String sortDirection;
    private List<DocumentResult> results;
    private List<String> coreBusinesses;
    private List<String> subjects;
}