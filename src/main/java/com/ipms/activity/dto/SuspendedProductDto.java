package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.Size;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class SuspendedProductDto {
    private Long id;
    @Size(max = 25)
    private String type;
    private Integer number;
    private Long customsRecordalMatterId;
    private Long activityId;
}
