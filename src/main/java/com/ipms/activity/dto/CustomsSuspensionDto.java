package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
public class CustomsSuspensionDto {
    private Long id;
    @NotNull
    private Long customsId;
    @NotEmpty
    @Size(max = 25)
    private String decisionNumber;
    @NotNull
    private LocalDate notificationDate;
    private Boolean isSmuggling;
    private String operationType;
    private String countryOfReciever;
    private String countryOfSender;
    private String countryOfTransit;
    private List<CustomsSuspensionsSenderDto> senders;
    private List<CustomsSuspensionsReceiverDto> receivers;
    private List<CustomsSuspensionsShippingDto> shippings;
}
