package com.ipms.activity.dto;

import com.ipms.activity.enums.CurrencyCode;
import com.ipms.activity.enums.PaymentStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class GuaranteePaymentDto {
    private Long id;
    private Long version;
    private BigDecimal amountPaidByClient;
    private CurrencyCode currencyCode;
    private LocalDate date;
    private PaymentStatus status;
    private Long guaranteeId;
}
