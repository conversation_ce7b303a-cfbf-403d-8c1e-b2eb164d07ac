package com.ipms.activity.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ComponentCourtDto {
    private Long id;

    @NotNull
    private Long activityId;

    @NotNull
    private Long courtId;

    private String judge;

    @NotNull
    private LocalDate filingDate;

    @NotNull
    private String docketNo;

    private LocalDate decisionNotificationDate;

    private String decisionNumber;

    private Long version;

    private String createdBy;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime createdAt;
    private String updatedBy;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime updatedAt;
}
