package com.ipms.activity.controller;

import com.ipms.activity.dto.RenewalHistoryDto;
import com.ipms.activity.service.RenewalHistoryService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("renewal-history")
public class RenewalHistoryController {
    private final RenewalHistoryService service;

    @GetMapping("/{renewalId}")
    public BaseResponse<List<RenewalHistoryDto>> getByRenewalId(@PathVariable Long renewalId) {
        return BaseResponse.<List<RenewalHistoryDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByRenewalId(renewalId))
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<RenewalHistoryDto> update(@PathVariable Long id, @Valid @RequestBody RenewalHistoryDto dto) {
        var renewalHistoryDto = service.update(id, dto);
        return BaseResponse.<RenewalHistoryDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(renewalHistoryDto)
                .build();
    }
}
