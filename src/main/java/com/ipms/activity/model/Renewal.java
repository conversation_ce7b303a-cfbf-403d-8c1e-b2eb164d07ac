package com.ipms.activity.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "renewal")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
public class Renewal extends BulletinRelatedActivity {
    @Column
    private String applicationNumber;

    @Column
    private LocalDate renewalDate;

    @Column
    private String decisionNumber;

    @OneToMany(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE}, mappedBy = "renewal")
    @JsonBackReference
    private List<RenewalHistory> renewalHistories;
}
