package com.ipms.activity.model;

import com.ipms.activity.enums.ActivityStatus;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "renewal_history")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
public class RenewalHistory extends VersionedEntity {
    @Column
    private Long period;

    @Column
    private LocalDate dueDate;

    @Column
    private LocalDate graceDate;

    @Column
    private LocalDate actualDate;

    @Column
    private LocalDate instructionDate;

    @Column
    private ActivityStatus status;

    @Column
    private Boolean isPaidBySomeoneElse;

    @Column
    private Boolean isGracePeriodWithFine;

    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "renewal_id")
    private Renewal renewal;
}
