package com.ipms.billing.dto;

import com.ipms.billing.enums.BillingOrderStatus;
import lombok.*;

import javax.validation.Valid;
import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.constraints.Size;
import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@AllArgsConstructor
public class BillingOrderCreateDto {
    @NotNull
    private LocalDate orderDate;
    private String businessUnit;
    @NotNull
    private Long issuerId;
    @NotEmpty
    private String billingAccountNo;
    @NotNull
    private Long billingAccountId;
    @Valid
    private List<BillingOrderDetailDto> orderDetails;
    @NotEmpty
    private String description1;
    @NotEmpty
    private String description2;
    @NotEmpty
    private String description3;
    @Size(max = 255)
    private String externalOrderNumber;
    private BillingOrderStatus status;
    @Size(max = 255)
    private String invoiceNumber;
    private LocalDate invoiceDate;
    private List<Long> expenseIds;
    private Long activityId;
    private String assignee;
}
