package com.ipms.billing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimesheetTotalTimeDto {
    private BigDecimal totalLoggedTime;
    private BigDecimal totalBillableTime;
    private TotalAmountDto totalAmount;
    private BigDecimal totalIncludedInCharge;
    private BigDecimal tsicMin;
    private BigDecimal tsicUsedBefore;
    private BigDecimal tsicUsedInCurrentProcess;
    private BigDecimal tsicRemaining;
    private Boolean fixedFee;
}
