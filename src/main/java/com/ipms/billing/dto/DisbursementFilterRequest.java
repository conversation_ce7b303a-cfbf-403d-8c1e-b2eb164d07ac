package com.ipms.billing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class DisbursementFilterRequest {
    @NotNull(groups = ToBeBilled.class)
    private List<Long> ids;
    @NotNull(groups = ToBeBilled.class)
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate billingPeriodEnds;
    private String sortField;
    private String sortDirection;

    public interface ToBeBilled {}
}
