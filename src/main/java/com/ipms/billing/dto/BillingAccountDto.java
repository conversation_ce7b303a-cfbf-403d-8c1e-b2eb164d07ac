package com.ipms.billing.dto;

import lombok.*;
import org.springframework.format.annotation.DateTimeFormat;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@AllArgsConstructor
public class BillingAccountDto {
    private Long id;
    @NotNull(groups = OnUpdate.class)
    private Long version;
    @NotEmpty
    private String accountName;
    @NotEmpty
    private String accountNo;
    @NotNull
    private Long issuerId;
    @NotEmpty
    private String address;
    private BigDecimal discountRate;
    @NotEmpty
    private String currency;
    private String currencyDisbursement;
    @NotEmpty
    private String riskManagement;
    private String description;
    private List<Long> issueIds;

    //Details
    private String billingDay;
    private String endOfBillingPeriod ;
    private Boolean willBeSentToResponsible;
    private Boolean createOrderAutomatically;
    private List<String> sendToEmails;
    private List<String> copyToEmails;
    private Boolean willOriginalInvoiceBeSent;
    private String originalInvoiceRecipient;
    private String billingSystem;
    private String attachmentsToBeSent;
    private String note;
    private String languageToBeBilled;

    private String createdBy;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime createdAt;
    private String updatedBy;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime updatedAt;

    public interface OnUpdate extends Default {}
}