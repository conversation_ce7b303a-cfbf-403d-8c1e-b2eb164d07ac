package com.ipms.billing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimesheetIncludedInChargeRequest {
    private Long activityId;
    private LocalDate packageStartDate;
    private BigDecimal tsicIncludedCharge;
}
