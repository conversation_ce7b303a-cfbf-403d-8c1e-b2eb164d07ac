package com.ipms.billing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillingApprovedEvent {
    private String user;
    private Long activityId;
    private Long billingAccountId;
    private String billingPeriodEnds;
    private Long matterId;
    private String agentReference;
    private List<Long> expenseIds;
}
