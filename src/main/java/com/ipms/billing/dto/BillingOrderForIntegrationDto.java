package com.ipms.billing.dto;

import lombok.*;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@AllArgsConstructor
public class BillingOrderForIntegrationDto {
    private Long id;
    private Long version;
    private String orderNumber;
    private LocalDate orderDate;
    private String businessUnit;
    private List<BillingOrderDetailDto> orderDetails;
    private String description1;
    private String description2;
    private String description3;
    private String currencyCode;

}
