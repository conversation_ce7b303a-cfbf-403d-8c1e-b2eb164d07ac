package com.ipms.billing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpenseSummaryDto {
    private String expenseCode;
    private String currency;
    private String definition;
    private BigDecimal unitPrice;
    private BigDecimal discountRate;
    private Long quantity;
    private BigDecimal totalAmount;
    private List<Long> expenseIds;
}
