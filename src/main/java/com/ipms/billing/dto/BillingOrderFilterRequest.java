package com.ipms.billing.dto;

import com.ipms.billing.enums.BillingOrderStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillingOrderFilterRequest {
    private List<Long> ids;
    private List<String> orderNumbers;
    private String assignee;
    private String invoiceNumber;
    private String orderNumber;
    private List<Long> issuerIds;
    private List<String> assignees;
    private List<BillingOrderStatus> statuses;
    private String createdBy;
    private String updatedBy;
    private String sortField;
    private String sortDirection;
}
