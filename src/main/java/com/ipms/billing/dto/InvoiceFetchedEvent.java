package com.ipms.billing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@AllArgsConstructor
@NoArgsConstructor
@Data
@Builder
public class InvoiceFetchedEvent {
    private List<Invoice> invoices;

    @Data
    @AllArgsConstructor
    @NoArgsConstructor
    @Builder
    public static class Invoice {
        private String erpDocumentCompany;
        private String erpDocumentNo;
        private String erpDocumentType;
        private String erpOrderNumber;
        private String erpOrderType;
        private String customerorSupplierNumber;
        private String customerorSupplierName;
        private String address;
        private String country;
        private String city;
        private String postalCode;
        private String taxNo;
        private String taxSchemeName;
        private String currencyCode;
        private String invoiceNo;
        private String invoiceDate;
        private String lineExtensionAmount;
        private String taxExclusiveAmount;
        private String taxInclusiveAmount;
        private String payableAmount;
        private List<InvoiceLine> invoiceLines;

    }

    @AllArgsConstructor
    @NoArgsConstructor
    @Data
    @Builder
    public static class InvoiceLine {
        private String billingId;
        private List<Long> expenseIds;
        private String itemNo;
        private String itemDescription;
        private String invoiceLineNo;
        private String unitofMeasure;
        private String quantity;
        private String priceAmount;
        private String allowanceCharge;
        private String taxRate;
        private String taxAmount;
        private String extensionAmount;
    }
}
