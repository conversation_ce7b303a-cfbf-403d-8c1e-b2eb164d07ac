package com.ipms.billing.dto;

import com.ipms.billing.enums.ExpenseCodeType;
import lombok.*;

import java.math.BigDecimal;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpenseCodeResponse {
    private int code;
    private Payload payload;

    @Data
    @Builder
    public static class Payload {
        private String definition;
        private BigDecimal unitPrice;
        private ExpenseCodeType type;
    }
}
