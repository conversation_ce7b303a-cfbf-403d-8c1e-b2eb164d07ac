package com.ipms.billing.dto;

import lombok.*;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityResponse {
    private int code;
    private Payload payload;

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Payload {
        private Long id;
        private Long matterId;
        private Long billingAccountId;
        private LocalDate billingPeriodEnds;
        private List<TSICPackageDto> tsicPackages;
    }
}
