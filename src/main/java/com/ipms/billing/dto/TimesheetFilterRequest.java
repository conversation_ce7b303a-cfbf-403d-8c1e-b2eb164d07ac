package com.ipms.billing.dto;

import com.ipms.billing.enums.ApprovalType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class TimesheetFilterRequest {
    private List<Long> activityIds;
    private List<Long> activityIdsNotIn;
    private Long activityId;
    private List<Long> matterIds;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime startDate;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime endDate;
    private ApprovalType approvalType;
    private Boolean isApproved;
    private List<String> partners;
    private List<String> timespentApprovals;
    private List<String> createdBys;
    private String updatedBy;
    private String sortField;
    private String sortDirection;
}
