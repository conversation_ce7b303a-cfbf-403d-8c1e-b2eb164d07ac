package com.ipms.billing.dto;

import lombok.*;

import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExchangeRateResponse {
    private int code;
    private ExchangeRateResponseDto payload;

    @Data
    @Builder
    public static class ExchangeRateResponseDto {
        private String fromCurrencyCode;
        private String toCurrencyCode;
        private LocalDate currencyDate;
        private BigDecimal exchangeRate;
        private LocalDate dateOfUpdate;
    }
}
