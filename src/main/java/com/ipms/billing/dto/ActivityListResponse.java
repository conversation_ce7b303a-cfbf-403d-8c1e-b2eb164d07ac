package com.ipms.billing.dto;

import lombok.*;

import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@NoArgsConstructor
@AllArgsConstructor
@Builder
public class ActivityListResponse {
    private int code;
    private List<Payload> payload;

    @Getter
    @Setter
    @Builder
    @NoArgsConstructor
    @AllArgsConstructor
    public static class Payload {
        private Long id;
        private Long billingAccountId;
        private String billingStatus;
        private LocalDate billingPeriodEnds;
    }
}
