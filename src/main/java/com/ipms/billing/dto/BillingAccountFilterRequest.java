package com.ipms.billing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class BillingAccountFilterRequest {
    private List<Long> ids;
    private List<String> accountNames;
    private List<String> accountNos;
    private List<Long> issueIds;
    private String searchKey;

    private String sortField;
    private String sortDirection;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime createdAt;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE_TIME)
    private LocalDateTime updatedAt;
    private String createdBy;
    private String updatedBy;

}
