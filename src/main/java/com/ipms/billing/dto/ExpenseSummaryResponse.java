package com.ipms.billing.dto;

import lombok.*;

import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpenseSummaryResponse {
    private int code;
    private List<Payload> payload;

    @Getter
    @Setter
    @Builder
    public static class Payload {
        private String expenseCode;
        private String currency;
        private String definition;
        private BigDecimal unitPrice;
        private Long quantity;
        private BigDecimal totalAmount;
        private List<Long> expenseIds;
    }
}
