package com.ipms.billing.dto;

import lombok.*;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;
import javax.validation.groups.Default;
import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@EqualsAndHashCode(onlyExplicitlyIncluded = true)
@AllArgsConstructor
public class BillingAccountExpenseCodeDto {

    private Long id;

    @NotNull(groups = OnUpdate.class)
    private Long version;

    @NotNull
    private Long billingAccountId;

    @NotEmpty
    private String generalExpenseCode;

    @NotEmpty
    private String specialExpenseCode;

    @NotEmpty
    private BigDecimal unitPrice;

    private String countryCode;

    private String protectionType;

    private String activityType;

    public interface OnUpdate extends Default {}
}
