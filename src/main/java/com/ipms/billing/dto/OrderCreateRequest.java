package com.ipms.billing.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class OrderCreateRequest {
    private String orderNumber;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate orderDate;
    private List<Long> expenseIds;
    private Boolean autoCreate;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate billingPeriodEnds;
    private String billingAccountCurrency;
}
