package com.ipms.billing.dto;

import com.ipms.billing.enums.PaymentStatus;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PaymentFilterRequest {
    private List<String> accrualNumbers;
    private List<PaymentStatus> statuses;
    private List<Long> issuerIds;
    private List<Long> matterIds;
    private String sortField;
    private String sortDirection;
}
