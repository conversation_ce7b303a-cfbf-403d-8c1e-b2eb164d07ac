package com.ipms.billing.client;

import com.ipms.billing.dto.BooleanResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient(value = "ipms-integration", path = "/api/integration")
public interface IntegrationClient {

    @GetMapping("/billing-order-integration/billing-order/{issuerId}/{externalOrderNumber}/{billingAccountNo}/is-cancelled")
    BooleanResponse isOrderCancelled(@PathVariable Long issuerId,
                                     @PathVariable String externalOrderNumber,
                                     @PathVariable String billingAccountNo);

}
