package com.ipms.billing.client;

import com.ipms.billing.dto.ActivityIdsResponse;
import com.ipms.billing.dto.ActivityListResponse;
import com.ipms.billing.dto.ActivityResponse;
import com.ipms.billing.dto.MatterIdsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.PutMapping;

import java.util.List;


@FeignClient(name = "ipms-activity", path = "/api/activity")
public interface ActivityClient {

    @PutMapping("/order-created-status/{activityId}")
    void setOrderCreatedStatus(@PathVariable Long activityId);

    @GetMapping("/expense/{expenseId}")
    ActivityResponse getActivityByExpenseId(@PathVariable Long expenseId);

    @GetMapping("/expenses-matterid/{expenseIds}")
    MatterIdsResponse getMatterIdsByExpenses(@PathVariable List<Long> expenseIds);

    @PutMapping("/cancel-billing-order/{activityId}")
    void cancelBillingOrder(@PathVariable Long activityId);

    @PutMapping("/expense/{activityId}/{expenseId}")
    ActivityResponse addExpense(@PathVariable Long activityId,
                                @PathVariable Long expenseId);

    @PutMapping("/expenses/{activityId}/{expenseIds}")
    ActivityResponse addExpenses(@PathVariable Long activityId,
                                 @PathVariable List<Long> expenseIds);

    @PutMapping("/disbursement/{activityId}/{disbursementId}")
    ActivityResponse addDisbursement(@PathVariable Long activityId,
                                     @PathVariable Long disbursementId);

    @GetMapping("/{activityId}")
    ActivityResponse getActivity(@PathVariable Long activityId);

    @GetMapping("/expenses/{expenseIds}")
    ActivityListResponse getActivitiesByExpenses(@PathVariable List<Long> expenseIds);

    @GetMapping("/billing-cart-activities/{assignee}")
    ActivityListResponse getBillingCartActivities(@PathVariable String assignee);

    @GetMapping("/activities/ids?timesheetApprovalType=MANUEL")
    ActivityIdsResponse getTimesheetManuelApprovalActivityIds();

    @GetMapping("/other-activity-ids/{activityId}")
    ActivityIdsResponse getOtherActivityIds(@PathVariable Long activityId);
}
