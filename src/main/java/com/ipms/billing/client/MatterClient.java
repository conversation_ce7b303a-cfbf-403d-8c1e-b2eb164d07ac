package com.ipms.billing.client;

import com.ipms.billing.dto.IdsResponse;
import com.ipms.billing.dto.MatterResponse;
import com.ipms.billing.dto.MatterSummaryResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;

import java.util.List;

@FeignClient(name = "ipms-matter", path = "/api/matter")
public interface MatterClient {

    @GetMapping("/{id}")
    MatterResponse getById(@PathVariable Long id);

    @GetMapping("/matter-summary/")
    MatterSummaryResponse getMatterSummary(@RequestParam(name = "matterIds") List<Long> matterIds);

    @GetMapping("/matter-ids")
    IdsResponse getMatterIds(@RequestParam List<String> partners,
                             @RequestParam List<String> timespentApprovals,
                             @RequestParam List<Long> ids);

}
