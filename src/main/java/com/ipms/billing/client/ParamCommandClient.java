package com.ipms.billing.client;

import com.ipms.billing.dto.*;
import com.ipms.core.exception.config.RetreiveMessageErrorDecoder;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;

@FeignClient(value = "ipms-paramcommand", path = "/api/paramcommand", configuration = RetreiveMessageErrorDecoder.class)
public interface ParamCommandClient {

    @GetMapping("/expense-code/unit-price/is-defined/{code}/{currency}")
    BooleanResponse isUnitPriceDefined(@PathVariable String code, @PathVariable String currency);

    @GetMapping("/issuer")
    IssuerResponse getIssuers();


    @GetMapping("/expense-code/unit-price/{code}/{currency}")
    UnitPriceResponse getUnitPrice(@PathVariable String code, @PathVariable String currency);

    @GetMapping("/expense-code/summary/{code}/{currency}")
    ExpenseCodeResponse getExpenseCode(@PathVariable String code, @PathVariable String currency);

    @GetMapping("/exchange-rate/latest")
    ExchangeRateResponse getLatestExchangeRate(
            @RequestParam String fromCurrencyCode,
            @RequestParam String toCurrencyCode,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate currencyDate);

    @GetMapping("/expense-code/validate/{codes}/{currency}")
    void validateExpenseCodes(@PathVariable List<String> codes, @PathVariable String currency);

    @PostMapping("/expense-code/eligible-for-discount")
    ListLongResponse getDiscountEligibleExpenseIds(@RequestBody List<ExpenseDto> expenses);
}
