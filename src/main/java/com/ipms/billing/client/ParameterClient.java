package com.ipms.billing.client;

import com.ipms.billing.dto.ParameterResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;

@FeignClient("ipms-paramquery")
public interface ParameterClient {

    @GetMapping("/api/paramquery/{key}")
    ParameterResponse getByKey(@PathVariable String key);

}
