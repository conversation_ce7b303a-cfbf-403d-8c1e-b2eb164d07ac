package com.ipms.billing.validator;

import com.ipms.billing.dto.TSICPackageDto;
import com.ipms.billing.dto.TimesheetDto;
import com.ipms.billing.enums.TimesheetResponseCode;
import com.ipms.billing.exception.TimesheetValidationException;
import org.springframework.stereotype.Component;

import java.util.Comparator;
import java.util.List;
import java.util.Optional;

@Component
public class TimesheetValidator {

    public void validateTimesheetDate(TimesheetDto dto, List<TSICPackageDto> tsicPackages) {
        validateTimesheetDateAgainstPackageStartDate(dto, tsicPackages);
        validateTimesheetDateAgainstPackageEndDate(dto, tsicPackages);
    }

    private void validateTimesheetDateAgainstPackageStartDate(TimesheetDto dto, List<TSICPackageDto> tsicPackages) {
        getActiveTsicPackage(tsicPackages).ifPresent(activeTsicPackage -> {
            if (dto.getDate().isBefore(activeTsicPackage.getStartDate())) {
                throw new TimesheetValidationException(
                        TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_START_DATE,
                        activeTsicPackage.getStartDate().toString()
                );
            }
        });
    }

    private void validateTimesheetDateAgainstPackageEndDate(TimesheetDto dto, List<TSICPackageDto> tsicPackages) {
        boolean activePackageExists = tsicPackages.stream()
                .anyMatch(pkg -> pkg.getEndDate() == null);

        if (!activePackageExists) {
            getLatestClosedPackage(tsicPackages).ifPresent(closedPackage -> {
                if (!dto.getDate().isAfter(closedPackage.getEndDate())) {
                    throw new TimesheetValidationException(
                            TimesheetResponseCode.TIMESHEET_DATE_BEFORE_PACKAGE_END_DATE,
                            closedPackage.getEndDate().toString()
                    );
                }
            });
        }
    }

    private Optional<TSICPackageDto> getActiveTsicPackage(List<TSICPackageDto> tsicPackages) {
        return tsicPackages.stream()
                .filter(tsicPackage -> tsicPackage.getEndDate() == null)
                .max(Comparator.comparing(TSICPackageDto::getStartDate));
    }

    private Optional<TSICPackageDto> getLatestClosedPackage(List<TSICPackageDto> tsicPackages) {
        return tsicPackages.stream()
                .filter(pkg -> pkg.getEndDate() != null)
                .max(Comparator.comparing(TSICPackageDto::getStartDate));
    }
}