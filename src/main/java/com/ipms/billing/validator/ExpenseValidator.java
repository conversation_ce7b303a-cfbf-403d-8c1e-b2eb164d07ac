package com.ipms.billing.validator;

import com.ipms.billing.dto.ExpenseDto;
import com.ipms.billing.enums.ExpenseResponseCode;
import com.ipms.billing.enums.PaymentStatus;
import com.ipms.billing.exception.AccrualNumberEditNotAllowedException;
import com.ipms.billing.model.Expense;
import org.springframework.stereotype.Component;

import java.util.Objects;

@Component
public class ExpenseValidator {
    public void validateUpdate(Expense existingExpense, ExpenseDto updatedExpense) {
        validateAccrualNumberEdit(existingExpense, updatedExpense);
    }

    private void validateAccrualNumberEdit(Expense existingExpense, ExpenseDto updatedExpense) {
        boolean accrualNumberChanged = !Objects.equals(
                existingExpense.getAccrualNumber(),
                updatedExpense.getAccrualNumber()
        );

        if (accrualNumberChanged &&
                existingExpense.getPayment() != null &&
                PaymentStatus.PAID.equals(existingExpense.getPayment().getStatus())) {
            throw new AccrualNumberEditNotAllowedException(ExpenseResponseCode.ACCRUAL_NUMBER_EDIT_NOT_ALLOWED);
        }
        if(accrualNumberChanged && existingExpense.getPayment() != null) {
            updatedExpense.getPayment().setAccrualNumber(updatedExpense.getAccrualNumber());
        }
    }
}
