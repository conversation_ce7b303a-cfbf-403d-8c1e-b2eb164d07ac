package com.ipms.billing.validator;

import com.ipms.billing.dto.BillingAccountDto;
import com.ipms.billing.exception.BillingAccountValidationException;
import com.ipms.billing.repository.BillingAccountRepository;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Component;

import static com.ipms.billing.enums.BillingAccountResponseCode.ACCOUNT_NAME_MUST_BE_UNIQUE;
import static com.ipms.billing.enums.BillingAccountResponseCode.ACCOUNT_NO_AND_ISSUER_MUST_BE_UNIQUE;

@Component
@RequiredArgsConstructor
public class BillingAccountValidator {
    private final BillingAccountRepository repository;


    public void validate(BillingAccountDto dto) {
        repository.findByAccountName(dto.getAccountName()).ifPresent(billingAccount -> {
                    throw new BillingAccountValidationException(
                            "Account name must be unique", ACCOUNT_NAME_MUST_BE_UNIQUE);

                });
        repository.findByAccountNoAndIssuerId(dto.getAccountNo(), dto.getIssuerId())
                .ifPresent(billingAccount -> {
                    throw new BillingAccountValidationException(
                            "Account no and issuer value must be unique", ACCOUNT_NO_AND_ISSUER_MUST_BE_UNIQUE);
                });
    }

    public void validate(Long id, BillingAccountDto dto) {
        repository.findByAccountName(dto.getAccountName()).ifPresent(billingAccount -> {
            if (!billingAccount.getId().equals(id)) {
                throw new BillingAccountValidationException(
                        "Account name must be unique", ACCOUNT_NAME_MUST_BE_UNIQUE);
            }
        });
        repository.findByAccountNoAndIssuerId(dto.getAccountNo(), dto.getIssuerId())
                .ifPresent(billingAccount -> {
                    if (!billingAccount.getId().equals(id)) {
                        throw new BillingAccountValidationException(
                                "Account no and issuer value must be unique", ACCOUNT_NO_AND_ISSUER_MUST_BE_UNIQUE);
                    }
                });
    }

}
