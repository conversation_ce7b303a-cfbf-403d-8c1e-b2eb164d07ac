package com.ipms.billing.mapper;

import com.ipms.billing.dto.ExpenseDto;
import com.ipms.billing.model.Expense;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface ExpenseMapper {
    Expense toExpense (ExpenseDto dto);

    @Mapping(target = "activityId", ignore = true)
    ExpenseDto toExpenseDto (Expense expense);

    List<ExpenseDto> toExpenseDtoList (List<Expense> expense);

    Expense toExpenseFromDto(ExpenseDto dto, @MappingTarget Expense expense);
}
