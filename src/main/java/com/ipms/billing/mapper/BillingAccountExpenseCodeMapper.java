package com.ipms.billing.mapper;

import com.ipms.billing.dto.BillingAccountExpenseCodeDto;
import com.ipms.billing.model.BillingAccountExpenseCode;
import org.mapstruct.Mapper;

@Mapper(componentModel = "spring")
public interface BillingAccountExpenseCodeMapper {
    BillingAccountExpenseCodeDto toBillingAccountExpenseCodeDto(BillingAccountExpenseCode billingAccountExpenseCode);

    BillingAccountExpenseCode toBillingAccountExpenseCode(BillingAccountExpenseCodeDto billingAccountExpenseCodeDto);
}
