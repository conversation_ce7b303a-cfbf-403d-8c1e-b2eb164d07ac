package com.ipms.billing.mapper;

import com.ipms.billing.dto.PaymentDto;
import com.ipms.billing.model.Payment;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface PaymentMapper {
    Payment toPayment(PaymentDto dto);
    PaymentDto toPaymentDto(Payment payment);
    @Mapping(target = "id", ignore = true)
    Payment toPaymentFromDto(PaymentDto dto, @MappingTarget Payment payment);
}
