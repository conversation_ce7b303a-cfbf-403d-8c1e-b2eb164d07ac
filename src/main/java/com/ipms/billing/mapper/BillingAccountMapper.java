package com.ipms.billing.mapper;

import com.ipms.billing.dto.BillingAccountDto;
import com.ipms.billing.model.BillingAccount;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface BillingAccountMapper {
    BillingAccountDto toBillingAccountDto(BillingAccount billingAccount);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "version", ignore = true)
    BillingAccount toBillingAccount(BillingAccountDto billingAccountDto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    BillingAccount toBillingAccountFromDto(BillingAccountDto dto, @MappingTarget BillingAccount billingAccount);
}