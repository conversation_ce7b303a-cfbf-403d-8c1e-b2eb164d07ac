package com.ipms.billing.mapper;

import com.ipms.billing.dto.BillingOrderCreateDto;
import com.ipms.billing.dto.BillingOrderDto;
import com.ipms.billing.model.BillingOrder;
import org.mapstruct.*;

import java.util.Optional;

@Mapper(componentModel = "spring", builder = @Builder(disableBuilder = true))
public interface BillingOrderMapper {

    BillingOrderDto toBillingOrderDto(BillingOrder billingOrder);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "version", ignore = true)
    @Mapping(target = "orderNumber", ignore = true)
    BillingOrder toBillingOrder(BillingOrderDto billingOrderDto);

    BillingOrder toBillingOrder(BillingOrderCreateDto billingOrderDto);

    @Mapping(target = "id", ignore = true)
    @Mapping(target = "createdAt", ignore = true)
    @Mapping(target = "createdBy", ignore = true)
    @Mapping(target = "orderNumber", ignore = true)
    BillingOrder toBillingOrderFromDto(BillingOrderDto dto, @MappingTarget BillingOrder billingOrder);

    @AfterMapping
    default void setBillingOrder(@MappingTarget BillingOrder billingOrder) {
        Optional.ofNullable(billingOrder.getOrderDetails())
                .ifPresent(orderDetails -> orderDetails
                        .forEach(orderDetail -> orderDetail.setBillingOrder(billingOrder)));

    }
}
