package com.ipms.billing.mapper;

import com.ipms.billing.dto.TimesheetDto;
import com.ipms.billing.dto.TimesheetFilterRequest;
import com.ipms.billing.model.Timesheet;
import com.ipms.billing.specification.TimesheetSpecification;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

import java.util.List;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface TimesheetMapper {
    Timesheet toTimesheet(TimesheetDto timesheetDto);
    TimesheetDto toTimesheetDto(Timesheet timesheet);
    List<TimesheetDto> convertToDtoList(List<Timesheet> timesheetList);
    Timesheet toTimesheetFromDto(TimesheetDto dto, @MappingTarget Timesheet timesheet);

    TimesheetSpecification toSpecification(TimesheetFilterRequest filterRequest);
}
