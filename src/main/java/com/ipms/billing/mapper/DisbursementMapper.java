package com.ipms.billing.mapper;


import com.ipms.billing.dto.DisbursementDto;
import com.ipms.billing.model.Disbursement;
import com.ipms.config.storage.model.StorageFile;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface DisbursementMapper {
    Disbursement toDisbursement (DisbursementDto dto);
    DisbursementDto toDisbursementDto (Disbursement disbursement);
    StorageFile toStorageFile(DisbursementDto dto);
    Disbursement toDisbursementFromDto(DisbursementDto dto, @MappingTarget Disbursement disbursement);
}
