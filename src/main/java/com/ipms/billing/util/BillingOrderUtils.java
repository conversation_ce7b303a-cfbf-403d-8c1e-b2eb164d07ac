package com.ipms.billing.util;

import com.ipms.billing.dto.MatterDto;
import org.apache.commons.lang3.StringUtils;

import java.text.MessageFormat;
import java.util.HashMap;
import java.util.Map;

public class BillingOrderUtils {

    private BillingOrderUtils() {}

    public static Map<String, String> getDescription(MatterDto matterDto, String firmTitle, String agentReference, String language) {
        var descriptionMap = new HashMap<String, String>();
        firmTitle = StringUtils.substring(firmTitle, 0, 80);
        agentReference  = StringUtils.substring(agentReference, 0, 80);
        if ("tr".equals(language)) {
            descriptionMap.put("description1", MessageFormat.format("İlgili Şirket: {0}", firmTitle));
            descriptionMap.put("description3", MessageFormat.format("B/R: {0} S/R: {1}",
                    matterDto.getNo(), agentReference));
        } else {
            descriptionMap.put("description1", MessageFormat.format("Concern: {0}", firmTitle));
            descriptionMap.put("description3", MessageFormat.format("O/R: {0} Y/R: {1}",
                    matterDto.getNo(), agentReference));
        }
        descriptionMap.put("description2", StringUtils.substring(matterDto.getName(), 0, 80));
        return descriptionMap;
    }
}
