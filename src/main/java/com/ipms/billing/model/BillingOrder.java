package com.ipms.billing.model;

import com.ipms.billing.enums.BillingOrderStatus;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "billing_order")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class BillingOrder extends VersionedEntity {

    @Column
    private String orderNumber;

    @Column
    private LocalDate orderDate;

    @Column
    private String businessUnit;

    @Column
    private Long issuerId;

    @Column
    private String billingAccountNo;

    @OneToMany(mappedBy="billingOrder", cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private List<BillingOrderDetail> orderDetails;

    @Column
    private String description1;

    @Column
    private String description2;

    @Column
    private String description3;

    @Column
    private String externalOrderNumber;

    @Column
    @Enumerated(value = EnumType.STRING)
    private BillingOrderStatus status;

    @Column
    private String invoiceNumber;

    @Column
    private LocalDate invoiceDate;

    @Column
    private String assignee;
}
