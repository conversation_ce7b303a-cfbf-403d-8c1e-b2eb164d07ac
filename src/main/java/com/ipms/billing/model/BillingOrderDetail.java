package com.ipms.billing.model;

import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "billing_order_detail")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class BillingOrderDetail extends VersionedEntity {

    @Column
    private String expenseCode;

    @Column
    private Long quantity;

    @Column
    private BigDecimal unitPrice;

    @Column
    private BigDecimal discountRate;


    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "billing_order_id")
    private BillingOrder billingOrder;

    @ElementCollection(fetch = FetchType.EAGER)
    @CollectionTable(name = "billing_order_detail_expenses",
            joinColumns = @JoinColumn(name = "billing_order_detail_id", referencedColumnName = "id"))
    @Column(name = "expense_id")
    private List<Long> expenses;
}