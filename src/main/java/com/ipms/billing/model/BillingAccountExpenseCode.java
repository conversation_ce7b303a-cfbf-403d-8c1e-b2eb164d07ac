package com.ipms.billing.model;

import com.ipms.billing.enums.ActivityType;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "billing_account_expense_code")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class BillingAccountExpenseCode extends VersionedEntity {
    @Column(nullable = false)
    private Long billingAccountId;

    @Column(nullable = false)
    private String generalExpenseCode;

    @Column(nullable = false)
    private String specialExpenseCode;

    @Column(nullable = false)
    private BigDecimal unitPrice;

    private String countryCode;

    private String protectionType;

    private ActivityType activityType;
}
