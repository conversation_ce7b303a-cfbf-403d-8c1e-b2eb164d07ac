package com.ipms.billing.model;

import com.fasterxml.jackson.annotation.JsonManagedReference;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "expense", indexes = {
    @Index(name = "idx_expense_activity_id", columnList = "activity_id")
})
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class Expense extends VersionedEntity {
    @Column
    private String expenseCode;

    @Column
    private LocalDate expenseDate;

    @Column
    private String currency;

    @Column(length = 500)
    private String description;

    @Column
    private BigDecimal unitPrice;

    @Column
    private Long quantity;

    @Column(length = 20)
    private String orderNumber;

    @Column
    private LocalDate orderDate;

    @Column
    private Boolean isBillingApproved;

    @Column
    private BigDecimal discountPrice;

    @Column
    private BigDecimal discountRate;

    @Column
    private Integer vat;

    @Column
    private LocalDate invoiceDate;

    @Column
    private String invoiceNumber;

    @ElementCollection
    @CollectionTable(name = "expense_timesheets", joinColumns = @JoinColumn(name = "expense_id"))
    @Column(name = "timesheet")
    private List<Long> timesheets;

    @ElementCollection
    @CollectionTable(name = "expense_disbursements", joinColumns = @JoinColumn(name = "expense_id"))
    @Column(name = "disbursement")
    private List<Long> disbursements;

    @Column
    private String accrualNumber;

    @Column(name = "activity_id")
    private Long activityId;

    @OneToOne(mappedBy = "expense", cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JsonManagedReference
    private Payment payment;
}
