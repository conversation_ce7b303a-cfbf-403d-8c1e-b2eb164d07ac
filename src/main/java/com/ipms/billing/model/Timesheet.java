package com.ipms.billing.model;

import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "time_sheet")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class Timesheet extends VersionedEntity {

    @Column(length = 750)
    private String description;
    @Column
    private Long matterId;
    @Column
    private Long activityId;
    @Column
    private LocalDate date;
    @Column
    private BigDecimal loggedTime;
    @Column
    private BigDecimal billableTime;
    @Column
    private Boolean isIncludeReport;
    @Column
    private Boolean isApproved;
    @Column
    private String expenseCode;
    @Column
    private Boolean includedInCharge;
    @Column
    private String approvedBy;
    @Column
    private LocalDate expenseDate;
    @Column
    private Boolean isBillingApproved;
    @Column
    private String timekeeper;
    @Column
    private Long localAttorneyId;


}
