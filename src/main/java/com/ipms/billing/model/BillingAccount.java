package com.ipms.billing.model;

import com.ipms.billing.enums.BillingDay;
import com.ipms.billing.enums.RiskManagement;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.ColumnDefault;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.math.BigDecimal;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "billing_account")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class BillingAccount extends VersionedEntity {

    @Column(nullable = false, unique = true)
    private String accountName;

    @Column(nullable = false)
    private String accountNo;

    @Column(nullable = false)
    private Long issuerId;

    @Column(nullable = false)
    private String address;

    @Column
    private BigDecimal discountRate;

    @Column(nullable = false)
    private String currency;

    @Column
    private String currencyDisbursement;

    @Column(nullable = false)
    @Enumerated(EnumType.STRING)
    private RiskManagement riskManagement;

    @Column
    private String description;

    @ElementCollection
    @CollectionTable(name = "billing_account_issue", joinColumns = @JoinColumn(name = "billing_account_id"))
    @Column(name = "issue_id")
    private List<Long> issueIds;

    //Details
    @Column
    private BillingDay billingDay;

    @Column
    private BillingDay endOfBillingPeriod ;

    @Column
    private Boolean willBeSentToResponsible;

    @Column
    private Boolean createOrderAutomatically;

    @ElementCollection
    @CollectionTable(name = "billing_account_send_to_email",
            joinColumns = @JoinColumn(name = "billing_account_id", referencedColumnName = "id"))
    @Column(name = "recipient_to")
    private List<String> sendToEmails;

    @ElementCollection
    @CollectionTable(name = "billing_account_copy_to_email",
            joinColumns = @JoinColumn(name = "billing_account_id", referencedColumnName = "id"))
    @Column(name = "recipient_cc")
    private List<String> copyToEmails;

    @Column
    private Boolean willOriginalInvoiceBeSent;

    @Column
    private String originalInvoiceRecipient;

    @Column
    private String billingSystem;

    @Column
    private String attachmentsToBeSent;

    @Column(columnDefinition = "TEXT")
    private String note;

    @Column
    private String languageToBeBilled;
}
