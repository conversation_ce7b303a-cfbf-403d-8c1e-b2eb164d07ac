package com.ipms.billing.model;

import com.ipms.billing.enums.DisbursementType;
import com.ipms.billing.enums.ToBeBilledChoice;
import com.ipms.core.entity.AttachmentEntity;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Index;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "disbursement", indexes = {
    @Index(name = "idx_disbursement_activity_id", columnList = "activity_id")
})
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
@AuditOverride(forClass = AttachmentEntity.class)
public class Disbursement extends AttachmentEntity {

    @Column(name = "disbursement_user")
    private String user;

    @Column
    private LocalDate disbursementDate;

    @Column
    private DisbursementType disbursementType;

    @Column
    private String currency;

    @Column
    private BigDecimal amount;

    @Column(length = 500)
    private String description;

    @Column
    private ToBeBilledChoice toBeBilled;

    @Column
    private String currencyToBeBilled;

    @Column(precision = 19, scale = 8)
    private BigDecimal exchangeRate;

    @Column
    private BigDecimal amountToBeBilled;

    @Column
    private LocalDate expenseDate;

    @Column
    private String expenseCode;

    @Column
    private Boolean isBillingApproved;

    @Column(name = "activity_id")
    private Long activityId;
}
