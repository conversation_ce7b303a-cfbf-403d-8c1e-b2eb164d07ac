package com.ipms.billing.model;

import com.fasterxml.jackson.annotation.JsonBackReference;
import com.ipms.billing.enums.PaymentStatus;
import com.ipms.core.entity.BaseEntity;
import com.ipms.core.entity.DatedEntity;
import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;
import org.hibernate.envers.AuditOverride;
import org.hibernate.envers.Audited;

import javax.persistence.*;
import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "payment")
@Entity
@Where(clause = "is_deleted='0'")
@Audited
@AuditOverride(forClass = BaseEntity.class)
@AuditOverride(forClass = DatedEntity.class)
@AuditOverride(forClass = VersionedEntity.class)
public class Payment extends VersionedEntity {
    @Column
    private Long matterId;

    @Column
    private String accrualNumber;

    @Column
    private Long issuerId;

    @Column
    private BigDecimal expenseAmount;

    @Column
    private String expenseCode;

    @Column
    private LocalDate expenseDate;

    @Column
    @Enumerated(value = EnumType.STRING)
    PaymentStatus status;

    @OneToOne(cascade = {CascadeType.PERSIST,
            CascadeType.DETACH,
            CascadeType.REFRESH,
            CascadeType.REMOVE})
    @JoinColumn(name = "expense_id", referencedColumnName = "id")
    @JsonBackReference
    private Expense expense;
}
