package com.ipms.billing.repository;

import com.ipms.billing.dto.OrderNumberInvoiceNumber;
import com.ipms.billing.model.Expense;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;


@Repository
public interface ExpenseRepository extends PagingAndSortingRepository<Expense, Long>, JpaSpecificationExecutor<Expense> {

    Page<Expense> findByIdInOrderByExpenseDateDesc(List<Long> expenseIds, Pageable pageable);

    List<Expense> findByIdIn (List<Long> ids);

    List<Expense> findByIdInAndOrderNumber (List<Long> ids, String orderNumber);

    List<Expense> findByDisbursementsIn(List<Long> disbursements);

    List<Expense> findByTimesheetsIn(List<Long> timesheets);

    Page<Expense> findByIdInAndOrderDateBeforeOrderByOrderDateDesc(List<Long> ids, LocalDate billingPeriodEnds,
                                                                       Pageable pageable);
    List<Expense> findByOrderNumberIn(List<String> orderNumbers);

    List<Expense> findByOrderNumber(String orderNumber);

    List<OrderNumberInvoiceNumber> getAllByIsDeletedFalse();

    List<Expense> findByIdInAndExpenseDateLessThanEqual(List<Long> ids, LocalDate billingPeriodEnds);

    List<Expense> findByOrderDateIsNullAndIdIn(List<Long> ids);

    List<Expense> findByActivityId(Long activityId);

    List<Expense> findByActivityIdIn(List<Long> activityIds);

}
