package com.ipms.billing.repository;

import com.ipms.billing.enums.ToBeBilledChoice;
import com.ipms.billing.model.Disbursement;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.CrudRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface DisbursementRepository extends CrudRepository<Disbursement, Long>, JpaSpecificationExecutor<Disbursement> {
    Page<Disbursement> findByIdInOrderByDisbursementDateDesc(List<Long> disbursementIds, Pageable pageable);
    List<Disbursement> findByIdIn (List<Long> ids);
    List<Disbursement> findByIdInAndExpenseDateIsNullAndToBeBilled(List<Long> ids, ToBeBilledChoice toBeBilledChoice);

    @Query("SELECT d FROM Disbursement d WHERE d.id IN :disbursementIds " +
            "AND DATE(d.expenseDate) < :billingPeriodEnds " +
            "ORDER BY d.disbursementDate DESC")
    Page<Disbursement> findByIdInAndExpenseDateBeforeOrderByDisbursementDateDesc(List<Long> disbursementIds,
                                                                                 LocalDate billingPeriodEnds,
                                                                                 Pageable pageable);

    List<Disbursement> findByActivityId(Long activityId);
}
