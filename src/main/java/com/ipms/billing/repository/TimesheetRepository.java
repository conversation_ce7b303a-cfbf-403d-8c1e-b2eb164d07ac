package com.ipms.billing.repository;

import com.ipms.billing.model.Timesheet;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.time.LocalDate;
import java.util.List;

@Repository
public interface TimesheetRepository extends PagingAndSortingRepository<Timesheet, Long>, JpaSpecificationExecutor<Timesheet> {
    List<Timesheet> findByCreatedByAndDateBetweenOrderByDateDesc(String username, LocalDate startDate, LocalDate endDate);
    List<Timesheet> findByActivityIdAndDateBetweenOrderByDateDesc(Long activityId, LocalDate startDate, LocalDate endDate);
    Page<Timesheet> findByActivityIdOrderByDateDesc(Long activityId, Pageable pageable);
    List<Timesheet> findByIdIn (List<Long> ids);
    List<Timesheet> findByActivityId(Long activityId);
    List<Timesheet> findByActivityIdNotInAndDateBefore(List<Long> activityIds, LocalDate dateBefore);
    List<Timesheet> findByActivityIdAndExpenseDateIsNull(Long activityId);
    List<Timesheet> findByActivityIdAndExpenseDateIsNotNullAndIncludedInChargeIsTrue(Long activityId);
    List<Timesheet> findByActivityIdInAndExpenseDateIsNullAndDateLessThanEqual(List<Long> activityIds, LocalDate startDate);
    Page<Timesheet> findByActivityIdInAndExpenseDateIsNullAndDateLessThanEqualOrderByDateDesc(List<Long> activityIds, LocalDate startDate, Pageable pageable);
}
