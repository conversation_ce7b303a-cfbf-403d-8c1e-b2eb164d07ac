package com.ipms.billing.repository;

import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.Repository;
import org.springframework.data.repository.query.Param;

import java.math.BigInteger;
import java.util.List;

/**
 * JPA Repository for managing order number sequence operations.
 * 
 * This repository provides elegant, type-safe access to PostgreSQL sequence
 * operations while maintaining the performance characteristics of native queries.
 * 
 * Uses PostgreSQL's sequence functionality to generate atomic, unique order numbers
 * for billing orders, ensuring thread-safety and preventing duplicates.
 */
@org.springframework.stereotype.Repository
public interface OrderNumberSequenceRepository extends Repository<Object, Long> {
    
    /**
     * Gets the next value from the billing order number sequence.
     * 
     * This method uses PostgreSQL's nextval() function to atomically retrieve
     * the next sequence value, ensuring thread-safety and uniqueness.
     * 
     * @return The next sequence value as a Long
     */
    @Query(value = "SELECT nextval('billing_order_number_seq')", nativeQuery = true)
    Long getNextSequenceValue();
    
    /**
     * Gets multiple sequential values from the billing order number sequence.
     * 
     * This method efficiently reserves a range of sequence values in a single
     * database operation, which is optimal for bulk order number generation.
     * The generate_series function creates the specified number of sequence calls.
     * 
     * @param count The number of sequence values to retrieve (must be positive)
     * @return List of sequential sequence values
     */
    @Query(value = "SELECT nextval('billing_order_number_seq') FROM generate_series(1, :count)", 
           nativeQuery = true)
    List<BigInteger> getNextSequenceValues(@Param("count") int count);
    
    /**
     * Gets the current value of the sequence without advancing it.
     * 
     * Useful for debugging and monitoring purposes. This does not consume
     * a sequence value.
     * 
     * @return The current sequence value
     */
    @Query(value = "SELECT currval('billing_order_number_seq')", nativeQuery = true)
    Long getCurrentSequenceValue();
    
    /**
     * Checks if the sequence exists in the database.
     * 
     * @return true if the sequence exists, false otherwise
     */
    @Query(value = "SELECT EXISTS(SELECT 1 FROM information_schema.sequences WHERE sequence_name = 'billing_order_number_seq')", 
           nativeQuery = true)
    boolean sequenceExists();
}