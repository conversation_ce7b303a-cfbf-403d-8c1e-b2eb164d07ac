package com.ipms.billing.repository;

import com.ipms.billing.model.BillingOrder;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.jpa.repository.Query;

import java.util.List;
import java.util.Optional;

public interface BillingOrderRepository extends JpaRepository<BillingOrder, Long>, JpaSpecificationExecutor<BillingOrder> {
    @Query(value = "SELECT * FROM billing_order o ORDER BY o.id DESC LIMIT 1", nativeQuery = true)
    Optional<BillingOrder> findFirstByOrderByIdDesc();
    List<BillingOrder> findByIdIn(List<Long> ids);
    Optional<BillingOrder> findByExternalOrderNumberAndBillingAccountNo(String externalOrderNumber, String billingAccountNo);
    Optional<BillingOrder> findByOrderNumber(String orderNumber);
}
