package com.ipms.billing.repository;

import com.ipms.billing.model.BillingAccount;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;
import java.util.Optional;

public interface BillingAccountRepository
        extends JpaRepository<BillingAccount, Long>, JpaSpecificationExecutor<BillingAccount> {

    Optional<BillingAccount> findByAccountNoAndIssuerId(String accountNo, Long issuerId);

    List<BillingAccount> findByAccountNoIn(List<String> accountNos);

    List<BillingAccount> findByIdIn(List<Long> ids);

    Optional<BillingAccount> findByAccountName(String accountName);
}
