package com.ipms.billing.repository;

import com.ipms.billing.enums.PaymentStatus;
import com.ipms.billing.model.Payment;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;
import org.springframework.data.repository.PagingAndSortingRepository;
import org.springframework.stereotype.Repository;

import java.util.List;
import java.util.Optional;

@Repository
public interface PaymentRepository extends PagingAndSortingRepository<Payment, Long>,
        JpaSpecificationExecutor<Payment> {
    Optional<Payment> findByAccrualNumberContainsIgnoreCase(String accrualNumber);
    List<Payment> findByStatusIn(List<PaymentStatus> statuses);
}
