package com.ipms.billing.repository;

import com.ipms.billing.model.BillingAccountExpenseCode;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.JpaSpecificationExecutor;

import java.util.List;

public interface BillingAccountExpenseCodeRepository
        extends JpaRepository<BillingAccountExpenseCode, Long>, JpaSpecificationExecutor<BillingAccountExpenseCode> {

    List<BillingAccountExpenseCode> findByBillingAccountIdIn(List<Long> billingAccountIds);
}
