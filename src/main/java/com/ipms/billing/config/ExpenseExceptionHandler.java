package com.ipms.billing.config;

import com.ipms.billing.exception.ExpenseNotDivisibleException;
import com.ipms.core.exception.BaseException;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.config.GlobalExceptionHandler;
import com.ipms.core.i18n.service.II18nMessageService;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Locale;

@RestControllerAdvice
public class ExpenseExceptionHandler extends GlobalExceptionHandler {
    public ExpenseExceptionHandler(II18nMessageService ii18nMessageService) {
        super(ii18nMessageService);
    }

    @ResponseStatus(HttpStatus.INTERNAL_SERVER_ERROR)
    @ExceptionHandler({
            ExpenseNotDivisibleException.class
    })
    public BaseResponse<Object> handleMailExceptions(BaseException exception, Locale locale) {
        return buildResponseCode(exception, locale);
    }
}