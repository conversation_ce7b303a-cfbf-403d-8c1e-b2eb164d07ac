package com.ipms.billing.converter;

import com.ipms.billing.enums.DisbursementType;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class DisbursementTypeConverter implements AttributeConverter<DisbursementType, String> {

    @Override
    public String convertToDatabaseColumn(DisbursementType mediaType) {
        return mediaType == null ? null : mediaType.getValue();
    }

    @Override
    public DisbursementType convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : DisbursementType.getEnum(s);
    }
}
