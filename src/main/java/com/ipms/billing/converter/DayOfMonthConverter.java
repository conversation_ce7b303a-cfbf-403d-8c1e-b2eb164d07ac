package com.ipms.billing.converter;

import com.ipms.billing.enums.BillingDay;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class DayOfMonthConverter implements AttributeConverter<BillingDay, Integer> {
    @Override
    public Integer convertToDatabaseColumn(BillingDay billingDay) {
        return billingDay == null ? null : billingDay.getValue();
    }

    @Override
    public BillingDay convertToEntityAttribute(Integer value) {
        return  value == null ? null : BillingDay.fromValue(value);
    }
}
