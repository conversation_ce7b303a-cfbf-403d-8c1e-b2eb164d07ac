package com.ipms.billing.converter;

import com.ipms.billing.enums.ToBeBilledChoice;

import javax.persistence.AttributeConverter;
import javax.persistence.Converter;

@Converter(autoApply = true)
public class ToBeBilledChoiceConverter implements AttributeConverter<ToBeBilledChoice, String> {

    @Override
    public String convertToDatabaseColumn(ToBeBilledChoice toBeBilledChoice) {
        return toBeBilledChoice == null ? null : toBeBilledChoice.getValue();
    }

    @Override
    public ToBeBilledChoice convertToEntityAttribute(String s) {
        return  s == null || s.isEmpty() ? null : ToBeBilledChoice.getEnum(s);
    }
}