package com.ipms.billing.controller;

import com.ipms.billing.dto.*;
import com.ipms.billing.dto.groups.UpdateTimesheetGroup;
import com.ipms.billing.service.TimesheetService;
import com.ipms.core.entity.Revision;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("timesheets")
@Tag(name = "timesheet", description = "This endpoint contains timesheet APIs ")
public class TimesheetController {

    private final TimesheetService service;

    @PostMapping
    public BaseResponse<TimesheetDto> save(@Valid @RequestBody TimesheetDto dto) {
        return BaseResponse.<TimesheetDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @GetMapping("/{timesheetId}")
    public BaseResponse<TimesheetDto> get(@PathVariable Long timesheetId) {
        return BaseResponse.<TimesheetDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getById(timesheetId))
                .build();
    }

    @GetMapping("/{startDate}/{endDate}")
    public BaseResponse<TimesheetSummaryDto> getAllByBetweenDates(@PathVariable(value = "startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                                                                  @PathVariable(value = "endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return BaseResponse.<TimesheetSummaryDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAllByDates(startDate, endDate))
                .build();
    }

    @GetMapping("by-activity/{activityId}/{startDate}/{endDate}")
    public BaseResponse<TimesheetTotalTimeDto> getAllByBetweenDates(@PathVariable Long activityId,
                                                                    @PathVariable(value = "startDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate,
                                                                    @PathVariable(value = "endDate") @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return BaseResponse.<TimesheetTotalTimeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getAllByDates(activityId, startDate, endDate))
                .build();
    }

    @GetMapping("/{activityId}/{page}/{size}")
    public BaseResponse<TimesheetPageDto> getPageByActivity(@PathVariable Long activityId,
                                                            @PathVariable int page,
                                                            @PathVariable int size) {
        return BaseResponse.<TimesheetPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getByActivity(activityId, page, size))
                .build();
    }

    @GetMapping("/timesheets/{page}/{size}")
    public BaseResponse<TimesheetPageDto> getTimesheetPage(@PathVariable int page,
                                                           @PathVariable int size,
                                                           TimesheetFilterRequest filterRequest){
        return BaseResponse.<TimesheetPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getPage(filterRequest, page, size))
                .build();
    }

    @GetMapping("/timesheets")
    public BaseResponse<List<TimesheetDto>> getAllTimesheets(TimesheetFilterRequest filterRequest){
        return BaseResponse.<List<TimesheetDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(filterRequest))
                .build();
    }

    @GetMapping("/unbilled/{activityId}")
    public BaseResponse<List<TimesheetDto>> getUnbilledTimesheets(@PathVariable Long activityId) {
        return BaseResponse.<List<TimesheetDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getUnbilledTimesheetsByActivityId(activityId))
                .build();
    }

    @GetMapping("/ids/{ids}")
    public BaseResponse<List<TimesheetDto>> getTimesheetList(@PathVariable List<Long> ids){
        return BaseResponse.<List<TimesheetDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByIdIn(ids))
                .build();
    }

    @PutMapping
    public BaseResponse<TimesheetDto> update(@RequestBody @Validated(UpdateTimesheetGroup.class) TimesheetDto dto) {
        return BaseResponse.<TimesheetDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(dto))
                .build();
    }

    @PutMapping("/approve/bulk")
    public BaseResponse<List<TimesheetDto>> approveBulk(@RequestBody ApproveBulkRequest dto) {
        return BaseResponse.<List<TimesheetDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.approveBulk(dto.getIds()))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/times/{activityId}")
    public BaseResponse<TimesheetTotalTimeDto> getTotalTimesByActivity(@PathVariable Long activityId){
        return BaseResponse.<TimesheetTotalTimeDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getTimesByActivity(activityId))
                .build();
    }

    @GetMapping("/times/{activityId}/{startDate}")
    public BaseResponse<TimesheetTotalTimeDto> getTotalTimesByActivityAndStartDate(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate) {
        return BaseResponse.<TimesheetTotalTimeDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getTimesByActivityAndStartDate(activityId, startDate))
                .build();
    }

    @GetMapping("/times-fds/{activityId}/{startDate}")
    public BaseResponse<TimesheetTotalTimeDto> getTotalTimesByActivityAndStartDateForFds(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate startDate) {
        return BaseResponse.<TimesheetTotalTimeDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getTimesByActivityAndStartDateForFds(activityId, startDate))
                .build();
    }

    @GetMapping("/times-by-end-date/{activityId}/{endDate}")
    public BaseResponse<TimesheetTotalTimeDto> getTotalTimesByActivityAndEndDate(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate endDate) {
        return BaseResponse.<TimesheetTotalTimeDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getTimesByActivityAndEndDate(activityId, endDate))
                .build();
    }

    @GetMapping("/revisions/{id}")
    public BaseResponse<List<Revision>> getRevisions(@PathVariable Long id) {
        return BaseResponse.<List<Revision>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getRevisions(id))
                .build();
    }

    @GetMapping("/revisions/{id}/{field}")
    public BaseResponse<List<Revision>> getRevisionsByField(@PathVariable Long id, @PathVariable String field) {
        return BaseResponse.<List<Revision>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getRevisionsByField(id, field))
                .build();
    }

    @PutMapping("/calculate-included-in-charge")
    public BaseResponse<Void> calculateIncludedInCharge(@RequestBody TimesheetIncludedInChargeRequest includedInChargeRequest) {
        service.calculateIncludedInCharge(includedInChargeRequest.getActivityId(),
                includedInChargeRequest.getPackageStartDate(),
                includedInChargeRequest.getTsicIncludedCharge());
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }


    @GetMapping("/not-invoiced-times/{activityId}")
    public BaseResponse<TimesheetTotalTimeDto> getUnbilledTimesByActivity(@PathVariable Long activityId){
        return BaseResponse.<TimesheetTotalTimeDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getUnbilledTimesByActivity(activityId))
                .build();
    }

    @GetMapping(path = "/not-invoiced-times/{activityId}/{billingPeriodEnds}", params = "others=false")
    public BaseResponse<TimesheetTotalTimeDto> getUnbilledTimes(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        var timesheetTotalTimeDto = service.getUnbilledTimes(activityId, billingPeriodEnds);
        return BaseResponse.<TimesheetTotalTimeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(timesheetTotalTimeDto)
                .build();
    }

    @GetMapping(path = "/not-invoiced/{activityId}/{billingPeriodEnds}/{page}/{size}", params = "others=false")
    public BaseResponse<TimesheetPageDto> getUnbilled(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds,
            @PathVariable int page,
            @PathVariable int size) {
        var timesheetPageDto = service.getUnbilledTimesheetsPage(activityId, billingPeriodEnds, page, size);
        return BaseResponse.<TimesheetPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(timesheetPageDto)
                .build();
    }

    @GetMapping(path = "/not-invoiced-times/{activityId}/{billingPeriodEnds}", params = "others=true")
    public BaseResponse<TimesheetTotalTimeDto> getUnbilledOthers(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        var timesheetTotalTimeDto = service.getOtherUnbilledTimes(activityId, billingPeriodEnds);
        return BaseResponse.<TimesheetTotalTimeDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(timesheetTotalTimeDto)
                .build();
    }

    @GetMapping(path = "/not-invoiced/{activityId}/{billingPeriodEnds}/{page}/{size}", params = "others=true")
    public BaseResponse<TimesheetPageDto> getUnbilledOthers(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds,
            @PathVariable int page,
            @PathVariable int size) {
        var timesheetPageDto = service.getOtherUnbilledPage(activityId, billingPeriodEnds, page, size);
        return BaseResponse.<TimesheetPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(timesheetPageDto)
                .build();
    }

    @PostMapping("/approve-by-system/{beforeDate}")
    public BaseResponse<Void> approveBySystem(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate beforeDate) {
        service.approveBySystem(beforeDate);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @PutMapping("/change-activity")
    public BaseResponse<Void> changeActivity(@RequestBody @Valid TimesheetChangeActivityRequest changeActivityRequest) {
        service.changeActivity(changeActivityRequest);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @PutMapping("/billing-approve/bulk")
    public BaseResponse<List<TimesheetDto>> billingApproveBulk(@RequestBody ApproveBulkRequest dto) {
        return BaseResponse.<List<TimesheetDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.approveBillingBulk(dto.getIds()))
                .build();
    }

    @PutMapping("/cancel-billing-approve/bulk")
    public BaseResponse<List<TimesheetDto>> cancelBillingApproveBulk(@RequestBody ApproveBulkRequest dto) {
        return BaseResponse.<List<TimesheetDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.cancelApprovelBillingBulk(dto.getIds()))
                .build();
    }

    @PutMapping("/approve-billing")
    public BaseResponse<Void> approveBilling(@RequestBody ApproveBillingRequest approveBillingRequest) {
        service.approveBilling(approveBillingRequest);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping(path = "/is-approvable/{activityId}/{billingPeriodEnds}")
    public BaseResponse<Boolean> isBillingApprovableTimesheets(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        var result = service.isBillingApprovableTimesheets(activityId, billingPeriodEnds);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(result)
                .build();
    }

    @GetMapping(path = "/has-billable/{activityId}/{billingPeriodEnds}")
    public BaseResponse<Boolean> hasBillable(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        var result = service.hasBillableTimesheet(activityId, billingPeriodEnds);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(result)
                .build();
    }

    @PutMapping(path = "/billing-cancellation/{activityId}/{billingPeriodEnds}")
    public BaseResponse<Void> updateTimesheetsForBillingCancellation(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        service.updateTimesheetsForBillingCancellation(activityId, billingPeriodEnds);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @PutMapping("/undo-billing-approve/{activityId}/{billingPeriodEnds}")
    public BaseResponse<Void> undoBillingApprovalTimesheets(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        service.undoBillingApprovalTimesheets(activityId, billingPeriodEnds);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @PostMapping("/split-timesheet/{timesheetId}/{billingPeriodEnds}")
    public BaseResponse<Boolean> splitTimesheet(
            @PathVariable Long timesheetId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        var result = service.splitTimesheet(timesheetId, billingPeriodEnds);

        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(result)
                .build();
    }

    @GetMapping("/has-unapproved/{activityId}/{billingPeriodEnds}")
    public BaseResponse<Boolean> hasUnapproved(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        var result = service.hasUnapproved(activityId, billingPeriodEnds);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(result)
                .build();
    }

    @PutMapping("/cancel-billing-order/{timesheetIds}")
    public BaseResponse<Void> cancelBillingOrder(@PathVariable List<Long> timesheetIds) {
        service.cancelBillingOrder(timesheetIds);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }
    
    @PutMapping("/included-in-charge/bulk")
    public BaseResponse<List<TimesheetDto>> updateIncludedInChargeBulk(
            @Valid @RequestBody IncludedInChargeBulkRequest request) {
        return BaseResponse.<List<TimesheetDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.updateIncludedInChargeBulk(request.getIds(), request.getIncludedInCharge()))
                .build();
    }
}
