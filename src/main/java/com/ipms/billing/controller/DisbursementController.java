package com.ipms.billing.controller;

import com.ipms.billing.dto.*;
import com.ipms.billing.service.DisbursementService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("disbursement")
@Tag(name = "disbursement", description = "This endpoints contains disbursement APIs")
public class DisbursementController {

    private final DisbursementService service;

    @PostMapping
    public BaseResponse<DisbursementDto> save(@Valid @RequestBody DisbursementDto dto) {
        return BaseResponse.<DisbursementDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<DisbursementDto> update(@PathVariable Long id, @Valid @RequestBody DisbursementDto dto) {
        return BaseResponse.<DisbursementDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(id, dto))
                .build();
    }

    @GetMapping("/to-be-billed/{page}/{size}")
    public BaseResponse<DisbursementPageDto> getToBeBilledDisbursements(
            @PathVariable int page, @PathVariable int size,
            @Validated(DisbursementFilterRequest.ToBeBilled.class) DisbursementFilterRequest filterRequest) {
        return BaseResponse.<DisbursementPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getToBeBilledDisbursements(filterRequest, page, size))
                .build();
    }

    @GetMapping("/billed/{page}/{size}")
    public BaseResponse<DisbursementPageDto> getBilledDisbursements(
            @PathVariable int page, @PathVariable int size,
            @Validated(DisbursementFilterRequest.ToBeBilled.class) DisbursementFilterRequest filterRequest) {
        return BaseResponse.<DisbursementPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getBilledDisbursements(filterRequest, page, size))
                .build();
    }

    @GetMapping("/{disbursementIds}/{page}/{size}")
    public BaseResponse<DisbursementPageDto> getByIds(@PathVariable int page,
                                                      @PathVariable int size,
                                                      @PathVariable List<Long> disbursementIds) {
        return BaseResponse.<DisbursementPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByIds(disbursementIds, page, size))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/ids/{ids}")
    public BaseResponse<List<DisbursementDto>> getByIds(@PathVariable List<Long> ids) {
        return BaseResponse.<List<DisbursementDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByIdIn(ids))
                .build();
    }

    @PutMapping("/billing-approve/bulk")
    public BaseResponse<List<DisbursementDto>> billingApproveBulk(@RequestBody IdsRequest idsRequest) {
        return BaseResponse.<List<DisbursementDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.approveBillingBulk(idsRequest.getIds()))
                .build();
    }

    @PutMapping("/cancel-billing-approve/bulk")
    public BaseResponse<List<DisbursementDto>> cancelBillingApproveBulk(@RequestBody IdsRequest idsRequest) {
        return BaseResponse.<List<DisbursementDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.cancelApprovelBillingBulk(idsRequest.getIds()))
                .build();
    }

    @PutMapping("/approve-billing")
    public BaseResponse<Void> approveBilling(@RequestBody ApproveBillingRequest approveBillingRequest) {
        service.approveBilling(approveBillingRequest);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping(path = "/is-approvable/{activityId}/{billingPeriodEnds}")
    public BaseResponse<Boolean> isBillingApprovableDisbursements(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        var result = service.isBillingApprovableDisbursements(activityId, billingPeriodEnds);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(result)
                .build();
    }

    @GetMapping(path = "/is-cancellable/{activityId}")
    public BaseResponse<Boolean> isBillingCancellableDisbursements(
            @PathVariable Long activityId) {
        var result = service.isBillingCancellableDisbursements(activityId);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(result)
                .build();
    }

    @GetMapping("/has-billable")
    public BaseResponse<Boolean> hasBillableDisbursementOrExpense(
            @RequestParam(required = false) List<Long> disbursementIds,
            @RequestParam(required = false) List<Long> expenseIds) {
        var isBillable = service.hasBillable(disbursementIds, expenseIds);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(isBillable)
                .build();
    }

    @PutMapping("/undo-billing-approve/{disbursementIds}/{billingPeriodEnds}")
    public BaseResponse<Void> undoBillingApproval(
            @PathVariable List<Long> disbursementIds,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        service.undoBillingApproval(disbursementIds, billingPeriodEnds);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/more-than-billing-amount")
    public BaseResponse<Boolean> moreThanBillingAmount(@RequestParam(required = false) List<Long> disbursementIds,
                                                       @RequestParam(required = false) List<Long> expenseIds) {
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.isTotalAmountMoreThanBillingAmount(disbursementIds, expenseIds))
                .build();
    }

    @GetMapping("/more-than-zero")
    public BaseResponse<Boolean> moreThanZero(@RequestParam(required = false) List<Long> disbursementIds,
                                              @RequestParam(required = false) List<Long> expenseIds) {
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.isTotalAmountMoreThanZero(disbursementIds, expenseIds))
                .build();
    }

    @PutMapping(path = "/billing-cancellation/{disbursementIds}/{billingPeriodEnds}")
    public BaseResponse<Void> updateTimesheetsForBillingCancellation(
            @PathVariable List<Long> disbursementIds,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        service.updateDisbursementsForBillingCancellation(disbursementIds, billingPeriodEnds);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }
}
