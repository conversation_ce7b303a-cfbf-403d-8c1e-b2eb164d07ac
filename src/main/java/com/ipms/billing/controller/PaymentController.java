package com.ipms.billing.controller;

import com.ipms.billing.dto.*;
import com.ipms.billing.service.PaymentService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import javax.validation.constraints.Size;

@RequiredArgsConstructor
@RestController
@RequestMapping("payment")
@Tag(name = "payment", description = "This endpoints contains payment APIs")
public class PaymentController {

    private final PaymentService service;

    @PostMapping
    public BaseResponse<PaymentDto> save(@Valid @RequestBody PaymentDto dto) {
        return BaseResponse.<PaymentDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @GetMapping("/{accrualNumber}")
    public BaseResponse<PaymentDto> getByName(@PathVariable @Size(min = 3) String accrualNumber) {
        return BaseResponse.<PaymentDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getByAccrualNumber(accrualNumber))
                .build();
    }

    @GetMapping("/{page}/{size}")
    public BaseResponse<PaymentPageDto> getPaymentList(@PathVariable int page,
                                                       @PathVariable int size,
                                                       PaymentFilterRequest filterRequest){
        return BaseResponse.<PaymentPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(filterRequest, page, size))
                .build();
    }

    @GetMapping("/grouped/{page}/{size}")
    public BaseResponse<PaymentGroupPageDto> getPaymentGroupedList(@PathVariable int page,
                                                       @PathVariable int size,
                                                       PaymentFilterRequest filterRequest){
        return BaseResponse.<PaymentGroupPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAllGrouped(filterRequest, page, size))
                .build();
    }

    @PutMapping("/{id}/{version}")
    public BaseResponse<PaymentDto> update(@Valid @RequestBody PaymentDto dto,
                                           @PathVariable Long id,
                                           @PathVariable Long version) {
        return BaseResponse.<PaymentDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(dto, id, version))
                .build();
    }

    @PutMapping("/bulk-update-status")
    public BaseResponse<Void> bulkUpdatePaymentStatus(@RequestBody BulkPaymentStatusUpdateRequest request) {
        service.bulkUpdateStatus(request.getPaymentIds(), request.getNewStatus());
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }


    @DeleteMapping("/auto-delete-unapproved")
    public BaseResponse<Boolean> deleteUnapprovedPayments() {
        service.deleteUnapprovedPayments();
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }
}
