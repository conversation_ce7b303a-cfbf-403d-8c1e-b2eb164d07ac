package com.ipms.billing.controller;

import com.ipms.billing.dto.BillingOrderCreateDto;
import com.ipms.billing.dto.BillingOrderDto;
import com.ipms.billing.dto.BillingOrderFilterRequest;
import com.ipms.billing.dto.BillingOrderPageDto;
import com.ipms.billing.service.BillingOrderService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/billing-orders")
public class BillingOrderController {

    private final BillingOrderService service;

    @GetMapping("/{id}")
    public BaseResponse<BillingOrderDto> getById(@PathVariable Long id) {
        var dto = service.getById(id);
        return BaseResponse.<BillingOrderDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @GetMapping("/ids/{ids}")
    public BaseResponse<List<BillingOrderDto>> getBillingOrderList(@PathVariable List<Long> ids){
        return BaseResponse.<List<BillingOrderDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByIdIn(ids))
                .build();
    }

    @PostMapping
    public BaseResponse<BillingOrderDto> save(@Valid @RequestBody BillingOrderDto billingOrderDto) {
        var dto = service.save(billingOrderDto);
        return BaseResponse.<BillingOrderDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @PostMapping("/with-expenses")
    public BaseResponse<BillingOrderDto> saveWithExpenses(@Valid @RequestBody BillingOrderCreateDto billingOrderDto) {
        var dto = service.saveWithExpenses(billingOrderDto);
        return BaseResponse.<BillingOrderDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @PostMapping("/with-expenses/bulk")
    public BaseResponse<List<BillingOrderDto>> saveBulkWithExpenses(
            @Valid @RequestBody List<BillingOrderCreateDto> billingOrderDtos) {
        var dtos = service.saveBulkWithExpenses(billingOrderDtos);
        return BaseResponse.<List<BillingOrderDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dtos)
                .build();
    }

    @DeleteMapping("/{id}")
    public BaseResponse<Void> delete(@PathVariable Long id) {
        service.delete(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @GetMapping("/{page}/{size}")
    public BaseResponse<BillingOrderPageDto> getPageDto(@PathVariable int page,
                                                        @PathVariable int size,
                                                        BillingOrderFilterRequest filterRequest) {
        return BaseResponse.<BillingOrderPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getPageDto(filterRequest, page, size))
                .build();
    }

    @PutMapping("/cancel/{id}")
    public BaseResponse<Void> cancel(@PathVariable Long id) {
        service.cancel(id);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }
}
