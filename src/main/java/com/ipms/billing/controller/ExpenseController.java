package com.ipms.billing.controller;

import com.ipms.billing.dto.*;
import com.ipms.billing.service.ExpenseService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import io.swagger.v3.oas.annotations.tags.Tag;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

@RequiredArgsConstructor
@RestController
@RequestMapping("expense")
@Tag(name = "expense", description = "This endpoints contains expense APIs")
public class ExpenseController {

    private final ExpenseService service;

    @GetMapping("/expenses/{page}/{size}")
    public BaseResponse<ExpensePageDto> getExpenseList(@PathVariable int page,
                                                       @PathVariable int size,
                                                       ExpenseFilterRequest filterRequest){
        return BaseResponse.<ExpensePageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(filterRequest, page, size))
                .build();
    }


    @PostMapping
    public BaseResponse<ExpenseDto> save(@Valid @RequestBody ExpenseDto dto) {
        return BaseResponse.<ExpenseDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.save(dto))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<ExpenseDto> update(@PathVariable Long id, @Valid @RequestBody ExpenseDto dto) {
        return BaseResponse.<ExpenseDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.update(id, dto))
                .build();
    }

    @GetMapping("/{expenseIds}/{page}/{size}")
    public BaseResponse<ExpensePageDto> getByIds(@PathVariable int page,
                                                 @PathVariable int size,
                                                 @PathVariable List<Long> expenseIds) {
        return BaseResponse.<ExpensePageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByIds(expenseIds, page, size))
                .build();
    }

    @GetMapping("/to-be-billed/{page}/{size}")
    public BaseResponse<ExpensePageDto> getToBeBilledExpenses(
            @PathVariable int page, @PathVariable int size,
            @Validated(ExpenseFilterRequest.ToBeBilled.class) ExpenseFilterRequest filterRequest) {
        return BaseResponse.<ExpensePageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getToBeBilledExpensePage(filterRequest, page, size))
                .build();
    }

    @GetMapping("/billed/{page}/{size}")
    public BaseResponse<ExpensePageDto> getBilledExpenses(
            @PathVariable int page, @PathVariable int size,
            @Validated(ExpenseFilterRequest.ToBeBilled.class) ExpenseFilterRequest filterRequest) {
        return BaseResponse.<ExpensePageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getBilledExpenses(filterRequest, page, size))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @DeleteMapping("/{ids}")
    public BaseResponse<Void> deleteBulk(@PathVariable List<Long> ids) {
        service.deleteBulk(ids);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @GetMapping("/ids/{ids}")
    public BaseResponse<List<ExpenseDto>> getByIds(@PathVariable List<Long> ids) {
        return BaseResponse.<List<ExpenseDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByIdIn(ids))
                .build();
    }

    @PutMapping("/billing-approve/bulk")
    public BaseResponse<List<ExpenseDto>> billingApproveBulk(@RequestBody IdsRequest idsRequest) {
        return BaseResponse.<List<ExpenseDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.approveBillingBulk(idsRequest.getIds()))
                .build();
    }

    @PutMapping("/billing-discount")
    public BaseResponse<Void> applyBillingDiscount(@RequestBody DiscountRequest discountRequest) {
        service.applyBillingDiscount(discountRequest);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

    @PutMapping("/cancel-billing-approve/bulk")
    public BaseResponse<List<ExpenseDto>> cancelBillingApproveBulk(@RequestBody IdsRequest idsRequest) {
        return BaseResponse.<List<ExpenseDto>>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.cancelApprovelBillingBulk(idsRequest.getIds()))
                .build();
    }

    @GetMapping(path = "/is-approvable/{activityId}/{billingPeriodEnds}")
    public BaseResponse<Boolean> isBillingApprovableExpenses(
            @PathVariable Long activityId,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        var result = service.isBillingApprovableExpenses(activityId, billingPeriodEnds);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(result)
                .build();
    }

    @GetMapping(path = "/is-cancellable/{activityId}")
    public BaseResponse<Boolean> isBillingCancellableExpenses(
            @PathVariable Long activityId) {
        var result = service.isBillingCancellableExpenses(activityId);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(result)
                .build();
    }

    @GetMapping("/order-creation/{assignee}/{page}/{size}")
    public BaseResponse<ExpenseSummaryPageDto> getOrderCreationExpenses(@PathVariable String assignee,
                                                                        @PathVariable int page,
                                                                        @PathVariable int size) {
        return BaseResponse.<ExpenseSummaryPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getOrderCreationExpenses(assignee, page, size))
                .build();
    }

    @DeleteMapping("/by-timesheets/{timesheetIds}")
    public BaseResponse<Void> deleteByTimesheetIds(@PathVariable List<Long> timesheetIds) {
        service.deleteByTimesheetIds(timesheetIds);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @GetMapping("/by-order/{orderNo}")
    public BaseResponse<List<ExpenseDto>> getListByOrderNo(@PathVariable String orderNo) {
        return BaseResponse.<List<ExpenseDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getExpensesByOrderNo(orderNo))
                .build();
    }

    @GetMapping("/order-number-list")
    public BaseResponse<List<String>> getOrderNumbersList() {
        return BaseResponse.<List<String>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getOrderNumbersList())
                .build();
    }

    @GetMapping("/invoice-number-list")
    public BaseResponse<List<String>> getInvoiceNumbersList() {
        return BaseResponse.<List<String>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getInvoiceNumbersList())
                .build();
    }

    @GetMapping("/is-invoice-completed/{billingPeriodEnds}/{expenseIds}")
    public BaseResponse<Boolean> isInvoiceCompleted(
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds,
            @PathVariable List<Long> expenseIds) {
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.isInvoiceCompleted(billingPeriodEnds, expenseIds))
                .build();
    }

    @GetMapping("/billing-discount/total-amount/{ids}")
    public BaseResponse<TotalAmount> getTotalAmountForDiscount(@PathVariable List<Long> ids) {
        return BaseResponse.<TotalAmount>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(service.getTotalAmountForDiscount(ids))
                .build();
    }

    @PutMapping("/clear-discount-rate/{expenseIds}/{billingPeriodEnds}")
    public BaseResponse<Void> clearDiscountRate(@PathVariable List<Long> expenseIds,
                                                @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        service.clearDiscountRate(expenseIds, billingPeriodEnds);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }

}
