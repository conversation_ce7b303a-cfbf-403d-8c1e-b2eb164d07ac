package com.ipms.billing.controller;

import com.ipms.billing.dto.BillableBillingAccount;
import com.ipms.billing.dto.BillingAccountDto;
import com.ipms.billing.dto.BillingAccountFilterRequest;
import com.ipms.billing.dto.BillingAccountPageDto;
import com.ipms.billing.service.BillingAccountService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import javax.validation.Valid;
import java.time.LocalDate;
import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/billing-accounts")
public class BillingAccountController {
    private final BillingAccountService service;

    @PostMapping
    public BaseResponse<BillingAccountDto> save(@Valid @RequestBody BillingAccountDto billingAccountDto) {
        var dto = service.save(billingAccountDto);
        return BaseResponse.<BillingAccountDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<BillingAccountDto> update(
            @PathVariable Long id,
            @Validated(BillingAccountDto.OnUpdate.class) @RequestBody BillingAccountDto billingAccountDto) {
        var dto = service.update(id, billingAccountDto);
        return BaseResponse.<BillingAccountDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .build();
    }

    @GetMapping("/{id}")
    public BaseResponse<BillingAccountDto> getById(@PathVariable Long id) {
        var dto = service.getById(id);
        return BaseResponse.<BillingAccountDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @GetMapping("/ids/{ids}")
    public BaseResponse<List<BillingAccountDto>> getBillingAccountList(@PathVariable List<Long> ids){
        return BaseResponse.<List<BillingAccountDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getByIdIn(ids))
                .build();
    }

    @GetMapping
    public BaseResponse<List<BillingAccountDto>> getAll(BillingAccountFilterRequest filterRequest) {
        var dtoList = service.getAll(filterRequest);
        return BaseResponse.<List<BillingAccountDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dtoList)
                .build();
    }

    @GetMapping("/{page}/{size}")
    public BaseResponse<BillingAccountPageDto> getAllPageable( @PathVariable int page,
                                                               @PathVariable int size,
                                                               BillingAccountFilterRequest filterRequest) {
        var dto = service.getAllPageable(filterRequest, page, size);
        return BaseResponse.<BillingAccountPageDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }

    @GetMapping("/is-billable")
    public BaseResponse<BillableBillingAccount> isBillable(
            @RequestParam Long id,
            @RequestParam @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) List<LocalDate> favorableDates) {
        return BaseResponse.<BillableBillingAccount>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.isBillable(id, favorableDates))
                .build();
    }

    @PostMapping("/{id}/issue/{issueId}")
    public BaseResponse<BillingAccountDto> addIssue(@PathVariable Long id, @PathVariable Long issueId) {
        var dto = service.addIssue(id, issueId);
        return BaseResponse.<BillingAccountDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(dto)
                .build();
    }
}
