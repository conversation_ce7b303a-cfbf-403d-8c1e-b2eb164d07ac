package com.ipms.billing.controller;

import com.ipms.billing.dto.BillingAccountExpenseCodeDto;
import com.ipms.billing.dto.BillingAccountExpenseCodeFilterRequest;
import com.ipms.billing.service.BillingAccountExpenseCodeService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.web.bind.annotation.*;

import java.util.List;

@RestController
@RequiredArgsConstructor
@RequestMapping("/billing-account-expense-code")
public class BillingAccountExpenseCodeController {
    private final BillingAccountExpenseCodeService service;

    @GetMapping("/")
    public BaseResponse<List<BillingAccountExpenseCodeDto>> getAll(
            BillingAccountExpenseCodeFilterRequest filterRequest) {
        return BaseResponse.<List<BillingAccountExpenseCodeDto>>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.getAll(filterRequest))
                .build();
    }

    @PostMapping
    public BaseResponse<BillingAccountExpenseCodeDto> save(
            @RequestBody BillingAccountExpenseCodeDto billingAccountExpenseCodeDto) {
        return BaseResponse.<BillingAccountExpenseCodeDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.save(billingAccountExpenseCodeDto))
                .build();
    }

    @PutMapping("/{id}")
    public BaseResponse<BillingAccountExpenseCodeDto> update(
            @PathVariable Long id,
            @RequestBody BillingAccountExpenseCodeDto billingAccountExpenseCodeDto) {
        billingAccountExpenseCodeDto.setId(id);
        return BaseResponse.<BillingAccountExpenseCodeDto>builder()
                .code(ResponseCode.SUCCESS)
                .payload(service.update(billingAccountExpenseCodeDto))
                .build();
    }

    @DeleteMapping("/{id}/{version}")
    public BaseResponse<Void> delete(@PathVariable Long id, @PathVariable Long version) {
        service.delete(id, version);
        return BaseResponse.<Void>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .build();
    }
}
