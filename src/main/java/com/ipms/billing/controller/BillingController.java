package com.ipms.billing.controller;

import com.ipms.billing.service.BillingService;
import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import lombok.RequiredArgsConstructor;
import org.springframework.format.annotation.DateTimeFormat;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.RestController;

import java.time.LocalDate;
import java.util.List;

@RestController
@RequiredArgsConstructor
public class BillingController {
    private final BillingService service;

    @GetMapping("/has-billable-expense-or-disbursement/{billingPeriodEnds}")
    public BaseResponse<Boolean> hasBillableExpenseOrDisbursement(
            @RequestParam(required = false) List<Long> expenseIds,
            @RequestParam(required = false) List<Long> disbursementIds,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        var hasBillable = service.hasBillableExpenseOrDisbursement(expenseIds, disbursementIds, billingPeriodEnds);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(hasBillable)
                .build();
    }

    @GetMapping(path = "/has-unapproved-expense-or-disbursement/{billingPeriodEnds}")
    public BaseResponse<Boolean> hasUnapprovedExpenseOrDisbursement(
            @RequestParam(required = false) List<Long> expenseIds,
            @RequestParam(required = false) List<Long> disbursementIds,
            @PathVariable @DateTimeFormat(iso = DateTimeFormat.ISO.DATE) LocalDate billingPeriodEnds) {
        var result = service.hasUnapprovedExpenseOrDisbursement(expenseIds, disbursementIds, billingPeriodEnds);
        return BaseResponse.<Boolean>builder()
                .code(ResponseCode.SUCCESS)
                .message(ResponseCode.SUCCESS.getMessageKey())
                .payload(result)
                .build();
    }
}
