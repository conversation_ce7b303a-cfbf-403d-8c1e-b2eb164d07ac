package com.ipms.billing.service.impl;

import com.ipms.billing.client.ParamCommandClient;
import com.ipms.billing.dto.*;
import com.ipms.billing.enums.BillingDay;
import com.ipms.billing.enums.RiskManagement;
import com.ipms.billing.exception.BillingAccountNotFoundException;
import com.ipms.billing.mapper.BillingAccountMapper;
import com.ipms.billing.model.BillingAccount;
import com.ipms.billing.model.BillingAccountExpenseCode;
import com.ipms.billing.repository.BillingAccountExpenseCodeRepository;
import com.ipms.billing.repository.BillingAccountRepository;
import com.ipms.billing.service.BillingAccountService;
import com.ipms.billing.specification.BillingAccountSpecification;
import com.ipms.billing.validator.BillingAccountValidator;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.enums.Domain;
import com.ipms.core.common.enums.TransferType;
import com.ipms.core.common.model.TransferEvent;
import com.ipms.core.common.utils.NumberUtils;
import com.ipms.core.common.utils.ObjectUtils;
import lombok.RequiredArgsConstructor;
import org.jetbrains.annotations.NotNull;
import org.jetbrains.annotations.Nullable;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.YearMonth;
import java.util.List;
import java.util.Map;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class BillingAccountServiceImpl implements BillingAccountService {
    private final BillingAccountExpenseCodeRepository expenseCodeRepository;
    private final BillingAccountRepository repository;
    private final BillingAccountMapper mapper;
    private final BillingAccountValidator validator;
    private final ProducerService producerService;
    private final ParamCommandClient paramCommandClient;

    @Value("${kafka.risk-impact-update-topic}")
    private String riskImpactUpdateTopic;

    @Override
    public BillingAccountDto save(BillingAccountDto billingAccountDto) {
        validator.validate(billingAccountDto);
        var billingAccount = mapper.toBillingAccount(billingAccountDto);
        var saved = repository.save(billingAccount);
        sendTransferEvent(saved, TransferType.INSERT);
        return mapper.toBillingAccountDto(saved);
    }

    @Override
    public BillingAccountDto update(Long id, BillingAccountDto billingAccountDto) {
        validator.validate(id, billingAccountDto);
        var billingAccount = getBillingAccountById(id);
        billingAccount = mapper.toBillingAccountFromDto(billingAccountDto, billingAccount);
        var saved = repository.save(billingAccount);
        sendRiskManagementUpdateEvent(billingAccount);
        sendTransferEvent(saved, TransferType.UPDATE);
        return mapper.toBillingAccountDto(saved);
    }

    @Override
    public BillingAccount getBillingAccountById(Long id) {
        return repository.findById(id)
                .orElseThrow(BillingAccountNotFoundException::new);
    }

    @Override
    public BillingAccountDto getById(Long id) {
        var billingAccount = getBillingAccountById(id);
        return mapper.toBillingAccountDto(billingAccount);
    }

    @Override
    public void delete(Long id, Long version) {
        var deleted = getBillingAccountById(id).toBuilder()
                .isDeleted(true)
                .version(version)
                .build();
        repository.save(deleted);
        sendTransferEvent(deleted, TransferType.DELETE);
    }

    @Override
    public List<BillingAccountDto> getAll(BillingAccountFilterRequest filterRequest) {
        return repository.findAll(mapSpecification(filterRequest)).stream()
                .map(mapper::toBillingAccountDto)
                .toList();
    }

    private BillingAccountSpecification mapSpecification(BillingAccountFilterRequest filterRequest) {
        return BillingAccountSpecification.builder()
                .ids(filterRequest.getIds())
                .accountNames(filterRequest.getAccountNames())
                .accountNos(filterRequest.getAccountNos())
                .issueIds(filterRequest.getIssueIds())
                .searchKey(filterRequest.getSearchKey())
                .build();
    }

    @Override
    public BillingAccountPageDto getAllPageable(BillingAccountFilterRequest filterRequest, int page, int size) {
        var billingAccountPage = repository.findAll(mapSpecification(filterRequest), PageRequest.of(page, size));
        return BillingAccountPageDto.builder()
                .billingAccountDtos(billingAccountPage.getContent().stream()
                        .map(mapper::toBillingAccountDto)
                        .toList())
                .totalElements(billingAccountPage.getTotalElements())
                .totalPages(billingAccountPage.getTotalPages())
                .build();
    }

    @Override
    public BillingAccountDto addIssue(Long id, Long issueId) {
        var billingAccount = getBillingAccountById(id);
        billingAccount.getIssueIds().add(issueId);
        var saved = repository.save(billingAccount);
        return mapper.toBillingAccountDto(saved);
    }

    @Override
    public BillableBillingAccount isBillable(Long id, List<LocalDate> favorableDates) {
        var billingAccount = getBillingAccountById(id);
        var isBillable = BillingDay.isDay(billingAccount.getBillingDay());
        if (isBillable) {
            var billingDayOptional = favorableDates.stream()
                    .filter(date ->
                            billingAccount.getBillingDay().getValue() == date.getDayOfMonth()
                            || isEndOfMonth(date, billingAccount))
                    .findFirst();
            if (billingDayOptional.isPresent()) {
                var billingDay = billingDayOptional.get();
                if (isEndOfMonth(billingDay)) {
                    YearMonth yearMonth = YearMonth.from(billingDay);
                    billingDay = yearMonth.atEndOfMonth();
                }
                return BillableBillingAccount.builder()
                        .billingDay(billingDay)
                        .isBillable(true)
                        .build();
            }
        }
        return BillableBillingAccount.builder()
                .isBillable(false)
                .build();
    }

    @Override
    public List<BillingAccountDto> getByIdIn(List<Long> ids) {
        return repository.findByIdIn(ids).stream()
                .map(mapper::toBillingAccountDto)
                .toList();
    }

    @Override
    public void processJdePriceEvent(PriceEvent priceEvent) {
        var priceDtos = priceEvent.getSpecialPrices();
        var accountNos = priceEvent.getAccountNos();
        var issuerIdToIssuerCodeMap = getIssuerIdToIssuerCodeMap();
        var billingAccounts = getBillingAccountsByAccountNoIn(accountNos);
        var accountNoIssuerCodeToBillingAccountMap = getBillingAccountMap(billingAccounts, issuerIdToIssuerCodeMap);
        var billingAccountIdToExpenseCodesMap = getExpenseCodeMap(billingAccounts);

        priceDtos.forEach(priceDto -> {
            var billingAccount = accountNoIssuerCodeToBillingAccountMap.get(concatCustomerAndMainCompany(priceDto));
            if (billingAccount != null) {
                var billingAccountExpenseCodes = billingAccountIdToExpenseCodesMap.get(billingAccount.getId());
                if (billingAccountExpenseCodes != null) {
                    updateExpenseCode(priceDto, billingAccountExpenseCodes);
                }
            }
        });
    }

    private void updateExpenseCode(PriceDto priceDto, List<BillingAccountExpenseCode> billingAccountExpenseCodes) {
        var updatedExpenseCodeOptional = billingAccountExpenseCodes.stream()
                .filter(billingAccountExpenseCode ->
                        billingAccountExpenseCode.getSpecialExpenseCode().equals(priceDto.getItemNo()))
                .findFirst();
        updatedExpenseCodeOptional.ifPresent(updatedExpenseCode -> {
            updatedExpenseCode.setUnitPrice(getParseUnitPrice(priceDto.getPrice()));
            expenseCodeRepository.save(updatedExpenseCode);
        });
    }

    private List<BillingAccount> getBillingAccountsByAccountNoIn(List<String> accountNos) {
        return repository.findByAccountNoIn(accountNos);
    }

    private @NotNull Map<Long, String> getIssuerIdToIssuerCodeMap() {
        return paramCommandClient.getIssuers().getPayload().stream()
                .collect(Collectors.toMap(IssuerDto::getId, IssuerDto::getCode));
    }

    private @NotNull Map<String, BillingAccount> getBillingAccountMap(List<BillingAccount> billingAccounts,
                                                                      Map<Long, String> issuerMap) {
        return billingAccounts.stream()
                .collect(Collectors.toMap(billingAccount -> getBillingAccountIssuerCodeKey(billingAccount, issuerMap),
                        billingAccount -> billingAccount));
    }

    private @Nullable BigDecimal getParseUnitPrice(String unitPrice) {
        return NumberUtils.parseBigDecimal(unitPrice, 4, RoundingMode.HALF_UP);
    }

    private Map<Long, List<BillingAccountExpenseCode>> getExpenseCodeMap(List<BillingAccount> billingAccounts) {
        var billingAccountIds = billingAccounts.stream()
                .map(BillingAccount::getId)
                .toList();
        return expenseCodeRepository.findByBillingAccountIdIn(billingAccountIds).stream()
                .collect(Collectors.groupingBy(BillingAccountExpenseCode::getBillingAccountId));
    }

    private @NotNull String concatCustomerAndMainCompany(PriceDto priceDto) {
        return priceDto.getCustomerorSupplierNumber().concat(priceDto.getMainCompany());
    }

    private @NotNull String getBillingAccountIssuerCodeKey(BillingAccount billingAccount, Map<Long, String> issuerMap) {
        return billingAccount.getAccountNo().concat(issuerMap.get(billingAccount.getIssuerId()));
    }

    public void sendTransferEvent(BillingAccount billingAccount, TransferType transferType) {
        producerService.sendTransfer(TransferEvent.builder()
                .type(transferType)
                .domainObjectId(billingAccount.getId())
                .domain(Domain.IPMS_BILLING)
                .build());
    }

    private void sendRiskManagementUpdateEvent(BillingAccount billingAccount) {
        if (RiskManagement.HIGH_RISK.equals(billingAccount.getRiskManagement())) {
            producerService.send(riskImpactUpdateTopic, ObjectUtils.toJson(billingAccount.getId()));
        }
    }

    private boolean isEndOfMonth(LocalDate billingDay) {
        return billingDay.getDayOfMonth() > 28;
    }

    private boolean isEndOfMonth(LocalDate date, BillingAccount billingAccount) {
        return billingAccount.getBillingDay().equals(BillingDay.END_OF_MONTH) && isEndOfMonth(date);
    }
}
