package com.ipms.billing.service.impl;

import com.ipms.billing.client.*;
import com.ipms.billing.dto.*;
import com.ipms.billing.enums.ActivityBillingStatus;
import com.ipms.billing.enums.BillingOrderStatus;
import com.ipms.billing.enums.CoreBusinessBusinessUnit;
import com.ipms.billing.exception.BillingOrderCancelException;
import com.ipms.billing.exception.BillingOrderNotFoundException;
import com.ipms.billing.mapper.BillingOrderMapper;
import com.ipms.billing.model.BillingAccount;
import com.ipms.billing.model.BillingOrder;
import com.ipms.billing.model.BillingOrderDetail;
import com.ipms.billing.model.Expense;
import com.ipms.billing.repository.BillingOrderRepository;
import com.ipms.billing.service.*;
import com.ipms.billing.specification.BillingOrderSpecification;
import com.ipms.billing.util.BillingOrderUtils;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.config.security.utils.SecurityUtils;
import com.ipms.core.common.enums.Domain;
import com.ipms.core.common.enums.NotificationDestinationType;
import com.ipms.core.common.enums.NotificationType;
import com.ipms.core.common.model.NotificationEvent;
import com.ipms.core.common.utils.ObjectUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.ArrayList;
import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class BillingOrderServiceImpl implements BillingOrderService {
    private final BillingOrderRepository repository;
    private final BillingOrderMapper mapper;
    private final ParameterClient parameterClient;
    private final ActivityClient activityClient;
    private final MatterClient matterClient;
    private final ParamCommandClient paramCommandClient;
    private final ProducerService producerService;
    private final BillingAccountService billingAccountService;
    private final FirmClient firmClient;
    private final ExpenseService expenseService;
    private final TimesheetService timesheetService;
    private final DisbursementService disbursementService;
    private final IntegrationClient integrationClient;
    private final OrderNumberService orderNumberService;

    @Value("${kafka.billing-order-integration-saved-topic}")
    private String billingOrderIntegrationSavedTopic;
    @Value("${kafka.billing-order-cancelled-topic}")
    private String billingOrderCancelledTopic;
    @Value("${kafka.invoice-updated-topic}")
    private String invoiceUpdatedTopic;

    @Override
    public BillingOrderDto save(BillingOrderDto billingOrderDto) {
        var nextOrderNumber = orderNumberService.generateOrderNumber();
        var billingOrder = mapper.toBillingOrder(billingOrderDto);
        billingOrder.setOrderNumber(nextOrderNumber);
        var saved = repository.save(billingOrder);
        producerService.send(billingOrderIntegrationSavedTopic, ObjectUtils.toJson(saved.getId()));
        return mapper.toBillingOrderDto(saved);
    }

    @Override
    public void processBillingApprovedEvent(BillingApprovedEvent billingApprovedEvent) {
        var billingAccount = billingAccountService.getBillingAccountById(billingApprovedEvent.getBillingAccountId());
        var paralegal = matterClient.getById(billingApprovedEvent.getMatterId()).getPayload().getParalegal();
        var createOrderAutomatically = billingAccount.getCreateOrderAutomatically();
        if (BooleanUtils.isTrue(createOrderAutomatically)) {
            var billingPeriodEnds = LocalDate.parse(billingApprovedEvent.getBillingPeriodEnds());
            var billingOrder = BillingOrder.builder()
                    .orderNumber(orderNumberService.generateOrderNumber())
                    .orderDate(LocalDate.now())
                    .issuerId(billingAccount.getIssuerId())
                    .businessUnit(getBusinessUnitByMatterId(billingApprovedEvent.getMatterId()))
                    .billingAccountNo(billingAccount.getAccountNo())
                    .assignee(paralegal)
                    .build();
            setDescriptions(billingApprovedEvent, billingAccount, billingOrder);
            var orderCreateRequest = OrderCreateRequest.builder()
                    .orderDate(billingOrder.getOrderDate())
                    .orderNumber(billingOrder.getOrderNumber())
                    .expenseIds(billingApprovedEvent.getExpenseIds())
                    .billingPeriodEnds(billingPeriodEnds)
                    .autoCreate(true)
                    .build();
            setOrderDetailsAndSave(orderCreateRequest, billingOrder);

        }
    }

    @Override
    public void processOrderIntegrationErrorEvent(String orderNumber) {
        var billingOrder = getByOrderNumber(orderNumber);
        billingOrder.setStatus(BillingOrderStatus.ERROR_IN_ORDER);
        repository.save(billingOrder);
    }

    @Override
    public void processOrderIntegrationSuccessEvent(String orderNumber) {
        var billingOrder = getByOrderNumber(orderNumber);
        billingOrder.setStatus(BillingOrderStatus.ORDER_INTEGRATED);
        repository.save(billingOrder);
    }

    private BillingOrder getByOrderNumber(String orderNumber) {
        return repository.findByOrderNumber(orderNumber).orElseThrow(BillingOrderNotFoundException::new);
    }

    private void setDescriptions(BillingApprovedEvent billingApprovedEvent, BillingAccount billingAccount,
                                 BillingOrder billingOrder) {
        var matterDto = matterClient.getById(billingApprovedEvent.getMatterId()).getPayload();
        var firmTitle = firmClient.getById(matterDto.getFirms().get(0)).getPayload().getTitle();
        var descriptionMap = BillingOrderUtils.getDescription(matterDto, firmTitle,
                billingApprovedEvent.getAgentReference(), billingAccount.getLanguageToBeBilled());
        billingOrder.setDescription1(descriptionMap.get("description1"));
        billingOrder.setDescription2(descriptionMap.get("description2"));
        billingOrder.setDescription3(descriptionMap.get("description3"));
    }

    private BillingOrder setOrderDetailsAndSave(OrderCreateRequest orderCreateRequest, BillingOrder billingOrder) {
        var expenseSummaryDtos = expenseService.processOrderCreate(orderCreateRequest);
        var orderDetails = createOrderDetailsByExpenseSummary(billingOrder, expenseSummaryDtos);
        billingOrder.setOrderDetails(orderDetails);
        var saved = repository.save(billingOrder);
        updateActivityStatusAsOrderCreated(orderCreateRequest.getExpenseIds());
        producerService.send(billingOrderIntegrationSavedTopic, ObjectUtils.toJson(saved.getId()));
        return saved;
    }

    private void updateActivityStatusAsOrderCreated(List<Long> expenseIds) {
        var activityList = activityClient.getActivitiesByExpenses(expenseIds).getPayload();
        activityList.forEach(activity -> {
            var activityExpenses = expenseService.getByActivityId(activity.getId());
            var activityExpenseIds = activityExpenses.stream()
                    .map(ExpenseDto::getId)
                    .toList();
            var hasOrderableExpense = expenseService.hasOrderableExpense(activityExpenseIds);
            if (!hasOrderableExpense) {
                activityClient.setOrderCreatedStatus(activity.getId());
            }
        });
    }

    @Transactional
    @Override
    public BillingOrderDto saveWithExpenses(BillingOrderCreateDto billingOrderCreateDto) {
        var nextOrderNumber = orderNumberService.generateOrderNumber();
        var billingOrder = mapper.toBillingOrder(billingOrderCreateDto);
        billingOrder.setOrderNumber(nextOrderNumber);
        billingOrder.setAssignee(SecurityUtils.getCurrentUsername().orElseThrow());
        var businessUnit = getBusinessUnit(billingOrderCreateDto);
        billingOrder.setBusinessUnit(businessUnit);

        var orderCreateRequest = OrderCreateRequest.builder()
                .orderDate(billingOrder.getOrderDate())
                .orderNumber(billingOrder.getOrderNumber())
                .expenseIds(billingOrderCreateDto.getExpenseIds())
                .build();
        var billingAccount = billingAccountService.getBillingAccountById(billingOrderCreateDto.getBillingAccountId());
        orderCreateRequest.setBillingAccountCurrency(billingAccount.getCurrency());
        var saved = setOrderDetailsAndSave(orderCreateRequest, billingOrder);
        return mapper.toBillingOrderDto(saved);
    }

    @Transactional
    @Override
    public List<BillingOrderDto> saveBulkWithExpenses(List<BillingOrderCreateDto> billingOrderCreateDtos) {
        var firstBillingOrderDto = billingOrderCreateDtos.stream().findFirst().orElseThrow();
        var billingAccount = billingAccountService.getBillingAccountById(firstBillingOrderDto.getBillingAccountId());
        if (billingOrderCreateDtos.size() == 1) {
            var billingOrderDto = saveWithExpenses(billingOrderCreateDtos.get(0));
            return List.of(billingOrderDto);
        }

        var billingOrders = createAndSaveBillingOrders(billingOrderCreateDtos);

        var orderCreateRequests = billingOrders.stream()
                .map(savedDto -> OrderCreateRequest.builder()
                        .orderDate(savedDto.getOrderDate())
                        .orderNumber(savedDto.getOrderNumber())
                        .expenseIds(firstBillingOrderDto.getExpenseIds())
                        .billingAccountCurrency(billingAccount.getCurrency())
                        .build())
                .toList();
        var expenseDtos = expenseService.processOrderSplit(firstBillingOrderDto.getActivityId(), orderCreateRequests);
        billingOrders.forEach(billingOrder -> {
            var expenses = expenseDtos.stream()
                    .filter(expense -> expense.getOrderNumber().equals(billingOrder.getOrderNumber()))
                    .toList();
            var billingOrderDetails = createOrderDetails(billingOrder, expenses);
            billingOrder.setOrderDetails(billingOrderDetails);
        });
        updateActivityStatusAsOrderCreated(orderCreateRequests.get(0).getExpenseIds());
        var savedBillingOrders = repository.saveAll(billingOrders);
        billingOrders.forEach(saved ->
                producerService.send(billingOrderIntegrationSavedTopic, ObjectUtils.toJson(saved.getId())));
        return savedBillingOrders.stream()
                .map(mapper::toBillingOrderDto)
                .toList();
    }

    @NotNull
    private List<BillingOrder> createAndSaveBillingOrders(List<BillingOrderCreateDto> billingOrderCreateDtos) {
        // Generate all order numbers atomically in bulk to prevent duplicates
        var orderNumbers = orderNumberService.generateOrderNumbers(billingOrderCreateDtos.size());
        
        List<BillingOrder> billingOrders = new ArrayList<>();
        for (int i = 0; i < billingOrderCreateDtos.size(); i++) {
            var dto = billingOrderCreateDtos.get(i);
            var billingOrder = mapper.toBillingOrder(dto);
            billingOrder.setAssignee(SecurityUtils.getCurrentUsername().orElseThrow());
            billingOrder.setOrderNumber(orderNumbers.get(i));
            billingOrder.setBusinessUnit(getBusinessUnit(dto));
            billingOrders.add(billingOrder);
        }
        
        return repository.saveAll(billingOrders);
    }

    private List<BillingOrderDetail> createOrderDetails(BillingOrder billingOrder,
                                                        List<ExpenseDto> expenses) {
        return expenses.stream()
                .map(expense -> BillingOrderDetail.builder()
                        .expenses(new ArrayList<>(List.of(expense.getId())))
                        .billingOrder(billingOrder)
                        .expenseCode(expense.getExpenseCode())
                        .quantity(expense.getQuantity())
                        .unitPrice(getUnitPrice(expense.getExpenseCode(), expense.getCurrency(), expense.getUnitPrice()))
                        .discountRate(expense.getDiscountRate())
                        .build())
                .collect(Collectors.toList());
    }

    private List<BillingOrderDetail> createOrderDetailsByExpenseSummary(BillingOrder billingOrder,
                                                                        List<ExpenseSummaryDto> expenseSummaries) {
        return expenseSummaries.stream()
                .map(expenseSummary -> BillingOrderDetail.builder()
                        .expenses(expenseSummary.getExpenseIds())
                        .billingOrder(billingOrder)
                        .expenseCode(expenseSummary.getExpenseCode())
                        .quantity(expenseSummary.getQuantity())
                        .unitPrice(getUnitPrice(expenseSummary.getExpenseCode(), expenseSummary.getCurrency(),
                                expenseSummary.getUnitPrice()))
                        .discountRate(expenseSummary.getDiscountRate())
                        .build())
                .collect(Collectors.toList());
    }

    private BigDecimal getUnitPrice(String expenseCode, String currency, BigDecimal unitPrice) {
        var unitPriceDefined = paramCommandClient.isUnitPriceDefined(expenseCode, currency)
                .getPayload();
        if (BooleanUtils.isFalse(unitPriceDefined)) {
            return unitPrice;
        }
        return null;
    }

    @Override
    public List<BillingOrderDto> getByIdIn(List<Long> ids) {
        return repository.findByIdIn(ids).stream()
                .map(mapper::toBillingOrderDto)
                .toList();
    }

    @Override
    public void updateExternalOrderNumber(BillingIntegrationUpdateEvent billingIntegrationUpdateEvent) {
        var billingOrder = getBillingOrderById(billingIntegrationUpdateEvent.getId());
        billingOrder.setExternalOrderNumber(billingIntegrationUpdateEvent.getExternalOrderNumber());
        repository.save(billingOrder);
        sendNotification(billingOrder);
    }

    @Transactional
    @Override
    public void cancel(Long id) {
        var billingOrder = getBillingOrderById(id);

        if (requiresJdeValidation(billingOrder.getStatus())) {
            validateJdeCancellation(billingOrder);
        }

        var activities = findActivitiesLinkedToBillingOrder(billingOrder);
        activities.forEach(activity -> processActivityCancellation(activity, billingOrder));

        delete(billingOrder.getId());
    }

    private void processActivityCancellation(ActivityListResponse.Payload activity, BillingOrder billingOrder) {
        var activityExpenseIds = expenseService.getByActivityId(activity.getId()).stream()
                .map(ExpenseDto::getId)
                .toList();
        var expenses = expenseService.getExpensesByIdsAndOrderNo(activityExpenseIds, billingOrder.getOrderNumber());

        if (ActivityBillingStatus.isDeleteExpensesStatus(activity.getBillingStatus())) {
            cancelBillingOrderAndDeleteRelatedExpenses(expenses);
        } else if (ActivityBillingStatus.isNullifyExpensesStatus(activity.getBillingStatus())) {
            validateJdeCancellation(billingOrder);

            if (hasUnapprovedRecord(activity)) {
                cancelBillingOrderAndDeleteRelatedExpenses(expenses);
            } else {
                expenseService.resetBillingData(expenses);
            }
            activityClient.cancelBillingOrder(activity.getId());
        }
    }

    private void validateJdeCancellation(BillingOrder billingOrder) {
        if (!isCancelledByJde(billingOrder)) {
            throw new BillingOrderCancelException("The billing order cannot be canceled because it is not canceled by JDE.");
        }
    }

    private boolean requiresJdeValidation(BillingOrderStatus status) {
        return status == BillingOrderStatus.ORDER_INTEGRATED || 
               status == BillingOrderStatus.INVOICE_IS_READY;
    }

    private List<ActivityListResponse.Payload> findActivitiesLinkedToBillingOrder(BillingOrder billingOrder) {
        var expenseIdsByOrderNo = expenseService.getExpenseIdsByOrderNo(billingOrder.getOrderNumber());
        if (expenseIdsByOrderNo.isEmpty()) {
            return List.of();
        }
        return activityClient.getActivitiesByExpenses(expenseIdsByOrderNo).getPayload();
    }

    private boolean hasUnapprovedRecord(ActivityListResponse.Payload activity) {
        var activityExpenseIds = expenseService.getByActivityId(activity.getId()).stream()
                .map(ExpenseDto::getId)
                .toList();
        var activityDisbursementIds = disbursementService.getByActivityId(activity.getId()).stream()
                .map(DisbursementDto::getId)
                .toList();
        
        return timesheetService.hasUnapproved(activity.getId(), activity.getBillingPeriodEnds())
                || expenseService.hasUnapprovedExpense(activityExpenseIds, activity.getBillingPeriodEnds())
                || disbursementService.hasUnapprovedDisbursement(
                        activityDisbursementIds, activity.getBillingPeriodEnds());
    }

    private void cancelBillingOrderAndDeleteRelatedExpenses(List<Expense> expenses) {
        expenseService.deleteExpensesRelatedWithTimesheetAndDisbursement(expenses);
        cancelBillingOrderForDisbursements(expenses);
        cancelBillingOrderForTimesheets(expenses);
    }

    private boolean isCancelledByJde(BillingOrder billingOrder) {
        var externalOrderNumber = billingOrder.getExternalOrderNumber();
        if (StringUtils.isBlank(externalOrderNumber)) {
            return true;
        }
        return integrationClient.isOrderCancelled(
                billingOrder.getIssuerId(),
                externalOrderNumber,
                billingOrder.getBillingAccountNo()
        ).getPayload();
    }

    private void cancelBillingOrderForTimesheets(List<Expense> expenses) {
        var timesheets = expenses.stream()
                .flatMap(expense -> expense.getTimesheets().stream())
                .toList();
        if (CollectionUtils.isNotEmpty(timesheets)) {
            timesheetService.cancelBillingOrder(timesheets);
        }
    }

    private void cancelBillingOrderForDisbursements(List<Expense> expenses) {
        var disbursementIds = expenses.stream()
                .flatMap(expense -> expense.getDisbursements().stream())
                .toList();
        if (CollectionUtils.isNotEmpty(disbursementIds)) {
            disbursementService.cancelBillingOrder(disbursementIds);
        }
    }

    private void sendNotification(BillingOrder billingOrder) {
        if (!billingOrder.getCreatedBy().equals("service-account-ipms-backend")) {
            producerService.sendNotification(NotificationEvent.builder()
                    .domain(Domain.IPMS_BILLING)
                    .domainObjectId(billingOrder.getId())
                    .destinationType(NotificationDestinationType.USER)
                    .destination(billingOrder.getCreatedBy())
                    .type(NotificationType.UPDATING)
                    .detail("externalOrderNumberUpdated")
                    .build());
        }
    }

    private String getBusinessUnitByMatterId(Long matterId) {
        var coreBusiness = matterClient.getById(matterId).getPayload().getCoreBusiness();
        return CoreBusinessBusinessUnit.valueOf(coreBusiness).getBusinessUnit();
    }

    private String getBusinessUnit(BillingOrderCreateDto billingOrderCreateDto) {
        var matterId = activityClient.getActivityByExpenseId(billingOrderCreateDto.getExpenseIds().get(0))
                .getPayload()
                .getMatterId();
        return getBusinessUnitByMatterId(matterId);
    }


    @Override
    public BillingOrderDto getById(Long id) {
        var billingOrder = getBillingOrderById(id);
        return mapper.toBillingOrderDto(billingOrder);
    }

    private BillingOrder getBillingOrderById(Long id) {
        return repository.findById(id).orElseThrow(BillingOrderNotFoundException::new);
    }

    @Override
    public void delete(Long id) {
        var billingOrder = getBillingOrderById(id).toBuilder()
                .isDeleted(true)
                .build();
        billingOrder.getOrderDetails().forEach(detail -> detail.setDeleted(true));
        repository.save(billingOrder);
        producerService.send(billingOrderCancelledTopic, ObjectUtils.toJson(billingOrder.getId()));
    }

    public List<BillingOrderDto> getBillingOrderDTOList(Page<BillingOrder> billingOrderPage) {
        return billingOrderPage.getContent()
                .stream().map(billingOrder -> {
                    var tmpBO = mapper.toBillingOrderDto(billingOrder);

                    var expenseIds = billingOrder.getOrderDetails().stream()
                            .flatMap(billingOrderDetail -> billingOrderDetail.getExpenses().stream())
                            .collect(Collectors.toList());

                    if (!expenseIds.isEmpty()) {
                        var matterIds = activityClient.getMatterIdsByExpenses(expenseIds)
                                .getPayload()
                                .stream().map(MatterId::matterId).toList();

                        var matterSummaries = matterClient.getMatterSummary(matterIds)
                                .getPayload()
                                .stream()
                                .toList();

                        tmpBO.setMatterSummaries(matterSummaries);
                    }

                    return tmpBO;
                }).toList();
    }

    @Override
    public BillingOrderPageDto getPageDto(BillingOrderFilterRequest filterRequest, int page, int size) {
        Page<BillingOrder> billingOrderPage = repository.findAll(mapToSpecification(filterRequest),
                PageRequest.of(page, size));

        return BillingOrderPageDto.builder()
                .billingOrderDtos(getBillingOrderDTOList(billingOrderPage))
                .totalElements(billingOrderPage.getTotalElements())
                .totalPages(billingOrderPage.getTotalPages())
                .build();
    }

    private BillingOrderSpecification mapToSpecification(BillingOrderFilterRequest filterRequest) {
        return BillingOrderSpecification.builder()
                .ids(filterRequest.getIds())
                .orderNumbers(filterRequest.getOrderNumbers())
                .assignee(filterRequest.getAssignee())
                .invoiceNumber(filterRequest.getInvoiceNumber())
                .orderNumber(filterRequest.getOrderNumber())
                .issuerIds(filterRequest.getIssuerIds())
                .assignees(filterRequest.getAssignees())
                .statuses(filterRequest.getStatuses())
                .createdBy(filterRequest.getCreatedBy())
                .updatedBy(filterRequest.getUpdatedBy())
                .sortField(filterRequest.getSortField())
                .sortDirection(filterRequest.getSortDirection())
                .build();
    }

    @Transactional
    @Override
    public void processInvoiceFetchedEvent(InvoiceFetchedEvent.Invoice invoice) {
        var externalOrderNumber = invoice.getErpOrderNumber();
        var billingAccountNo = invoice.getCustomerorSupplierNumber();
        var billingOrder = repository.findByExternalOrderNumberAndBillingAccountNo(
                externalOrderNumber, billingAccountNo);

        billingOrder.ifPresent(order -> {
            if (StringUtils.isEmpty(order.getInvoiceNumber())) {
                setExpenseIds(invoice.getInvoiceLines(), order.getOrderDetails());
                expenseService.updateInvoiceFields(invoice);
                if (expenseService.isInvoiceCompleted(order.getOrderNumber())) {
                    updateBillingOrderWithInvoice(invoice, order);
                    sendInvoiceUpdatedEvent(invoice.getInvoiceLines());
                }
            }
        });
    }

    private void updateBillingOrderWithInvoice(InvoiceFetchedEvent.Invoice invoice, BillingOrder order) {
        order.setInvoiceNumber(invoice.getErpDocumentNo());
        order.setInvoiceDate(LocalDate.parse(invoice.getInvoiceDate()));
        order.setStatus(BillingOrderStatus.INVOICE_IS_READY);
        repository.save(order);
    }


    private void sendInvoiceUpdatedEvent(List<InvoiceFetchedEvent.InvoiceLine> invoiceLines) {
        var expenseIds = invoiceLines.stream()
                .flatMap(invoiceLine -> invoiceLine.getExpenseIds().stream())
                .collect(Collectors.toSet());
        if (CollectionUtils.isNotEmpty(expenseIds)) {
            var invoiceUpdatedEvent = InvoiceUpdatedEvent.builder()
                    .expenseIds(expenseIds)
                    .build();
            producerService.sendTransactional(invoiceUpdatedTopic, ObjectUtils.toJson(invoiceUpdatedEvent));
        }
    }

    private void setExpenseIds(List<InvoiceFetchedEvent.InvoiceLine> invoiceLines,
                               List<BillingOrderDetail> orderDetails) {
        invoiceLines.forEach(invoiceLine -> setExpenseIdsForInvoiceLine(orderDetails, invoiceLine));
    }

    private static void setExpenseIdsForInvoiceLine(List<BillingOrderDetail> orderDetails,
                                                    InvoiceFetchedEvent.InvoiceLine invoiceLine) {
        orderDetails.stream()
                .filter(detail -> String.valueOf(detail.getId()).equals(StringUtils.trim(invoiceLine.getBillingId())))
                .findFirst()
                .ifPresent(detail -> invoiceLine.setExpenseIds(detail.getExpenses()));
    }

}
