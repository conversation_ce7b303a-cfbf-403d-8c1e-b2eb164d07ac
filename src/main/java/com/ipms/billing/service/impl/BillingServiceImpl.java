package com.ipms.billing.service.impl;

import com.ipms.billing.service.BillingService;
import com.ipms.billing.service.DisbursementService;
import com.ipms.billing.service.ExpenseService;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import java.time.LocalDate;
import java.util.List;

@Service
@RequiredArgsConstructor
public class BillingServiceImpl implements BillingService {
    private final ExpenseService expenseService;
    private final DisbursementService disbursementService;

    @Override
    public boolean hasBillableExpenseOrDisbursement(List<Long> expenseIds, List<Long> disbursementIds,
                                                    LocalDate billingPeriodEnds) {
        return expenseService.hasBillableExpenseByBillingPeriodEnds(expenseIds, billingPeriodEnds)
                || disbursementService.hasBillableDisbursementByBillingPeriodEnds(disbursementIds, billingPeriodEnds);
    }

    @Override
    public boolean hasUnapprovedExpenseOrDisbursement(List<Long> expenseIds, List<Long> disbursementIds,
                                                      LocalDate billingPeriodEnds) {
        return expenseService.hasUnapprovedExpense(expenseIds, billingPeriodEnds)
                || disbursementService.hasUnapprovedDisbursement(disbursementIds, billingPeriodEnds);
    }
}
