package com.ipms.billing.service.impl;

import com.ipms.billing.client.ParameterClient;
import com.ipms.billing.repository.OrderNumberSequenceRepository;
import com.ipms.billing.service.OrderNumberService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigInteger;
import java.time.Year;
import java.util.ArrayList;
import java.util.List;
import java.util.regex.Pattern;
import java.util.stream.Collectors;

/**
 * Thread-safe implementation of OrderNumberService using JPA Repository pattern.
 * 
 * This elegant implementation uses Spring Data JPA with native queries to
 * interact with PostgreSQL sequences, ensuring atomic order number generation
 * while maintaining clean architecture principles.
 */
@Slf4j
@Service
@RequiredArgsConstructor
public class OrderNumberServiceImpl implements OrderNumberService {
    
    private final ParameterClient parameterClient;
    private final OrderNumberSequenceRepository sequenceRepository;
    
    private static final String SEQUENCE_NAME = "billing_order_number_seq";
    private static final String PARAMETER_KEY = "ORDER_NUMBER_PREFIX";
    private static final int ORDER_NUMBER_LENGTH = 6;
    
    // Regex pattern for order number validation: PREFIX + 2 digits (year) + 6 digits (sequence)
    private static final Pattern ORDER_NUMBER_PATTERN = Pattern.compile("^[A-Z]{2,}\\d{8}$");
    
    @Override
    @Transactional
    public String generateOrderNumber() {
        log.debug("Generating single order number");
        
        var orderNumberPrefix = getOrderNumberPrefix();
        var yearSuffix = getCurrentYearSuffix();
        var sequenceValue = getNextSequenceValue();
        
        var orderNumber = String.format("%s%s%06d", orderNumberPrefix, yearSuffix, sequenceValue);
        
        log.debug("Generated order number: {}", orderNumber);
        return orderNumber;
    }
    
    @Override
    @Transactional
    public List<String> generateOrderNumbers(int count) {
        if (count < 1) {
            throw new IllegalArgumentException("Count must be at least 1");
        }
        
        log.debug("Generating {} order numbers in bulk", count);
        
        var orderNumberPrefix = getOrderNumberPrefix();
        var yearSuffix = getCurrentYearSuffix();
        var sequenceValues = getNextSequenceValues(count);
        
        List<String> orderNumbers = new ArrayList<>(count);
        
        for (Long sequenceValue : sequenceValues) {
            var orderNumber = String.format("%s%s%06d", orderNumberPrefix, yearSuffix, sequenceValue);
            orderNumbers.add(orderNumber);
        }
        
        log.debug("Generated {} order numbers with {} sequence values", count, sequenceValues.size());
        return orderNumbers;
    }
    
    @Override
    public boolean isValidOrderNumberFormat(String orderNumber) {
        if (orderNumber == null || orderNumber.trim().isEmpty()) {
            return false;
        }
        
        return ORDER_NUMBER_PATTERN.matcher(orderNumber.trim()).matches();
    }
    
    /**
     * Gets the order number prefix from parameter service.
     * 
     * @return The order number prefix (e.g., "BO")
     */
    private String getOrderNumberPrefix() {
        try {
            return parameterClient.getByKey(PARAMETER_KEY).getPayload().getValue();
        } catch (Exception e) {
            log.error("Failed to retrieve order number prefix from parameter service", e);
            throw new RuntimeException("Unable to retrieve order number prefix", e);
        }
    }
    
    /**
     * Gets the current year suffix (last 2 digits).
     * 
     * @return Two-digit year suffix (e.g., "24" for 2024)
     */
    private String getCurrentYearSuffix() {
        return String.format("%02d", Year.now().getValue() % 100);
    }
    
    /**
     * Gets the next value from the database sequence using JPA Repository.
     * 
     * @return Next sequence value
     */
    private Long getNextSequenceValue() {
        try {
            return sequenceRepository.getNextSequenceValue();
        } catch (Exception e) {
            log.error("Failed to get next sequence value from {}", SEQUENCE_NAME, e);
            throw new RuntimeException("Unable to generate order number sequence", e);
        }
    }
    
    /**
     * Gets multiple sequential values from the database sequence using JPA Repository.
     * 
     * This method uses the repository's bulk sequence operation to efficiently
     * reserve multiple sequence values in a single database call.
     * 
     * @param count Number of sequence values needed
     * @return List of sequential sequence values
     */
    private List<Long> getNextSequenceValues(int count) {
        try {
            return sequenceRepository.getNextSequenceValues(count)
                .stream()
                .map(bigInt -> bigInt.longValue())
                .collect(Collectors.toList());
        } catch (Exception e) {
            log.error("Failed to get {} sequence values from {}", count, SEQUENCE_NAME, e);
            throw new RuntimeException("Unable to generate bulk order number sequences", e);
        }
    }
}