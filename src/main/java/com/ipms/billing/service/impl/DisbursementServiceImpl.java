package com.ipms.billing.service.impl;

import com.ipms.billing.client.ActivityClient;
import com.ipms.billing.client.ParamCommandClient;
import com.ipms.billing.client.ParameterClient;
import com.ipms.billing.dto.*;
import com.ipms.billing.enums.ToBeBilledChoice;
import com.ipms.billing.exception.DisbursementBillingApproveException;
import com.ipms.billing.exception.DisbursementNotFoundException;
import com.ipms.billing.exception.ExchangeRateNotFoundException;
import com.ipms.billing.mapper.DisbursementMapper;
import com.ipms.billing.model.BillingAccount;
import com.ipms.billing.model.Disbursement;
import com.ipms.billing.repository.DisbursementRepository;
import com.ipms.billing.service.BillingAccountService;
import com.ipms.billing.service.DisbursementService;
import com.ipms.billing.service.ExpenseService;
import com.ipms.billing.specification.DisbursementToBeBilledSpecification;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.config.storage.service.StorageService;
import com.ipms.core.common.enums.Domain;
import com.ipms.core.common.enums.TransferType;
import com.ipms.core.common.model.TransferEvent;
import com.ipms.core.common.utils.NumberUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.io.FilenameUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;
import java.util.UUID;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@RequiredArgsConstructor
@Service
public class DisbursementServiceImpl implements DisbursementService {

    private final DisbursementRepository repository;
    private final DisbursementMapper mapper;
    private final ActivityClient activityClient;
    private final StorageService storageService;
    private final ProducerService producerService;
    private final ExpenseService expenseService;
    private final ParamCommandClient paramCommandClient;
    private final ParameterClient parameterClient;
    private final BillingAccountService billingAccountService;

    @Value("${param.default-currency}")
    private String defaultCurrencyParam;

    @Value("${param.auto-billing-amount}")
    private String autoBillingAmountParam;

    @Override
    public DisbursementDto save(DisbursementDto dto) {
        Optional.ofNullable(dto.getFileName()).ifPresent(s -> dto.setFileUniqueName(prepareDocumentName(dto.getFileName())));
        var disbursement = mapper.toDisbursement(dto);
        disbursement.setToBeBilled(ToBeBilledChoice.YES);
        var saved = repository.save(disbursement);
        activityClient.addDisbursement(dto.getActivityId(), saved.getId());
        var disbursementDto = toDto(saved);
        disbursementDto.setActivityId(dto.getActivityId());
        sendTransferEvent(saved.getId(), TransferType.INSERT);
        return disbursementDto;
    }

    @Override
    public DisbursementDto update(Long id, DisbursementDto dto) {
        Optional.ofNullable(dto.getFileName())
                .ifPresent(s -> dto.setFileUniqueName(prepareDocumentName(dto.getFileName())));
        var existingDisbursement = getById(id).toBuilder().build();
        var disbursement = mapper.toDisbursementFromDto(dto, existingDisbursement);
        var updated = repository.save(disbursement);
        sendTransferEvent(updated.getId(), TransferType.UPDATE);
        return toDto(updated);
    }

    @Override
    public List<DisbursementDto> getByIdIn(List<Long> ids) {
        return repository.findByIdIn(ids)
                .stream()
                .map(this::toDto)
                .toList();
    }

    @Override
    public List<DisbursementDto> approveBillingBulk(List<Long> ids) {
        var disbursements = repository.findByIdIn(ids);
        disbursements.forEach(disbursement -> disbursement.setIsBillingApproved(Boolean.TRUE));
        return StreamSupport.stream(repository.saveAll(disbursements).spliterator(), false)
                .map(mapper::toDisbursementDto)
                .toList();
    }

    @Override
    public List<DisbursementDto> cancelApprovelBillingBulk(List<Long> ids) {
        var disbursements = repository.findByIdIn(ids);
        disbursements.forEach(disbursement -> disbursement.setIsBillingApproved(Boolean.FALSE));
        return StreamSupport.stream(repository.saveAll(disbursements).spliterator(), false)
                .map(mapper::toDisbursementDto)
                .toList();
    }

    @Override
    @Transactional
    public void approveBilling(ApproveBillingRequest approveBillingRequest) {
        var activityId = approveBillingRequest.getActivityId();
        var activityResponse = activityClient.getActivity(activityId);
        var billingAccount = billingAccountService.getBillingAccountById(activityResponse.getPayload().getBillingAccountId());
        var billingPeriodEnds = activityResponse.getPayload().getBillingPeriodEnds();
        var disbursementIds = repository.findByActivityId(activityId).stream()
                .map(Disbursement::getId)
                .toList();
        var toBeBilledDisbursements = getToBeBilledDisbursements(disbursementIds, billingPeriodEnds);
        validateDisbursementsApproval(toBeBilledDisbursements);
        var billingAccountCurrency = getBillingAccountCurrency(billingAccount);

        toBeBilledDisbursements.forEach(disbursement -> {
            if (disbursement.getExchangeRate() == null && !billingAccountCurrency.equals(disbursement.getCurrency()) ) {
                var exchangeRate = getExchangeRate(disbursement.getDisbursementDate(),
                        billingAccountCurrency,
                        disbursement.getCurrency());
                disbursement.setExchangeRate(exchangeRate);
            } else if (billingAccountCurrency.equals(disbursement.getCurrency())) {
                disbursement.setExchangeRate(BigDecimal.ONE);
            }
            disbursement.setCurrencyToBeBilled(billingAccountCurrency);
            disbursement.setAmountToBeBilled(disbursement.getAmount().divide(disbursement.getExchangeRate(), RoundingMode.HALF_UP));

        });

        repository.saveAll(toBeBilledDisbursements);

        var expenseCodeBillableTime = toBeBilledDisbursements.stream()
                .collect(Collectors.groupingBy(Disbursement::getExpenseCode));

        expenseCodeBillableTime.forEach((expenseCode, disbursements) -> {
            var totalAmountOfExpenseCode = disbursements.stream()
                    .map(Disbursement::getAmountToBeBilled)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            var expenseDisbursementIds = disbursements.stream()
                    .map(Disbursement::getId)
                    .toList();

            var expenseDto = ExpenseDto.builder()
                    .activityId(activityId)
                    .expenseCode(expenseCode)
                    .quantity(1L)
                    .currency(billingAccountCurrency)
                    .unitPrice(totalAmountOfExpenseCode)
                    .expenseDate(billingPeriodEnds)
                    .isBillingApproved(true)
                    .disbursements(expenseDisbursementIds)
                    .build();

            expenseService.save(expenseDto);
        });

        var unbilledDisbursements = getUnbilledDisbursements(disbursementIds, billingPeriodEnds);
        unbilledDisbursements.forEach(disbursement -> disbursement.setExpenseDate(billingPeriodEnds));
        repository.saveAll(unbilledDisbursements);
        sendUpdateTransferEvent(unbilledDisbursements);
    }

    private String getBillingAccountCurrency(BillingAccount billingAccount) {
        var currencyDisbursement = billingAccount.getCurrencyDisbursement();
        if (StringUtils.isNotBlank(currencyDisbursement)) {
            return currencyDisbursement;
        }
        return billingAccount.getCurrency();
    }

    private BigDecimal getExchangeRate(LocalDate disbursementDate, String currency, String currencyToBeInvoiced) {
        if (currency.equals(currencyToBeInvoiced)) {
            return BigDecimal.ONE;
        }

        var exchangeRate = paramCommandClient.getLatestExchangeRate(currency, currencyToBeInvoiced, disbursementDate);
        if (exchangeRate == null) {
            throw new ExchangeRateNotFoundException();
        }
        return exchangeRate.getPayload().getExchangeRate();
    }

    private void validateDisbursementsApproval(List<Disbursement> unbilledDisbursements) {
        var notAllDisbursementsApproved = unbilledDisbursements.stream()
                .anyMatch(disbursement -> BooleanUtils.isNotTrue(disbursement.getIsBillingApproved()));
        if (notAllDisbursementsApproved) {
            throw new DisbursementBillingApproveException();
        }
    }

    private List<Disbursement> getUnbilledDisbursements(List<Long> disbursementIds, LocalDate billingPeriodEnds) {
        var toBeBilledSpecification = DisbursementToBeBilledSpecification.builder()
                .ids(disbursementIds)
                .billingPeriodEnds(billingPeriodEnds)
                .build();
        return repository.findAll(toBeBilledSpecification);
    }

    private List<Disbursement> getUnbilledDisbursements(List<Long> disbursementIds) {
        return repository.findByIdInAndExpenseDateIsNullAndToBeBilled(disbursementIds, ToBeBilledChoice.YES);
    }

    @Override
    public DisbursementPageDto getToBeBilledDisbursements(DisbursementFilterRequest filterRequest, int page, int size) {
        var toBeBilledSpecification = DisbursementToBeBilledSpecification.builder()
                .ids(filterRequest.getIds())
                .billingPeriodEnds(filterRequest.getBillingPeriodEnds())
                .sortDirection(filterRequest.getSortDirection())
                .sortField(filterRequest.getSortField())
                .build();
        var disbursementPage = repository.findAll(toBeBilledSpecification, PageRequest.of(page, size));
        return getDisbursementPageDto(disbursementPage);
    }

    @Override
    public DisbursementPageDto getBilledDisbursements(DisbursementFilterRequest filterRequest, int page, int size) {
        var disbursementPage = repository.findByIdInAndExpenseDateBeforeOrderByDisbursementDateDesc(filterRequest.getIds(),
                filterRequest.getBillingPeriodEnds(), PageRequest.of(page, size));
        return getDisbursementPageDto(disbursementPage);
    }

    private DisbursementPageDto getDisbursementPageDto(Page<Disbursement> disbursementPage) {
        return DisbursementPageDto.builder()
                .disbursements(disbursementPage.getContent()
                        .stream()
                        .map(this::toDto)
                        .toList())
                .totalElements(disbursementPage.getTotalElements())
                .totalPages(disbursementPage.getTotalPages())
                .build();
    }


    public List<Disbursement> getToBeBilledDisbursements(List<Long> disbursementIds, LocalDate billingPeriodEnds) {
        return getUnbilledDisbursements(disbursementIds, billingPeriodEnds).stream()
                .filter(disbursement -> disbursement.getToBeBilled() != null
                        && ToBeBilledChoice.YES.equals(disbursement.getToBeBilled()))
                .toList();
    }

    @Override
    public DisbursementPageDto getByIds(List<Long> disbursementIds, int page, int size) {
        var disbursementPage = repository.findByIdInOrderByDisbursementDateDesc(disbursementIds, PageRequest.of(page, size));
        return DisbursementPageDto.builder()
                .disbursements(disbursementPage.getContent()
                        .stream()
                        .map(this::toDto)
                        .toList())
                .totalElements(disbursementPage.getTotalElements())
                .totalPages(disbursementPage.getTotalPages())
                .build();
    }

    @Override
    public void delete(Long id, Long version) {
        var disbursement = getById(id).toBuilder()
                .version(version)
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(disbursement);
        sendTransferEvent(id, TransferType.DELETE);
    }

    private Disbursement getById(Long id) {
        return repository.findById(id)
                .orElseThrow(DisbursementNotFoundException::new);
    }

    public static String prepareDocumentName(String fileName) {
        return  "m" + UUID.randomUUID()+"."+ FilenameUtils.getExtension(fileName);
    }

    private DisbursementDto toDto(Disbursement disbursement) {
        var dto = mapper.toDisbursementDto(disbursement);
        var url = storageService.generateSas(mapper.toStorageFile(dto));
        dto.setFileSignedUrl(url);
        return dto;
    }

    private void sendUpdateTransferEvent(List<Disbursement> disbursements) {
        disbursements.forEach(disbursement ->
                sendTransferEvent(disbursement.getId(), TransferType.UPDATE));
    }

    private void sendTransferEvent(Long id, TransferType transferType) {
        producerService.sendTransfer(TransferEvent.builder()
                .type(transferType)
                .domainObjectId(id)
                .domain(Domain.IPMS_DISBURSEMENT)
                .build());
    }

    @Override
    public Boolean isBillingApprovableDisbursements(Long activityId, LocalDate billingPeriodEnds) {
        var disbursementIds = repository.findByActivityId(activityId).stream()
                .map(Disbursement::getId)
                .toList();
        var filterRequest = DisbursementFilterRequest.builder();

        filterRequest.ids(disbursementIds);
        filterRequest.billingPeriodEnds(billingPeriodEnds);

        if(!disbursementIds.isEmpty()) {
            var disbursements = getToBeBilledDisbursements(filterRequest.build(), 0, disbursementIds.size());
            for(DisbursementDto disbursement: disbursements.getDisbursements()) {
                if(disbursement.getIsBillingApproved() == null || !disbursement.getIsBillingApproved()) {
                    return false;
                }
            }
        }
        return true;
    }

    @Override
    public Boolean isBillingCancellableDisbursements(Long activityId) {
        var activity = activityClient.getActivity(activityId).getPayload();
        var disbursementIds = repository.findByActivityId(activityId).stream()
                .map(Disbursement::getId)
                .toList();

        if (!disbursementIds.isEmpty()) {
            var toBeBilledSpecification = DisbursementToBeBilledSpecification.builder()
                    .ids(disbursementIds)
                    .billingPeriodEnds(activity.getBillingPeriodEnds())
                    .toBeBilled(ToBeBilledChoice.YES)
                    .build();
            return repository.findAll(toBeBilledSpecification).isEmpty();
        }
        return true;
    }

    @Override
    public Boolean hasBillable(List<Long> disbursementIds, List<Long> expenseIds) {
        return isBillable(disbursementIds) || expenseService.isBillable(expenseIds);
    }

    private Boolean isBillable(List<Long> disbursementIds) {
        return !getUnbilledDisbursements(disbursementIds).isEmpty();
    }

    @Override
    public boolean isTotalAmountMoreThanBillingAmount(List<Long> disbursementIds, List<Long> expenseIds) {
        var currency = getParamValue(defaultCurrencyParam);
        var billingAmount = getParamValue(autoBillingAmountParam);
        var totalAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(disbursementIds)) {
            var totalDisbursementAmount = getUnbilledDisbursements(disbursementIds).stream()
                    .map(disbursement -> {
                        var amount = disbursement.getAmount();
                        var exchangeRate = getExchangeRate(LocalDate.now(), disbursement.getCurrency(),
                                currency);
                        return amount.multiply(exchangeRate);
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalAmount = totalAmount.add(totalDisbursementAmount);
        }

        if (CollectionUtils.isNotEmpty(expenseIds)) {
            var totalExpenseAmount = expenseService.getUnbilledExpenses(expenseIds).stream()
                    .map(expense -> {
                        var amount = expense.getUnitPrice().multiply(BigDecimal.valueOf(expense.getQuantity()));
                        var exchangeRate = getExchangeRate(LocalDate.now(), expense.getCurrency(), currency);
                        return amount.multiply(exchangeRate);
                    })
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalAmount = totalAmount.add(totalExpenseAmount);
        }

        return totalAmount.compareTo(NumberUtils.parseBigDecimal(billingAmount)) > 0;
    }

    @Override
    public boolean isTotalAmountMoreThanZero(List<Long> disbursementIds, List<Long> expenseIds) {
        var totalAmount = BigDecimal.ZERO;
        if (CollectionUtils.isNotEmpty(disbursementIds)) {
            var totalDisbursementAmount = getUnbilledDisbursements(disbursementIds).stream()
                    .map(Disbursement::getAmount)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalAmount = totalAmount.add(totalDisbursementAmount);
        }

        if (CollectionUtils.isNotEmpty(expenseIds)) {
            var totalExpenseAmount = expenseService.getUnbilledExpenses(expenseIds).stream()
                    .map(expense -> expense.getUnitPrice().multiply(BigDecimal.valueOf(expense.getQuantity())))
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            totalAmount = totalAmount.add(totalExpenseAmount);
        }

        return totalAmount.compareTo(BigDecimal.ZERO) > 0;
    }

    private String getParamValue(String key) {
        return parameterClient.getByKey(key).getPayload().getValue();
    }

    @Transactional
    @Override
    public void undoBillingApproval(List<Long> disbursementIds, LocalDate billingPeriodEnds) {
        var approvedDisbursements = findApprovedDisbursementsForPeriod(disbursementIds, billingPeriodEnds);
        clearExpenseDates(approvedDisbursements);
        deleteAssociatedExpenses(approvedDisbursements);
        saveDisbursements(approvedDisbursements);
        sendUpdateTransferEvent(approvedDisbursements);
    }

    private List<Disbursement> findApprovedDisbursementsForPeriod(List<Long> disbursementIds, LocalDate billingPeriodEnds) {
        return repository.findByIdIn(disbursementIds).stream()
                .filter(disbursement -> disbursement.getExpenseDate() != null
                        && billingPeriodEnds.equals(disbursement.getExpenseDate()))
                .toList();
    }

    private void clearExpenseDates(List<Disbursement> disbursements) {
        disbursements.forEach(disbursement -> disbursement.setExpenseDate(null));
    }

    private void deleteAssociatedExpenses(List<Disbursement> disbursements) {
        var disbursementIds = disbursements.stream()
                .map(Disbursement::getId)
                .toList();
        expenseService.deleteByDisbursementIds(disbursementIds);
    }

    private void saveDisbursements(List<Disbursement> disbursements) {
        repository.saveAll(disbursements);
    }

    @Override
    public boolean hasBillableDisbursementByBillingPeriodEnds(List<Long> disbursementIds, LocalDate billingPeriodEnds) {
        return !getToBeBilledDisbursements(disbursementIds, billingPeriodEnds).isEmpty();
    }

    @Override
    public boolean hasUnapprovedDisbursement(List<Long> disbursementIds, LocalDate billingPeriodEnds) {
        return getToBeBilledDisbursements(disbursementIds, billingPeriodEnds).stream()
                .anyMatch(disbursement -> BooleanUtils.isNotTrue(disbursement.getIsBillingApproved()));
    }

    @Override
    public void cancelBillingOrder(List<Long> disbursements) {
        var disbursementList = repository.findByIdIn(disbursements);
        disbursementList.forEach(disbursement -> disbursement.setExpenseDate(null));
        repository.saveAll(disbursementList);
        sendUpdateTransferEvent(disbursementList);
    }

    @Override
    public void updateDisbursementsForBillingCancellation(List<Long> ids, LocalDate billingPeriodEnds) {
        var disbursementsToUpdate = findDisbursementsForCancellation(ids, billingPeriodEnds);
        var today = LocalDate.now();
        disbursementsToUpdate.forEach(disbursement -> disbursement.setExpenseDate(today));
        repository.saveAll(disbursementsToUpdate);
        sendUpdateTransferEvent(disbursementsToUpdate);
    }

    @NotNull
    private List<Disbursement> findDisbursementsForCancellation(List<Long> ids, LocalDate billingPeriodEnds) {
        return getUnbilledDisbursements(ids, billingPeriodEnds).stream()
                .filter(disbursement -> BooleanUtils.isTrue(disbursement.getIsBillingApproved())
                        && !ToBeBilledChoice.YES.equals(disbursement.getToBeBilled())
                        && disbursement.getExpenseDate() == null)
                .toList();
    }

    @Override
    public List<DisbursementDto> getByActivityId(Long activityId) {
        return repository.findByActivityId(activityId).stream()
                .map(mapper::toDisbursementDto)
                .toList();
    }
}
