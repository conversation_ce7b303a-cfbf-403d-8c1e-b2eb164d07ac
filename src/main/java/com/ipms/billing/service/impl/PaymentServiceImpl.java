package com.ipms.billing.service.impl;

import com.ipms.billing.dto.*;
import com.ipms.billing.enums.PaymentStatus;
import com.ipms.billing.exception.PaymentNotFoundException;
import com.ipms.billing.mapper.PaymentMapper;
import com.ipms.billing.model.Payment;
import com.ipms.billing.repository.PaymentRepository;
import com.ipms.billing.service.PaymentService;
import com.ipms.billing.specification.PaymentSpecification;
import lombok.RequiredArgsConstructor;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.util.*;
import java.util.stream.Collectors;

@Service
@RequiredArgsConstructor
public class PaymentServiceImpl implements PaymentService {

    private final PaymentRepository repository;
    private final PaymentMapper mapper;

    @Override
    public PaymentDto save(PaymentDto dto) {
        var payment = mapper.toPayment(dto);
        var saved = repository.save(payment);
        return mapper.toPaymentDto(saved);
    }

    @Override
    public Payment getPaymentById(Long id) {
        return repository.findById(id)
                .orElseThrow(PaymentNotFoundException::new);
    }

    @Override
    public PaymentDto getByAccrualNumber(String accrualNumber) {
        return repository.findByAccrualNumberContainsIgnoreCase(accrualNumber)
                .map(mapper::toPaymentDto)
                .orElse(null);
    }

    @Override
    public PaymentPageDto getAll(PaymentFilterRequest filterRequest, int page, int size) {
        var paymentPage = repository.findAll(mapSpecification(filterRequest), PageRequest.of(page, size));
        return PaymentPageDto.builder()
                .payments(paymentPage.getContent()
                        .stream()
                        .map(mapper::toPaymentDto)
                        .toList())
                .totalElements(paymentPage.getTotalElements())
                .totalPages(paymentPage.getTotalPages())
                .build();
    }

    private PaymentSpecification mapSpecification(PaymentFilterRequest filterRequest) {
        return PaymentSpecification.builder()
                .issuerIds(filterRequest.getIssuerIds())
                .matterIds(filterRequest.getMatterIds())
                .accrualNumbers(filterRequest.getAccrualNumbers())
                .statuses(filterRequest.getStatuses())
                .sortField(filterRequest.getSortField())
                .sortDirection(filterRequest.getSortDirection())
                .build();
    }

    @Override
    public PaymentGroupPageDto getAllGrouped(PaymentFilterRequest filterRequest, int page, int size) {
        List<Payment> allPayments = repository.findAll(mapSpecification(filterRequest));

        List<PaymentGroupDto> paymentGroups = createPaymentGroups(allPayments);

        return createPagedResult(paymentGroups, page, size);
    }

    private List<PaymentGroupDto> createPaymentGroups(List<Payment> payments) {
        return payments.stream()
                .map(mapper::toPaymentDto)
                .collect(Collectors.groupingBy(dto ->
                        Optional.ofNullable(dto.getAccrualNumber()).orElse("NULL_ACCRUAL")))
                .entrySet()
                .stream()
                .map(this::mapToPaymentGroupDto)
                .sorted(Comparator.comparing(PaymentGroupDto::getAccrualNumber))
                .toList();
    }

    private PaymentGroupDto mapToPaymentGroupDto(Map.Entry<String, List<PaymentDto>> entry) {
        return PaymentGroupDto.builder()
                .accrualNumber(entry.getKey())
                .payments(entry.getValue())
                .build();
    }

    private PaymentGroupPageDto createPagedResult(List<PaymentGroupDto> paymentGroups, int page, int size) {
        int totalGroups = paymentGroups.size();
        List<PaymentGroupDto> pagedGroups = getPagedGroups(paymentGroups, page, size, totalGroups);

        return PaymentGroupPageDto.builder()
                .paymentGroups(pagedGroups)
                .totalElements((long) totalGroups)
                .totalPages(calculateTotalPages(totalGroups, size))
                .build();
    }

    private List<PaymentGroupDto> getPagedGroups(List<PaymentGroupDto> paymentGroups, int page, int size, int totalGroups) {
        int fromIndex = page * size;
        int toIndex = Math.min(fromIndex + size, totalGroups);

        return fromIndex < totalGroups ?
                paymentGroups.subList(fromIndex, toIndex) :
                Collections.emptyList();
    }

    private int calculateTotalPages(int totalGroups, int size) {
        return (int) Math.ceil((double) totalGroups / size);
    }

    @Override
    public PaymentDto update(PaymentDto dto, Long id, Long version) {
        var payment = mapper.toPaymentFromDto(dto, getPaymentById(id).toBuilder().build());
        payment.setVersion(version);
        var updated = repository.save(payment);
        return mapper.toPaymentDto(updated);
    }

    @Override
    @Transactional
    public void bulkUpdateStatus(List<Long> paymentIds, PaymentStatus newStatus) {
        var payments = repository.findAllById(paymentIds);

        for (Payment payment : payments) {
            payment.setStatus(newStatus);
        }

        repository.saveAll(payments);
    }


    @Override
    public void deleteUnapprovedPayments() {
        List<Payment> paymentsToDelete = repository.findByStatusIn(
                List.of(PaymentStatus.WAITING_FOR_APPROVAL, PaymentStatus.PENDING_PAYMENT)
        );

        if (!paymentsToDelete.isEmpty()) {
            paymentsToDelete.forEach(payment -> payment.setDeleted(true));
            repository.saveAll(paymentsToDelete);
        }
    }
}
