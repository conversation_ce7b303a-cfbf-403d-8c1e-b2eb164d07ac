package com.ipms.billing.service.impl;

import com.ipms.billing.client.ActivityClient;
import com.ipms.billing.client.MatterClient;
import com.ipms.billing.client.ParamCommandClient;
import com.ipms.billing.client.ParameterClient;
import com.ipms.billing.dto.*;
import com.ipms.billing.enums.ApprovalType;
import com.ipms.billing.enums.TimesheetResponseCode;
import com.ipms.billing.exception.TimesheetNotFoundException;
import com.ipms.billing.exception.TimesheetValidationException;
import com.ipms.billing.mapper.TimesheetMapper;
import com.ipms.billing.model.Timesheet;
import com.ipms.billing.repository.TimesheetRepository;
import com.ipms.billing.service.BillingAccountExpenseCodeService;
import com.ipms.billing.service.BillingAccountService;
import com.ipms.billing.service.ExpenseService;
import com.ipms.billing.service.TimesheetService;
import com.ipms.billing.validator.TimesheetValidator;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.config.security.utils.SecurityUtils;
import com.ipms.core.common.enums.Domain;
import com.ipms.core.common.enums.TransferType;
import com.ipms.core.common.model.TransferEvent;
import com.ipms.core.common.utils.ObjectUtils;
import com.ipms.core.entity.Revision;
import com.ipms.core.service.RevisionService;
import lombok.Builder;
import lombok.Data;
import lombok.RequiredArgsConstructor;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.BooleanUtils;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;

import javax.transaction.Transactional;
import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.time.LocalDateTime;
import java.util.*;
import java.util.function.Function;
import java.util.stream.Collectors;
import java.util.stream.Stream;
import java.util.stream.StreamSupport;

@RequiredArgsConstructor
@Service
public class TimesheetServiceImpl implements TimesheetService {

    public static final int MINUTES_IN_HOUR = 60;
    private final TimesheetRepository repository;
    private final TimesheetMapper mapper;
    private final ProducerService producerService;
    private final RevisionService<Timesheet> revisionService;
    private final ActivityClient activityClient;
    private final MatterClient matterClient;
    private final ParamCommandClient paramCommandClient;
    private final ParameterClient parameterClient;
    private final BillingAccountService billingAccountService;
    private final ExpenseService expenseService;
    private final BillingAccountExpenseCodeService billingAccountExpenseCodeService;
    private final TimesheetValidator timesheetValidator;

    @Value("${kafka.timesheet-changed-topic}")
    private String timesheetChangedTopic;

    @Override
    public TimesheetDto save(TimesheetDto dto) {
        var tsicPackages = getTsicPackagesForActivity(dto.getActivityId());
        timesheetValidator.validateTimesheetDate(dto, tsicPackages);
        var timesheet = repository.save(mapper.toTimesheet(dto));
        sendTransferEvent(timesheet.getId(), TransferType.INSERT);
        sendTimesheetChangedEvent(timesheet);
        return mapper.toTimesheetDto(timesheet);
    }

    @Override
    public TimesheetDto getById(Long id) {
        return mapper.toTimesheetDto(getTimesheetById(id));
    }

    @Override
    public TimesheetSummaryDto getAllByDates(LocalDate startDate, LocalDate endDate) {
        List<TimesheetDto> timesheetDtoList = getTimesheetDtoListByDate(startDate, endDate);
        var summary = getTimesheetTotalTimes(timesheetDtoList);
        return TimesheetSummaryDto
                .builder()
                .timesheetList(timesheetDtoList)
                .totalTimes(summary)
                .build();
    }

    @Override
    public TimesheetTotalTimeDto getAllByDates(Long activityId, LocalDate startDate, LocalDate endDate) {
        List<TimesheetDto> timesheetDtoList = getTimesheetDtoListByDate(activityId, startDate, endDate);
        var totalTimes = getTimesheetTotalTimes(timesheetDtoList);
        return TimesheetTotalTimeDto
                .builder()
                .totalBillableTime(totalTimes.getTotalBillableTime())
                .totalLoggedTime(totalTimes.getTotalLoggedTime())
                .build();
    }

    @Override
    public TimesheetPageDto getByActivity(Long activityId, int page, int size) {
        Page<Timesheet> timesheetPage = repository.findByActivityIdOrderByDateDesc(activityId, PageRequest.of(page, size));
        return TimesheetPageDto.builder()
                .timesheetList(mapper.convertToDtoList(timesheetPage.getContent()))
                .totalElements(timesheetPage.getTotalElements())
                .totalPages(timesheetPage.getTotalPages())
                .build();
    }

    @Override
    public TimesheetDto update(TimesheetDto dto) {
        var tsicPackages = getTsicPackagesForActivity(dto.getActivityId());
        timesheetValidator.validateTimesheetDate(dto, tsicPackages);
        var timesheetBeforeUpdate = getTimesheetById(dto.getId()).toBuilder().build();
        var timesheet = mapper.toTimesheetFromDto(dto, timesheetBeforeUpdate);
        var saved = repository.save(timesheet);
        sendTransferEvent(saved.getId(), TransferType.UPDATE);
        sendTimesheetChangedEvent(saved);
        return mapper.toTimesheetDto(saved);
    }

    @Override
    public void delete(Long id, Long version) {
        var timesheet = getTimesheetById(id).toBuilder()
                .version(version)
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(timesheet);
        sendTransferEvent(timesheet.getId(), TransferType.DELETE);
        sendTimesheetChangedEvent(timesheet);
    }

    private void sendTimesheetChangedEvent(List<Timesheet> timesheets) {
        timesheets.stream()
                .findAny()
                .ifPresent(this::sendTimesheetChangedEvent);
    }

    private void sendTimesheetChangedEvent(Timesheet timesheet) {
        producerService.send(timesheetChangedTopic, ObjectUtils.toJson(timesheet.getActivityId()));
    }

    @Override
    public List<TimesheetDto> getByIdIn(List<Long> ids) {
        return repository.findByIdIn(ids)
                .stream()
                .map(mapper::toTimesheetDto)
                .toList();
    }

    @Override
    public TimesheetTotalTimeDto getTimesByActivity(Long activityId) {
        var timesheetList = getTimesheetsByActivity(activityId);
        return getTotalTimesAsHours(timesheetList);
    }

    @Override
    public TimesheetTotalTimeDto getTimesByActivityAndStartDate(Long activityId, LocalDate startDate) {
        var timesheetList = repository.findByActivityIdAndExpenseDateIsNull(activityId).stream()
                .filter(ts -> !startDate.isAfter(ts.getDate()))
                .toList();
        return getTotalTimesAsHours(timesheetList);
    }

    @Override
    public TimesheetTotalTimeDto getTimesByActivityAndStartDateForFds(Long activityId, LocalDate startDate) {
        var timesheetList1 = repository.findByActivityIdAndExpenseDateIsNull(activityId).stream()
                .filter(ts -> !startDate.isAfter(ts.getDate()))
                .toList();
        var timesheetList2 = repository.findByActivityIdAndExpenseDateIsNotNullAndIncludedInChargeIsTrue(activityId).stream()
                .filter(ts -> !startDate.isAfter(ts.getDate()))
                .toList();
        return getTotalTimesAsHours(Stream.concat(timesheetList1.stream(), timesheetList2.stream()).toList());
    }

    @Override
    public TimesheetTotalTimeDto getTimesByActivityAndEndDate(Long activityId, LocalDate endDate) {
        var timesheetList = repository.findByActivityIdAndExpenseDateIsNull(activityId).stream()
                .filter(ts -> !endDate.isBefore(ts.getDate()))
                .toList();
        return getTotalTimesAsHours(timesheetList);
    }

    private TimesheetTotalTimeDto getTotalTimesAsHours(List<Timesheet> timesheetList) {
        var timesheetDtoList = mapper.convertToDtoList(timesheetList);
        var totalTimes = getTimesheetTotalTimes(timesheetDtoList);
        convertMinutesToHours(totalTimes);
        return totalTimes;
    }

    @Override
    public TimesheetTotalTimeDto getUnbilledTimesByActivity(Long activityId) {
        var timesheetDtoList = getUnbilledTimesheetsByActivityId(activityId);
        return getTimesheetTotalTimesByActivityId(timesheetDtoList, activityId);
    }

    @Override
    public List<TimesheetDto> getUnbilledTimesheetsByActivityId(Long activityId) {
        var timesheetList = repository.findByActivityIdAndExpenseDateIsNull(activityId);
        return mapper.convertToDtoList(timesheetList);
    }

    @Override
    public TimesheetTotalTimeDto getUnbilledTimes(Long activityId, LocalDate billingPeriodEnds) {
        var timesheetDtoList = getUnbilledTimesheets(activityId, billingPeriodEnds).stream()
                .map(mapper::toTimesheetDto)
                .toList();
        var totalAmount = calculateTotalAmount(activityId);
        var totalTimeDto = getTimesheetTotalTimesByActivityId(timesheetDtoList, activityId);
        totalTimeDto.setTotalAmount(totalAmount);
        return totalTimeDto;
    }

    public TimesheetPageDto getUnbilledTimesheetsPage(Long activityId, LocalDate billingPeriodEnds,
                                                       int page, int size) {
        var unbilledTimesheets = getUnbilledTimesheetDtos(activityId, billingPeriodEnds);
        return convertToPageDto(page, size, unbilledTimesheets);
    }

    public List<TimesheetDto> getUnbilledTimesheetDtos(Long activityId, LocalDate billingPeriodEnds) {
        var timesheetsBeforePeriod = getUnbilledTimesheets(activityId, billingPeriodEnds);
        var timesheetsAfterPeriod = getUnbilledTimesheetsAfterPeriod(activityId, billingPeriodEnds);
        return combineTimesheets(timesheetsBeforePeriod, timesheetsAfterPeriod);
    }

    private TimesheetPageDto convertToPageDto(long page, int size, List<TimesheetDto> unbilledTimesheets) {

        var paginatedTimesheetDtos = unbilledTimesheets.stream()
                .skip(page * size)
                .limit(size)
                .toList();

        int totalSize = unbilledTimesheets.size();
        int totalPages = (int) Math.ceil((double) totalSize / size);

        return TimesheetPageDto.builder()
                .timesheetList(paginatedTimesheetDtos)
                .totalPages(totalPages)
                .totalElements(totalSize)
                .build();
    }

    private List<TimesheetDto> combineTimesheets(List<Timesheet> timesheetsBeforePeriod,
                                                 List<Timesheet> timesheetsAfterPeriod) {
        var beforePeriodDtos = timesheetsBeforePeriod.stream()
                .map(timesheet -> {
                    var dto = mapper.toTimesheetDto(timesheet);
                    dto.setAfterPeriod(false);
                    return dto;
                })
                .toList();

        var afterPeriodDtos = timesheetsAfterPeriod.stream()
                .map(timesheet -> {
                    var dto = mapper.toTimesheetDto(timesheet);
                    dto.setAfterPeriod(true);
                    return dto;
                })
                .toList();

        return Stream.concat(beforePeriodDtos.stream(), afterPeriodDtos.stream())
                .toList();
    }

    @Override
    public TimesheetTotalTimeDto getOtherUnbilledTimes(Long activityId, LocalDate billingPeriodEnds) {
        var activityIds = activityClient.getOtherActivityIds(activityId).getPayload();
        var timesheetDtoList = repository.findByActivityIdInAndExpenseDateIsNullAndDateLessThanEqual(
                        activityIds,
                        billingPeriodEnds
                ).stream()
                .map(mapper::toTimesheetDto)
                .toList();
        return getTimesheetTotalTimes(timesheetDtoList);
    }

    @Override
    public TimesheetPageDto getOtherUnbilledPage(Long activityId, LocalDate billingPeriodEnds,
                                                    int page, int size) {
        var activityIds = activityClient.getOtherActivityIds(activityId).getPayload();
        var timesheetPage = repository.findByActivityIdInAndExpenseDateIsNullAndDateLessThanEqualOrderByDateDesc(
                activityIds,
                billingPeriodEnds,
                PageRequest.of(page, size)
        );
        return TimesheetPageDto.builder()
                .timesheetList(timesheetPage.getContent()
                        .stream()
                        .map(mapper::toTimesheetDto)
                        .toList())
                .totalElements(timesheetPage.getTotalElements())
                .totalPages(timesheetPage.getTotalPages())
                .build();
    }

    @Override
    public List<Timesheet> getUnbilledTimesheets(Long activityId, LocalDate billingPeriodEnds) {
        var tsicPackages = getTsicPackagesForActivity(activityId);
        if (CollectionUtils.isEmpty(tsicPackages)) {
            return getUnbilledTimesheetsByBillingPeriodEnds(activityId, billingPeriodEnds);
        }

        var tsicPackageOptional = getTsicPackageWithEndDateIsAfterBillingPeriodEnds(billingPeriodEnds, tsicPackages);
        if (tsicPackageOptional.isPresent()) {
            var tsicPackage = tsicPackageOptional.get();
            return getUnbilledTimesheetsByBillingPeriodEnds(activityId, billingPeriodEnds).stream()
                    .filter(timesheet -> !tsicPackage.getStartDate().isAfter(timesheet.getDate()))
                    .sorted(Comparator.comparing(Timesheet::getDate).reversed())
                    .toList();
        }

        tsicPackageOptional = getTsicPackageWithEndDateIsBeforeBillingPeriodEnds(billingPeriodEnds, tsicPackages);
        if (tsicPackageOptional.isPresent()) {
            var tsicPackage = tsicPackageOptional.get();
            return getUnbilledTimesheetsByBillingPeriodEnds(activityId, billingPeriodEnds).stream()
                    .filter(timesheet -> timesheet.getDate().isAfter(tsicPackage.getEndDate()))
                    .sorted(Comparator.comparing(Timesheet::getDate).reversed())
                    .toList();
        }

        return Collections.emptyList();
    }

    private List<Timesheet> getUnbilledTimesheetsByBillingPeriodEnds(Long activityId, LocalDate billingPeriodEnds) {
        return getTimesheetsByActivity(activityId).stream()
                .filter(timesheet -> !billingPeriodEnds.isBefore(timesheet.getDate())
                        && isExpenseDateNullOrEqualTo(timesheet.getExpenseDate(), billingPeriodEnds))
                .sorted(Comparator.comparing(Timesheet::getDate).reversed())
                .toList();
    }

    private List<TSICPackageDto> getTsicPackagesForActivity(Long activityId) {
        return activityClient.getActivity(activityId).getPayload().getTsicPackages();
    }

    private Optional<TSICPackageDto> getTsicPackageWithEndDateIsAfterBillingPeriodEnds(
            LocalDate billingPeriodEnds,
            List<TSICPackageDto> tsicPackages) {
        return tsicPackages.stream()
                .filter(tsicPackage ->
                        tsicPackage.getStartDate().isBefore(billingPeriodEnds)
                                && isEndDateAfterBillingPeriodEnds(billingPeriodEnds, tsicPackage.getEndDate()))
                .max(Comparator.comparing(TSICPackageDto::getStartDate));
    }

    private boolean isEndDateAfterBillingPeriodEnds(LocalDate billingPeriodEnds, LocalDate endDate) {
        return endDate == null || endDate.isAfter(billingPeriodEnds);
    }

    private Optional<TSICPackageDto> getTsicPackageWithEndDateIsBeforeBillingPeriodEnds(
            LocalDate billingPeriodEnds,
            List<TSICPackageDto> tsicPackages) {
        return tsicPackages.stream()
                .filter(tsicPackage -> tsicPackage.getStartDate().isBefore(billingPeriodEnds)
                        && tsicPackage.getEndDate().isBefore(billingPeriodEnds))
                .max(Comparator.comparing(TSICPackageDto::getStartDate));
    }

    @Override
    public List<Timesheet> getUnbilledTimesheetsAfterPeriod(Long activityId, LocalDate billingPeriodEnds) {
        return repository.findByActivityId(activityId).stream()
                .filter(timesheet -> timesheet.getDate().isAfter(billingPeriodEnds)
                        && isExpenseDateNullOrEqualTo(timesheet.getExpenseDate(), billingPeriodEnds))
                .sorted(Comparator.comparing(Timesheet::getDate))
                .toList();
    }

    @Override
    public List<Revision> getRevisions(Long id) {
        return revisionService.getAll(Timesheet.class, id);
    }

    @Override
    public List<Revision> getRevisionsByField(Long id, String field) {
        return revisionService.getByField(Timesheet.class, field, id);
    }

    @Override
    public TimesheetPageDto getPage(TimesheetFilterRequest filterRequest, int page, int size) {
        prepareFiltersForQuery(filterRequest);
        var timesheetPage = repository.findAll(mapper.toSpecification(filterRequest), PageRequest.of(page, size));
        return TimesheetPageDto.builder()
                .timesheetList(mapper.convertToDtoList(timesheetPage.getContent()))
                .totalElements(timesheetPage.getTotalElements())
                .totalPages(timesheetPage.getTotalPages())
                .build();
    }

    @Override
    public List<TimesheetDto> getAll(TimesheetFilterRequest filterRequest) {
        prepareFiltersForQuery(filterRequest);
        var timesheets = repository.findAll(mapper.toSpecification(filterRequest));
        return mapper.convertToDtoList(timesheets);
    }

    private void prepareFiltersForQuery(TimesheetFilterRequest filterRequest) {
        setApprovalFilters(filterRequest);
        setMatterIdFilter(filterRequest);
    }

    private void setApprovalFilters(TimesheetFilterRequest filterRequest) {
        if (filterRequest.getApprovalType() != null) {
            setActivityIdsFilter(filterRequest);
        }
    }

    private void setActivityIdsFilter(TimesheetFilterRequest filterRequest) {
        var manuelApprovalActivityIds = activityClient.getTimesheetManuelApprovalActivityIds().getPayload();
        if (ApprovalType.MANUEL.equals(filterRequest.getApprovalType())) {
            Optional.ofNullable(filterRequest.getActivityIds())
                    .ifPresent(manuelApprovalActivityIds::retainAll);
            filterRequest.setActivityIds(manuelApprovalActivityIds);
        } else if (ApprovalType.SYSTEM.equals(filterRequest.getApprovalType())) {
            filterRequest.setActivityIdsNotIn(manuelApprovalActivityIds);
        }
    }

    private void setMatterIdFilter(TimesheetFilterRequest filterRequest) {
        if (haveMatterSpecificFilters(filterRequest)) {
            var matterIds = matterClient.getMatterIds(filterRequest.getPartners(),
                            filterRequest.getTimespentApprovals(),
                            filterRequest.getMatterIds())
                    .getPayload();
            filterRequest.setMatterIds(matterIds);
        }
    }

    private boolean haveMatterSpecificFilters(TimesheetFilterRequest filterRequest) {
        return CollectionUtils.isNotEmpty(filterRequest.getTimespentApprovals())
                || CollectionUtils.isNotEmpty(filterRequest.getPartners());
    }

    @Override
    public List<TimesheetDto> approveBulk(List<Long> ids) {
        var timesheetList = repository.findByIdIn(ids);
        timesheetList.forEach(timesheet -> timesheet.setIsApproved(Boolean.TRUE));
        var timesheetDtos = StreamSupport.stream(repository.saveAll(timesheetList).spliterator(), false)
                .map(mapper::toTimesheetDto)
                .toList();
        sendUpdateTransferEvent(timesheetDtos);
        return timesheetDtos;
    }

    @Override
    public List<TimesheetDto> approveBillingBulk(List<Long> ids) {
        var timesheetList = repository.findByIdIn(ids);
        timesheetList.forEach(timesheet -> timesheet.setIsBillingApproved(Boolean.TRUE));
        var timesheetDtos = StreamSupport.stream(repository.saveAll(timesheetList).spliterator(), false)
                .map(mapper::toTimesheetDto)
                .toList();
        sendUpdateTransferEvent(timesheetDtos);
        return timesheetDtos;
    }

    @Override
    public List<TimesheetDto> cancelApprovelBillingBulk(List<Long> ids) {
        var timesheetList = repository.findByIdIn(ids);
        timesheetList.forEach(timesheet -> timesheet.setIsBillingApproved(Boolean.FALSE));
        var timesheetDtos = StreamSupport.stream(repository.saveAll(timesheetList).spliterator(), false)
                .map(mapper::toTimesheetDto)
                .toList();
        sendUpdateTransferEvent(timesheetDtos);
        return timesheetDtos;
    }

    @Transactional
    @Override
    public void approveBilling(ApproveBillingRequest approveBillingRequest) {
        var activityId = approveBillingRequest.getActivityId();
        var activityResponse = activityClient.getActivity(activityId);
        var billingAccountId = activityResponse.getPayload().getBillingAccountId();
        var currency =  billingAccountService.getById(billingAccountId).getCurrency();
        var billingPeriodEnds = activityResponse.getPayload().getBillingPeriodEnds();

        var unbilledTimesheetsWithIncludedInCharge = getUnbilledTimesheets(activityId, billingPeriodEnds);
        var unbilledTimesheets = unbilledTimesheetsWithIncludedInCharge.stream()
                .filter(timesheetDto -> BooleanUtils.isNotTrue(timesheetDto.getIncludedInCharge()))
                .toList();
        validateTimesheetsApproval(unbilledTimesheets);

        var expenseCodeBillableTime = unbilledTimesheets.stream()
                .collect(Collectors.groupingBy(Timesheet::getExpenseCode));
        var billableQuantities = getBillableQuantities(expenseCodeBillableTime, currency).stream()
                .filter(billableQuantity -> billableQuantity.getQuantity() > 0);

        billableQuantities.forEach(billableQuantity -> {
            var billingAccountExpenseCodeOptional = getBillingAccountExpenseCode(billingAccountId, billableQuantity.getExpenseCode());
            var expenseCode = billingAccountExpenseCodeOptional
                    .map(BillingAccountExpenseCodeDto::getSpecialExpenseCode)
                    .orElse(billableQuantity.getExpenseCode());
            var unitPrice = billingAccountExpenseCodeOptional
                    .map(BillingAccountExpenseCodeDto::getUnitPrice)
                    .orElse(billableQuantity.getUnitPrice());
            var expenseDto = ExpenseDto.builder()
                    .activityId(activityId)
                    .expenseCode(expenseCode)
                    .quantity(billableQuantity.getQuantity())
                    .currency(currency)
                    .unitPrice(unitPrice)
                    .expenseDate(billingPeriodEnds)
                    .isBillingApproved(true)
                    .timesheets(billableQuantity.getTimesheetIds())
                    .build();
            expenseService.save(expenseDto);
        });
        unbilledTimesheetsWithIncludedInCharge.forEach(timesheet -> timesheet.setExpenseDate(billingPeriodEnds));
        repository.saveAll(unbilledTimesheetsWithIncludedInCharge);
        sendTimesheetChangedEvent(unbilledTimesheetsWithIncludedInCharge);
        sendUpdateTransferEventForTimesheets(unbilledTimesheetsWithIncludedInCharge);
    }

    private Optional<BillingAccountExpenseCodeDto> getBillingAccountExpenseCode(Long billingAccountId, String generalExpenseCode) {
        var billingAccountExpenseCodeFilterRequest = BillingAccountExpenseCodeFilterRequest.builder()
                .billingAccountId(billingAccountId)
                .generalExpenseCode(generalExpenseCode)
                .build();
        return billingAccountExpenseCodeService.getAll(billingAccountExpenseCodeFilterRequest).stream()
                .findFirst();

    }

    public TotalAmountDto calculateTotalAmount(Long activityId) {
        var activityResponse = activityClient.getActivity(activityId);
        var billingAccountCurrency = billingAccountService
                .getById(activityResponse.getPayload().getBillingAccountId())
                .getCurrency();

        var unbilledTimesheets = getUnbilledTimesheets(activityId, activityResponse.getPayload().getBillingPeriodEnds()).stream()
                .filter(timesheetDto -> BooleanUtils.isNotTrue(timesheetDto.getIncludedInCharge()))
                .toList();
        var expenseCodeBillableTime = unbilledTimesheets.stream()
                .collect(Collectors.groupingBy(Timesheet::getExpenseCode));

        var totalAmount = getBillableQuantities(expenseCodeBillableTime, billingAccountCurrency).stream()
                .map(billableQuantity ->
                        billableQuantity.getUnitPrice().multiply(BigDecimal.valueOf(billableQuantity.getQuantity())))
                .reduce(BigDecimal.ZERO, BigDecimal::add);

        return TotalAmountDto.builder()
                .totalAmount(totalAmount)
                .currency(billingAccountCurrency)
                .build();
    }

    public List<BillableQuantity> getBillableQuantities(Map<String, List<Timesheet>> expenseCodeBillableTime, String currency) {
        List<BillableQuantity> billableQuantities = new ArrayList<>();
        var divideMinutes = getExpenseCodeFor6Min();
        expenseCodeBillableTime.forEach((expenseCode, timesheets) -> {
            var billableTime = timesheets.stream()
                    .map(Timesheet::getBillableTime)
                    .filter(Objects::nonNull)
                    .reduce(BigDecimal.ZERO, BigDecimal::add);
            var timesheetIds = timesheets.stream()
                    .map(Timesheet::getId)
                    .toList();

            var quantity = billableTime.longValue() / MINUTES_IN_HOUR;
            var unitPrice = paramCommandClient.getUnitPrice(expenseCode, currency).getPayload();
            var billableQuantityForHours = BillableQuantity.builder()
                    .expenseCode(expenseCode)
                    .quantity(quantity)
                    .unitPrice(unitPrice)
                    .timesheetIds(timesheetIds)
                    .build();
            billableQuantities.add(billableQuantityForHours);
            var quantityOfMinutes = (billableTime.longValue() % MINUTES_IN_HOUR) / divideMinutes;
            var expenseCodeFor6 = expenseCode + "_" + divideMinutes;
            var unitPriceFor6 = paramCommandClient.getUnitPrice(expenseCodeFor6, currency).getPayload();
            if (quantityOfMinutes > 0) {
                var billableQuantityForMinutes = BillableQuantity.builder()
                        .expenseCode(expenseCodeFor6)
                        .quantity(quantityOfMinutes)
                        .unitPrice(unitPriceFor6)
                        .timesheetIds(timesheetIds)
                        .build();
                billableQuantities.add(billableQuantityForMinutes);
            }
        });

        return billableQuantities;
    }

    private Integer getExpenseCodeFor6Min() {
        var expenseCodeFor6Min = parameterClient.getByKey("EXPENSE_CODE_FOR_6_MIN").getPayload().getValue();
        return Integer.parseInt(expenseCodeFor6Min);
    }

    private void validateTimesheetsApproval(List<Timesheet> unbilledTimesheets) {
        var notAllTimesheetsApproved = unbilledTimesheets.stream()
                .anyMatch(timesheet -> timesheet.getIsBillingApproved() == null
                        || timesheet.getIsBillingApproved() == Boolean.FALSE);
        if (notAllTimesheetsApproved) {
            throw new TimesheetValidationException(TimesheetResponseCode.ALL_TIMESHEETS_MUST_BE_APPROVED);
        }
    }

    private void sendUpdateTransferEventForTimesheets(List<Timesheet> timesheets) {
        var timesheetDtos = timesheets.stream()
                .map(mapper::toTimesheetDto)
                .toList();
        sendUpdateTransferEvent(timesheetDtos);
    }

    private void sendUpdateTransferEvent(List<TimesheetDto> timesheetDtos) {
        timesheetDtos.forEach(timesheetDto -> sendTransferEvent(timesheetDto.getId(), TransferType.UPDATE));
    }

    @Override
    public void approveBySystem(LocalDate beforeDate) {
        var manuelApprovalActivityIds = activityClient.getTimesheetManuelApprovalActivityIds().getPayload();
        var timesheets = repository.findByActivityIdNotInAndDateBefore(manuelApprovalActivityIds, beforeDate).stream()
                .filter(timesheet -> timesheet.getIsApproved() == null || !timesheet.getIsApproved())
                .toList();
        timesheets.forEach(timesheet -> timesheet.setIsApproved(true));
        repository.saveAll(timesheets);
        timesheets.forEach(timesheet -> sendTransferEvent(timesheet.getId(), TransferType.UPDATE));
    }

    @Override
    public void calculateIncludedInCharge(Long activityId, LocalDate packageStartDate, BigDecimal tsicIncludedCharge) {
        var timesheets = repository.findByActivityId(activityId);
        var includedInChargeTimesheets = timesheets.stream()
                .filter(timesheet ->
                        BooleanUtils.isTrue(timesheet.getIncludedInCharge())
                                && !packageStartDate.isAfter(timesheet.getDate()))
                .map(mapper::toTimesheetDto)
                .toList();
        var totalBillableTime = countTotals(includedInChargeTimesheets, TimesheetDto::getBillableTime);
        var timesheetsNotIncludedInCharge = timesheets.stream()
                .filter(timesheet -> BooleanUtils.isNotTrue(timesheet.getIncludedInCharge())
                        && timesheet.getExpenseDate() == null)
                .toList();
        for (Timesheet timesheet : timesheetsNotIncludedInCharge) {
            var tsicIncludedChargeMinutes = tsicIncludedCharge.multiply(BigDecimal.valueOf(MINUTES_IN_HOUR));
            if (tsicIncludedChargeMinutes.compareTo(totalBillableTime.add(timesheet.getBillableTime())) >= 0) {
                timesheet.setIncludedInCharge(Boolean.TRUE);
                totalBillableTime = totalBillableTime.add(timesheet.getBillableTime());
            }

        }
        sendTimesheetChangedEvent(timesheets);
        repository.saveAll(timesheetsNotIncludedInCharge);
    }

    @Override
    public void changeActivity(TimesheetChangeActivityRequest changeActivityRequest) {
        var timesheets = repository.findByIdIn(changeActivityRequest.getTimesheetIds());
        timesheets.forEach(timesheet -> timesheet.setActivityId(changeActivityRequest.getTargetActivityId()));
        repository.saveAll(timesheets);
        sendTimesheetChangedEvent(timesheets);
        timesheets.forEach(timesheet -> sendTransferEvent(timesheet.getId(), TransferType.UPDATE));
    }

    private void convertMinutesToHours(TimesheetTotalTimeDto totalTimes) {
        if (!totalTimes.getTotalBillableTime().equals(BigDecimal.ZERO)) {
            totalTimes.setTotalBillableTime(totalTimes.getTotalBillableTime().divide(new BigDecimal(MINUTES_IN_HOUR), RoundingMode.HALF_UP));
        }
        if (!totalTimes.getTotalLoggedTime().equals(BigDecimal.ZERO)) {
            totalTimes.setTotalLoggedTime(totalTimes.getTotalLoggedTime().divide(new BigDecimal(MINUTES_IN_HOUR), RoundingMode.HALF_UP));
        }
        if (!totalTimes.getTotalIncludedInCharge().equals(BigDecimal.ZERO)) {
            totalTimes.setTotalIncludedInCharge(totalTimes.getTotalIncludedInCharge().divide(new BigDecimal(MINUTES_IN_HOUR), RoundingMode.HALF_UP));
        }
    }

    private Timesheet getTimesheetById(Long id) {
        return repository.findById(id)
                .orElseThrow(TimesheetNotFoundException::new);
    }

    private BigDecimal countTotals(List<TimesheetDto> timesheetList, Function<TimesheetDto, BigDecimal> mapping) {
        return timesheetList.stream()
                .map(mapping)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    private TimesheetTotalTimeDto getTimesheetTotalTimesByActivityId(List<TimesheetDto> unbilledTimesheets,
                                                                     Long activityId) {
        if (CollectionUtils.isEmpty(unbilledTimesheets)) {
            return TimesheetTotalTimeDto.builder().build();
        }
        var activity = activityClient.getActivity(activityId);
        var tsicPackageOptional = getActiveTsicPackage(activity.getPayload().getTsicPackages());
        if (tsicPackageOptional.isEmpty()) {
            return getTimesheetTotalTimes(unbilledTimesheets);
        }
        var timesheetTotalTimeDto = getTimesheetTotalTimes(unbilledTimesheets);
        var tsicPackage = tsicPackageOptional.get();
        timesheetTotalTimeDto.setFixedFee(tsicPackage.getFixedFee());
        var timesheetsByActivity = getTimesheetsByActivity(activityId);
        var tsicUsedBefore = timesheetsByActivity.stream()
                .filter(timesheetDto -> !timesheetDto.getDate().isBefore(tsicPackage.getStartDate())
                        && timesheetDto.getExpenseDate() != null
                        && timesheetDto.getExpenseDate().isBefore(activity.getPayload().getBillingPeriodEnds())
                        && timesheetDto.getIncludedInCharge() != null && timesheetDto.getIncludedInCharge())
                .map(Timesheet::getBillableTime)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        timesheetTotalTimeDto.setTsicUsedBefore(tsicUsedBefore);
        var tsicUsedInCurrentProcess = timesheetsByActivity.stream()
                .filter(timesheetDto -> !timesheetDto.getDate().isBefore(tsicPackage.getStartDate())
                        && isExpenseDateNullOrEqualTo(timesheetDto.getExpenseDate(), activity.getPayload().getBillingPeriodEnds())
                        && BooleanUtils.isTrue(timesheetDto.getIncludedInCharge()))
                .map(Timesheet::getBillableTime)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
        timesheetTotalTimeDto.setTsicUsedInCurrentProcess(tsicUsedInCurrentProcess);
        var tsicPackageIncludedChargeMin = tsicPackage.getIncludedCharge().multiply(BigDecimal.valueOf(MINUTES_IN_HOUR));
        timesheetTotalTimeDto.setTsicMin(tsicPackageIncludedChargeMin);
        var tsicRemaining = tsicPackageIncludedChargeMin
                .subtract(tsicUsedBefore).subtract(tsicUsedInCurrentProcess);
        timesheetTotalTimeDto.setTsicRemaining(tsicRemaining);
        return timesheetTotalTimeDto;
    }

    private List<Timesheet> getTimesheetsByActivity(Long activityId) {
        return repository.findByActivityId(activityId);
    }

    private Optional<TSICPackageDto> getActiveTsicPackage(List<TSICPackageDto> tsicPackages) {
        return tsicPackages.stream()
                .filter(tsicPackage -> tsicPackage.getEndDate() == null)
                .max(Comparator.comparing(TSICPackageDto::getStartDate));
    }

    private TimesheetTotalTimeDto getTimesheetTotalTimes(List<TimesheetDto> timesheetList) {
        var totalLoggedTime = countTotals(timesheetList, TimesheetDto::getLoggedTime);
        var totalBillableTime = countTotals(timesheetList, TimesheetDto::getBillableTime);
        var timesheetDtoListIncludedInCharge = timesheetList.stream()
                .filter(timesheetDto -> BooleanUtils.isTrue(timesheetDto.getIncludedInCharge()))
                .toList();
        var totalIncludedInCharge = countTotals(timesheetDtoListIncludedInCharge, TimesheetDto::getBillableTime);
        return TimesheetTotalTimeDto.builder()
                .totalLoggedTime(totalLoggedTime)
                .totalBillableTime(totalBillableTime)
                .totalIncludedInCharge(totalIncludedInCharge)
                .build();
    }

    private List<TimesheetDto> getTimesheetDtoListByDate(LocalDate startDate, LocalDate endDate) {
        var username = SecurityUtils.getCurrentUsername()
                .orElseThrow();
        return repository.findByCreatedByAndDateBetweenOrderByDateDesc(username, startDate, endDate)
                .stream()
                .map(mapper::toTimesheetDto)
                .toList();
    }

    private List<TimesheetDto> getTimesheetDtoListByDate(Long activityId, LocalDate startDate, LocalDate endDate) {
        return repository.findByActivityIdAndDateBetweenOrderByDateDesc(activityId, startDate, endDate)
                .stream()
                .map(mapper::toTimesheetDto)
                .toList();
    }

    private void sendTransferEvent(Long id, TransferType transferType) {
        producerService.sendTransfer(TransferEvent.builder()
                .type(transferType)
                .domainObjectId(id)
                .domain(Domain.IPMS_TIMESHEET)
                .build());
    }

    @Override
    public Boolean isBillingApprovableTimesheets(Long activityId, LocalDate billingPeriodEnds) {
        for (Timesheet timesheet : getUnbilledTimesheets(activityId, billingPeriodEnds)) {
            if (timesheet.getIsBillingApproved() == null || !timesheet.getIsBillingApproved()) {
                return false;
            }
        }
        return true;
    }

    @Override
    public Boolean hasBillableTimesheet(Long activityId, LocalDate billingPeriodEnds) {
        return getUnbilledTimesheetDtos(activityId, billingPeriodEnds).stream()
                .anyMatch(timesheet ->
                        BooleanUtils.isNotTrue(timesheet.getIncludedInCharge())
                                && BooleanUtils.isTrue(timesheet.getIsIncludeReport()
                                && timesheet.getExpenseDate() == null)
                );
    }

    @Override
    public void updateTimesheetsForBillingCancellation(Long activityId, LocalDate billingPeriodEnds) {
        var timesheetsToUpdate = getUnbilledTimesheets(activityId, billingPeriodEnds).stream()
                .filter(timesheet -> BooleanUtils.isTrue(timesheet.getIsBillingApproved())
                        && BooleanUtils.isTrue(timesheet.getIncludedInCharge())
                        && timesheet.getExpenseDate() == null)
                .toList();
        var today = LocalDate.now();
        timesheetsToUpdate.forEach(timesheet -> timesheet.setExpenseDate(today));
        repository.saveAll(timesheetsToUpdate);
        sendTimesheetChangedEvent(timesheetsToUpdate);
        sendUpdateTransferEventForTimesheets(timesheetsToUpdate);
    }

    @Transactional
    @Override
    public void undoBillingApprovalTimesheets(Long activityId, LocalDate billingPeriodEnds) {
        var timesheets = getTimesheetsByActivity(activityId).stream()
                .filter(timesheet -> timesheet.getExpenseDate() != null
                        && billingPeriodEnds.equals(timesheet.getExpenseDate()))
                .toList();

        if (!timesheets.isEmpty()) {
            timesheets.forEach(timesheet -> timesheet.setExpenseDate(null));
            repository.saveAll(timesheets);
            expenseService.deleteByTimesheetIds(timesheets.stream().map(Timesheet::getId).toList());
            sendTimesheetChangedEvent(timesheets);
        }
    }

    @Override
    public Boolean splitTimesheet(Long timesheetId, LocalDate billingPeriodEnds) {
        var timesheet = repository.findById(timesheetId).orElseThrow();

        var timesheetTotalTimeDto = getUnbilledTimes(timesheet.getActivityId(), billingPeriodEnds);
        var tsicRemaining = timesheetTotalTimeDto.getTsicRemaining();

        if (isSplitRequired(tsicRemaining, timesheetTotalTimeDto.getTotalLoggedTime())) {
            timesheet.setBillableTime(timesheet.getBillableTime().subtract(tsicRemaining));
            timesheet.setLoggedTime(timesheet.getLoggedTime().subtract(tsicRemaining));
            repository.save(timesheet);

            var splitTimeSheet = createNewTimesheet(timesheet, tsicRemaining);

            repository.save(splitTimeSheet);
            sendTimesheetChangedEvent(timesheet);
            return true;
        }

        return false;
    }

    private static boolean isSplitRequired(BigDecimal tsicRemaining, BigDecimal totalLoggedTime) {
        return tsicRemaining != null
                && tsicRemaining.compareTo(BigDecimal.ZERO) > 0
                && totalLoggedTime.compareTo(tsicRemaining) > 0;
    }

    private Timesheet createNewTimesheet(Timesheet timesheet, BigDecimal tsicRemaining) {
        var timesheetDto = TimesheetDto.builder()
                .description(timesheet.getDescription())
                .matterId(timesheet.getMatterId())
                .activityId(timesheet.getActivityId())
                .date(timesheet.getDate())
                .loggedTime(tsicRemaining)
                .billableTime(tsicRemaining)
                .isIncludeReport(timesheet.getIsIncludeReport())
                .isApproved(timesheet.getIsApproved())
                .includedInCharge(true)
                .expenseCode(timesheet.getExpenseCode())
                .approvedBy(timesheet.getApprovedBy())
                .expenseDate(timesheet.getExpenseDate())
                .isBillingApproved(timesheet.getIsBillingApproved())
                .timekeeper(timesheet.getTimekeeper())
                .localAttorneyId(timesheet.getLocalAttorneyId())
                .createdBy(timesheet.getCreatedBy())
                .createdAt(LocalDateTime.now())
                .build();
        return mapper.toTimesheet(timesheetDto);
    }

    @Override
    public boolean hasUnapproved(Long activityId, LocalDate billingPeriodEnds) {
        return getTimesheetsByActivity(activityId).stream()
                .anyMatch(timesheet -> !billingPeriodEnds.isBefore(timesheet.getDate())
                        && BooleanUtils.isNotTrue(timesheet.getIsBillingApproved()));
    }

    @Override
    public void cancelBillingOrder(List<Long> timesheetIds) {
        var timesheets = repository.findByIdIn(timesheetIds);
        timesheets.forEach(timesheet -> timesheet.setExpenseDate(null));
        repository.saveAll(timesheets);
        sendUpdateTransferEvent(timesheets.stream().map(mapper::toTimesheetDto).toList());
        sendTimesheetChangedEvent(timesheets);
    }
    
    @Override
    public List<TimesheetDto> updateIncludedInChargeBulk(List<Long> ids, Boolean includedInCharge) {
        var timesheetList = repository.findByIdIn(ids);
        timesheetList.forEach(timesheet -> timesheet.setIncludedInCharge(includedInCharge));
        var timesheetDtos = StreamSupport.stream(repository.saveAll(timesheetList).spliterator(), false)
                .map(mapper::toTimesheetDto)
                .toList();
        sendUpdateTransferEvent(timesheetDtos);
        sendTimesheetChangedEvent(timesheetList);
        return timesheetDtos;
    }
    
    private boolean isExpenseDateNullOrEqualTo(LocalDate expenseDate, LocalDate compareDate) {
        return expenseDate == null || expenseDate.isEqual(compareDate);
    }

    @Data
    @Builder
    public static class BillableQuantity {
        private long quantity;
        private BigDecimal unitPrice;
        private String expenseCode;
        private List<Long> timesheetIds;
    }
}
