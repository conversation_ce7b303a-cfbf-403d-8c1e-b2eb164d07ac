package com.ipms.billing.service.impl;

import com.ipms.billing.client.ActivityClient;
import com.ipms.billing.client.ParamCommandClient;
import com.ipms.billing.client.ParameterClient;
import com.ipms.billing.dto.*;
import com.ipms.billing.enums.ExpenseCodeType;
import com.ipms.billing.enums.ExpenseResponseCode;
import com.ipms.billing.enums.PaymentStatus;
import com.ipms.billing.exception.BillingAccountNotSameException;
import com.ipms.billing.exception.CurrencyNotSameException;
import com.ipms.billing.exception.ExpenseNotDivisibleException;
import com.ipms.billing.exception.ExpenseNotFoundException;
import com.ipms.billing.mapper.ExpenseMapper;
import com.ipms.billing.model.Expense;
import com.ipms.billing.repository.ExpenseRepository;
import com.ipms.billing.service.BillingAccountService;
import com.ipms.billing.service.ExpenseService;
import com.ipms.billing.specification.ExpenseSpecification;
import com.ipms.billing.specification.ExpenseToBeBilledSpecification;
import com.ipms.billing.validator.ExpenseValidator;
import com.ipms.config.kafka.service.ProducerService;
import com.ipms.core.common.enums.Domain;
import com.ipms.core.common.enums.TransferType;
import com.ipms.core.common.model.TransferEvent;
import com.ipms.core.common.utils.NumberUtils;
import lombok.RequiredArgsConstructor;
import org.apache.commons.lang3.BooleanUtils;
import org.apache.commons.lang3.StringUtils;
import org.jetbrains.annotations.NotNull;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.LocalDate;
import java.util.*;
import java.util.stream.Collectors;
import java.util.stream.StreamSupport;

@RequiredArgsConstructor
@Service
public class ExpenseServiceImpl implements ExpenseService {

    private final ExpenseRepository repository;
    private final ExpenseMapper mapper;
    private final ActivityClient activityClient;
    private final ProducerService producerService;
    private final ParamCommandClient paramCommandClient;
    private final ParameterClient parameterClient;
    private final BillingAccountService billingAccountService;
    private final ExpenseValidator validator;


    @Override
    @Transactional
    public ExpenseDto save(ExpenseDto dto) {
        attachPaymentForOfficialFee(dto);
        var expense = mapper.toExpense(dto);

        attachExpenseToPayment(expense);

        var saved = repository.save(expense);
        postSaveActions(dto.getActivityId(), saved);
        return mapper.toExpenseDto(saved);
    }

    private void attachPaymentForOfficialFee(ExpenseDto dto) {
        if (isOfficialFee(dto)) {
            dto.setPayment(createPaymentDtoForExpense(dto));
        }
    }

    private boolean isOfficialFee(ExpenseDto dto) {
        return dto.getAccrualNumber() != null &&
                ExpenseCodeType.OFFICIAL_FEE.equals(
                        paramCommandClient
                                .getExpenseCode(dto.getExpenseCode(), dto.getCurrency())
                                .getPayload()
                                .getType()
                );
    }

    private void attachExpenseToPayment(Expense expense) {
        Optional.ofNullable(expense.getPayment())
                .ifPresent(payment -> payment.setExpense(expense));
    }

    private void postSaveActions(Long activityId, Expense saved) {
        activityClient.addExpense(activityId, saved.getId());
        sendTransferEvent(saved.getId(), TransferType.INSERT);
    }

    private PaymentDto createPaymentDtoForExpense(ExpenseDto dto) {
        var activity = activityClient.getActivity(dto.getActivityId()).getPayload();
        var billingAccount = billingAccountService.getById(activity.getBillingAccountId());

        return PaymentDto.builder()
                .matterId(activity.getMatterId())
                .accrualNumber(dto.getAccrualNumber())
                .issuerId(billingAccount.getIssuerId())
                .expenseAmount(calculateAmount(dto.getUnitPrice(), dto.getQuantity()))
                .expenseCode(dto.getExpenseCode())
                .expenseDate(dto.getExpenseDate())
                .status(PaymentStatus.WAITING_FOR_APPROVAL)
                .build();
    }

    @Override
    public ExpenseDto update(Long id, ExpenseDto dto) {
        var existingExpense = getById(id).toBuilder().build();
        validator.validateUpdate(existingExpense, dto);

        var expense = mapper.toExpenseFromDto(dto, existingExpense);
        var updated = repository.save(expense);
        sendTransferEvent(updated.getId(), TransferType.UPDATE);
        return mapper.toExpenseDto(updated);
    }

    @Override
    public List<ExpenseDto> approveBillingBulk(List<Long> ids) {
        var expenses = repository.findByIdIn(ids);
        expenses.forEach(expense -> expense.setIsBillingApproved(Boolean.TRUE));
        return StreamSupport.stream(repository.saveAll(expenses).spliterator(), false)
                .map(mapper::toExpenseDto)
                .toList();
    }

    @Override
    public List<ExpenseDto> cancelApprovelBillingBulk(List<Long> ids) {
        var expenses = repository.findByIdIn(ids);
        expenses.forEach(expense -> expense.setIsBillingApproved(Boolean.FALSE));
        return StreamSupport.stream(repository.saveAll(expenses).spliterator(), false)
                .map(mapper::toExpenseDto)
                .toList();
    }

    @Override
    public ExpensePageDto getByIds(List<Long> expenseIds, int page, int size) {
        var expensePage = repository.findByIdInOrderByExpenseDateDesc(expenseIds, PageRequest.of(page, size));
        return ExpensePageDto.builder()
                .expenses(expensePage.getContent()
                        .stream()
                        .map(mapper::toExpenseDto)
                        .toList())
                .totalElements(expensePage.getTotalElements())
                .totalPages(expensePage.getTotalPages())
                .build();
    }

    @Override
    public void delete(Long id, Long version) {
        var expense = getById(id).toBuilder()
                .version(version)
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(expense);
        sendTransferEvent(id, TransferType.DELETE);
    }

    private void delete(Long id) {
        var expense = getById(id).toBuilder()
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(expense);
        sendTransferEvent(id, TransferType.DELETE);
    }

    @Override
    public void deleteBulk(List<Long> ids) {
        var expenses = repository.findByIdIn(ids);
        expenses.forEach(expense -> {
            repository.save(expense.toBuilder()
                    .isDeleted(Boolean.TRUE)
                    .build());
            sendTransferEvent(expense.getId(), TransferType.DELETE);
        });
    }

    @Override
    public void deleteByDisbursementIds(List<Long> disbursementIds) {
        var expenseIds = repository.findByDisbursementsIn(disbursementIds).stream().map(Expense::getId).toList();
        deleteBulk(expenseIds);
    }

    @Override
    public void deleteByTimesheetIds(List<Long> timesheetIds) {
        var expenseIds = repository.findByTimesheetsIn(timesheetIds).stream().map(Expense::getId).toList();
        deleteBulk(expenseIds);
    }

    @Override
    public List<ExpenseDto> getByIdIn(List<Long> ids) {
        return repository.findByIdIn(ids)
                .stream()
                .map(mapper::toExpenseDto)
                .toList();
    }

    @Override
    public void applyBillingDiscount(DiscountRequest discountRequest) {
        var expenses = repository.findByIdIn(discountRequest.getIds());
        var eligibleExpenses = filterExpensesByEligibility(expenses, true);
        eligibleExpenses.forEach(expense -> {
            expense.setDiscountRate(discountRequest.getDiscountRate());
            repository.save(expense);
        });
    }

    @Override
    public ExpensePageDto getToBeBilledExpensePage(ExpenseFilterRequest filterRequest, int page, int size) {
        var toBeBilledSpecification = ExpenseToBeBilledSpecification.builder()
                .ids(filterRequest.getIds())
                .billingPeriodEnds(filterRequest.getBillingPeriodEnds())
                .sortDirection(filterRequest.getSortDirection())
                .sortField(filterRequest.getSortField())
                .build();
        var expensePage = repository.findAll(toBeBilledSpecification, PageRequest.of(page, size));
        return getExpensePageDto(expensePage);
    }

    @Override
    public TotalAmount getTotalAmountForDiscount(List<Long> ids) {
        var expenses = repository.findByIdIn(ids);

        var disbursementExpenses = getDisbursementExpenses(expenses);
        var serviceFeeExpenses = getServiceFeeExpenses(expenses);

        var eligibleDisbursements = filterExpensesByEligibility(disbursementExpenses, true);
        var notEligibleDisbursements = filterExpensesByEligibility(disbursementExpenses, false);

        var eligibleServiceFees = filterExpensesByEligibility(serviceFeeExpenses, true);
        var notEligibleServiceFees = filterExpensesByEligibility(serviceFeeExpenses, false);

        var eligibleAmount = buildAmount(eligibleDisbursements, eligibleServiceFees);
        var notEligibleAmount = buildAmount(notEligibleDisbursements, notEligibleServiceFees);

        return TotalAmount.builder()
                .eligibleForDiscount(eligibleAmount)
                .notEligibleForDiscount(notEligibleAmount)
                .build();
    }

    private TotalAmount.Amount buildAmount(List<Expense> disbursementExpenses, List<Expense> serviceFeeExpenses) {
        return TotalAmount.Amount.builder()
                .totalDisbursements(calculateAmount(disbursementExpenses))
                .totalServiceFees(calculateAmount(serviceFeeExpenses))
                .build();
    }

    private List<Expense> getServiceFeeExpenses(List<Expense> expenses) {
        return expenses.stream()
                .filter(expense -> expense.getDisbursements().isEmpty())
                .toList();
    }


    private List<Expense> filterExpensesByEligibility(List<Expense> expenses, boolean isEligible) {
        var eligibleExpenseIds = getDiscountEligibleExpenseIds(expenses);
        return expenses.stream()
                .filter(expense -> isEligible == eligibleExpenseIds.contains(expense.getId()))
                .toList();
    }

    private List<Long> getDiscountEligibleExpenseIds(List<Expense> expenses) {
        if (expenses.isEmpty()) {
            return Collections.emptyList();
        }
        return paramCommandClient.getDiscountEligibleExpenseIds(mapper.toExpenseDtoList(expenses))
                .getIds();
    }

    @NotNull
    private BigDecimal calculateAmount(List<Expense> expenses) {
        return expenses.stream()
                .map(this::calculateAmount)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

    @NotNull
    private BigDecimal calculateAmount(Expense expense) {
        return calculateAmount(expense.getUnitPrice(), expense.getQuantity());
    }

    @NotNull
    private List<Expense> getTimesheetExpenses(List<Expense> expenses) {
        return expenses.stream()
                .filter(expense -> !expense.getTimesheets().isEmpty())
                .toList();
    }

    @NotNull
    private List<Expense> getDisbursementExpenses(List<Expense> expenses) {
        return expenses.stream()
                .filter(expense -> !expense.getDisbursements().isEmpty())
                .toList();
    }

    @Override
    public List<Expense> getToBeBilledExpenses(List<Long> ids, LocalDate billingPeriodEnds) {
        var toBeBilledSpecification = ExpenseToBeBilledSpecification.builder()
                .ids(ids)
                .billingPeriodEnds(billingPeriodEnds)
                .build();
        return repository.findAll(toBeBilledSpecification);
    }

    @Override
    public ExpensePageDto getBilledExpenses(ExpenseFilterRequest filterRequest, int page, int size) {
        var expensePage = repository.findByIdInAndOrderDateBeforeOrderByOrderDateDesc(filterRequest.getIds(),
                filterRequest.getBillingPeriodEnds(), PageRequest.of(page, size));
        return getExpensePageDto(expensePage);
    }

    @Override
    public List<Expense> getUnbilledExpenses(List<Long> ids) {
        return repository.findByOrderDateIsNullAndIdIn(ids);
    }

    private ExpensePageDto getExpensePageDto(Page<Expense> expensePage) {
        return ExpensePageDto.builder()
                .expenses(expensePage.getContent()
                        .stream()
                        .map(mapper::toExpenseDto)
                        .toList())
                .totalElements(expensePage.getTotalElements())
                .totalPages(expensePage.getTotalPages())
                .build();
    }

    private Expense getById(Long id) {
        return repository.findById(id)
                .orElseThrow(ExpenseNotFoundException::new);
    }

    private void sendTransferEvent(List<Expense> expenses) {
        expenses.forEach(expense -> sendTransferEvent(expense.getId(), TransferType.DELETE));
    }

    private void sendTransferEvent(Long id, TransferType transferType) {
        producerService.sendTransfer(TransferEvent.builder()
                .type(transferType)
                .domainObjectId(id)
                .domain(Domain.IPMS_EXPENSE)
                .build());
    }

    @Override
    public Boolean isBillingApprovableExpenses(Long activityId, LocalDate billingPeriodEnds) {
        var expenseIds = getExpenseIds(activityId);
        var filterRequest = ExpenseFilterRequest.builder();

        filterRequest.ids(expenseIds);
        filterRequest.billingPeriodEnds(billingPeriodEnds);

        if(!expenseIds.isEmpty()) {
            var expenses = getToBeBilledExpensePage(filterRequest.build(), 0, expenseIds.size());
            for (ExpenseDto expense : expenses.getExpenses()) {
                if(expense.getIsBillingApproved() == null || !expense.getIsBillingApproved()) {
                    return false;
                }
            }
        }

        return true;
    }

    @NotNull
    private List<Long> getExpenseIds(List<Long> activityIds) {
        return repository.findByActivityIdIn(activityIds).stream()
                .map(Expense::getId)
                .toList();
    }

    @NotNull
    private List<Long> getExpenseIds(Long activityId) {
        return repository.findByActivityId(activityId).stream()
                .map(Expense::getId)
                .toList();
    }

    @Override
    public ExpenseSummaryPageDto getOrderCreationExpenses(String assignee, int page, int size) {
        var activities = activityClient.getBillingCartActivities(assignee).getPayload();
        validateBillingAccountConsistency(activities);
        var expenseDtos = filterOrderCreationExpenses(activities);
        var orderCreationExpenses = getExpenseSummaryDtos(expenseDtos);
        return convertToPageDto(page, size, orderCreationExpenses);
    }

    private List<ExpenseSummaryDto> getExpenseSummaryDtos(List<ExpenseDto> expenseDtos) {
        var expensesByExpenseCodeMap = expenseDtos.stream()
                .collect(Collectors.groupingBy(ExpenseDto::getExpenseCode,
                        Collectors.groupingBy(ExpenseDto::getUnitPrice)));
        List<ExpenseSummaryDto> orderCreationExpenses = new ArrayList<>();
        expensesByExpenseCodeMap.forEach((expenseCode, expensesByUnitPiceMap) -> {
            var firstEntry = expensesByUnitPiceMap.entrySet().stream()
                    .findFirst()
                    .orElseThrow();
            var currency = firstEntry.getValue().get(0).getCurrency();
            var expenseCodeResponse = paramCommandClient.getExpenseCode(expenseCode, currency)
                    .getPayload();
            expensesByUnitPiceMap.forEach((unitPrice, expenses) -> {
                validateCurrencyConsistency(expenses, currency);
                var orderCreationExpense = new ExpenseSummaryDto();
                var totalQuantity = expenses.stream()
                        .mapToLong(ExpenseDto::getQuantity)
                        .sum();
                orderCreationExpense.setQuantity(totalQuantity);
                orderCreationExpense.setUnitPrice(expenses.get(0).getUnitPrice());
                orderCreationExpense.setDiscountRate(expenses.get(0).getDiscountRate());
                orderCreationExpense.setExpenseCode(expenseCode);
                orderCreationExpense.setDefinition(expenseCodeResponse.getDefinition());
                orderCreationExpense.setCurrency(currency);
                orderCreationExpense.setTotalAmount(calculateAmountWithDiscount(orderCreationExpense.getUnitPrice(),
                                                                                orderCreationExpense.getQuantity(),
                                                                                orderCreationExpense.getDiscountRate()));
                var expenseIds = expenses.stream()
                        .map(ExpenseDto::getId)
                        .toList();
                orderCreationExpense.setExpenseIds(expenseIds);
                orderCreationExpenses.add(orderCreationExpense);
            });
        });
        return orderCreationExpenses;
    }

    @Override
    public boolean hasOrderableExpense(List<Long> expenseIds) {
        var expenses = repository.findByIdIn(expenseIds);
        return expenses.stream().anyMatch(expense ->
                        BooleanUtils.isTrue(expense.getIsBillingApproved()) && expense.getOrderDate() == null);
    }

    @Transactional
    @Override
    public List<ExpenseSummaryDto> processOrderCreate(OrderCreateRequest orderCreateRequest) {
        var expenses = getExpenses(orderCreateRequest);
        if (expenses.isEmpty()) {
            return Collections.emptyList();
        }
        var expenseCodes = expenses.stream()
                .map(Expense::getExpenseCode)
                .toList();
        var billingAccountCurrency = orderCreateRequest.getBillingAccountCurrency();
        validateCurrencyConsistencyForOrder(expenses, billingAccountCurrency);
        paramCommandClient.validateExpenseCodes(expenseCodes, billingAccountCurrency);

        expenses.forEach(expense -> {
            expense.setOrderDate(orderCreateRequest.getOrderDate());
            expense.setOrderNumber(orderCreateRequest.getOrderNumber());
            repository.save(expense);
            sendTransferEvent(expense.getId(), TransferType.UPDATE);
        });
        var expenseDtos = expenses.stream()
                .map(mapper::toExpenseDto)
                .toList();
        return getExpenseSummaryDtos(expenseDtos);
    }

    private List<Expense> getExpenses(OrderCreateRequest orderCreateRequest) {
        if (BooleanUtils.isTrue(orderCreateRequest.getAutoCreate())) {
            return getToBeBilledExpenses(orderCreateRequest.getExpenseIds(), orderCreateRequest.getBillingPeriodEnds());
        } else {
            return repository.findByIdIn(orderCreateRequest.getExpenseIds());
        }
    }

    @Override
    public List<ExpenseDto> processOrderSplit(Long activityId, List<OrderCreateRequest> orderCreateRequests) {
        var orderCreateRequest = orderCreateRequests.stream()
                .findFirst()
                .orElseThrow();
        var expenseIds = orderCreateRequest.getExpenseIds();
        var billingExpenses = repository.findByIdIn(expenseIds);

        validateCurrencyConsistencyForOrder(billingExpenses, orderCreateRequest.getBillingAccountCurrency());
        
        calculateTimesheetExpenses(billingExpenses, orderCreateRequests);
        calculateDisbursementExpenses(orderCreateRequests, billingExpenses);

        var manuelExpenses = billingExpenses.stream()
                .filter(expense -> expense.getTimesheets().isEmpty() && expense.getDisbursements().isEmpty())
                .toList();
        calculateNoUnitPriceExpenses(manuelExpenses, orderCreateRequests);
        calculateUnitPricedExpenses(manuelExpenses, orderCreateRequests);

        var orderNumbers = orderCreateRequests.stream()
                .map(OrderCreateRequest::getOrderNumber)
                .toList();

        var expenses = repository.findByOrderNumberIn(orderNumbers);

        activityClient.addExpenses(activityId, expenses.stream().map(Expense::getId).toList());

        return expenses.stream()
                .map(mapper::toExpenseDto)
                .toList();
    }

    @Override
    public Boolean isBillable(List<Long> expenseIds) {
        return expenseIds != null && getByIdIn(expenseIds).stream()
                .anyMatch(expenseDto -> StringUtils.isEmpty(expenseDto.getOrderNumber()));
    }

    @Override
    public List<ExpenseDto> getExpensesByOrderNo(String orderNo) {
        return repository.findByOrderNumber(orderNo).stream()
                .map(mapper::toExpenseDto)
                .toList();
    }

    @Override
    public List<Expense> getExpenseEntitiesByOrderNo(String orderNo) {
        return repository.findByOrderNumber(orderNo).stream()
                .toList();
    }

    @Override
    public List<Expense> getExpensesByIdsAndOrderNo(List<Long> ids, String orderNo) {
        return repository.findByIdInAndOrderNumber(ids, orderNo);
    }

    @Override
    public List<Long> getExpenseIdsByOrderNo(String orderNo) {
        return getExpensesByOrderNo(orderNo).stream()
                .map(ExpenseDto::getId)
                .toList();
    }

    @Override
    public void deleteExpensesRelatedWithTimesheetAndDisbursement(List<Expense> expenses) {
        expenses.forEach(expense -> {
            if (hasDisbursementOrTimesheetRelation(expense)) {
                delete(expense.getId());
            } else {
                resetBillingData(expense);
            }
        });
    }

    private static boolean hasDisbursementOrTimesheetRelation(Expense expense) {
        return !expense.getDisbursements().isEmpty() || !expense.getTimesheets().isEmpty();
    }

    @Override
    public void resetBillingData(List<Expense> expenses) {
        expenses.forEach(this::resetBillingData);
    }

    private void resetBillingData(Expense expense) {
        expense.setOrderDate(null);
        expense.setOrderNumber(null);
        expense.setInvoiceNumber(null);
        expense.setInvoiceDate(null);
        expense.setDiscountPrice(BigDecimal.ZERO);
        // Do not reset discount rate during cancellation
        expense.setVat(0);
        repository.save(expense);
        sendTransferEvent(expense.getId(), TransferType.UPDATE);
    }

    private void calculateDisbursementExpenses(List<OrderCreateRequest> orderCreateRequests,
                                               List<Expense> billingExpenses) {
        var splitCount = orderCreateRequests.size();
        var disbursementExpenses = billingExpenses.stream()
                .filter(expense -> !expense.getDisbursements().isEmpty())
                .toList();
        var disbursementExpenseCodeMap = disbursementExpenses.stream()
                .collect(Collectors.groupingBy(Expense::getExpenseCode));
        disbursementExpenseCodeMap.forEach((expenseCode, expenses) -> {
            var expense = expenses.stream()
                    .findFirst()
                    .orElseThrow(ExpenseNotFoundException::new);
            var totalAmount = calculateAmount(expenses);
            var unitPrice = totalAmount.divide(new BigDecimal(splitCount), 2, RoundingMode.HALF_UP);
            var expenseDto = mapper.toExpenseDto(expense);
            expenseDto.setQuantity(1L);
            expenseDto.setUnitPrice(unitPrice);
            deleteExpenses(expenses);
            cloneExpense(expenseDto, orderCreateRequests);
        });
    }

    @NotNull
    private BigDecimal calculateAmount(BigDecimal unitPrice, Long quantity) {
        return unitPrice.multiply(new BigDecimal(quantity));
    }

    @NotNull
    private BigDecimal calculateAmountWithDiscount(BigDecimal unitPrice, Long quantity, BigDecimal discountRate) {
        BigDecimal amount = calculateAmount(unitPrice, quantity);
        if (discountRate != null) {
            discountRate = discountRate.divide(BigDecimal.valueOf(100), 2, RoundingMode.HALF_UP);
            BigDecimal discountFactor = BigDecimal.ONE.subtract(discountRate);
            amount = amount.multiply(discountFactor);
        }
        return amount;
    }

    private void calculateTimesheetExpenses(List<Expense> billingExpenses,
                                            List<OrderCreateRequest> orderCreateRequests) {
        var timesheetExpenses = getTimesheetExpenses(billingExpenses);
        var timesheetExpenseCodeMap = timesheetExpenses.stream()
                .collect(Collectors.groupingBy(expense -> {
                    var expenseCode = expense.getExpenseCode();
                    var index = expenseCode.indexOf("_");
                    if (index != -1) {
                        expenseCode = expenseCode.substring(0, index);
                    }
                    return expenseCode;
                }));
        timesheetExpenseCodeMap.forEach((expenseCode, expenses) -> {
            var quantityOf60 = expenses.stream()
                    .filter(expense -> !is6MinuteCode(expense))
                    .mapToLong(Expense::getQuantity)
                    .sum();
            var optionalExpenseOf60 = expenses.stream()
                    .filter(expense -> !is6MinuteCode(expense))
                    .findFirst();
            var quantityOf6 = expenses.stream()
                    .filter(this::is6MinuteCode)
                    .mapToLong(Expense::getQuantity)
                    .sum();
            var optionalExpenseOf6 = expenses.stream()
                    .filter(this::is6MinuteCode)
                    .findFirst();
            var total = quantityOf60 * 10 + quantityOf6;
            quantityOf6 = total / orderCreateRequests.size();
            quantityOf60 = quantityOf6 / 10;
            quantityOf6 = quantityOf6 % 10;

            if (quantityOf60 > 0) {
                var expense = optionalExpenseOf60.orElseThrow(ExpenseNotFoundException::new);
                var expenseDto = mapper.toExpenseDto(expense);
                expenseDto.setQuantity(quantityOf60);
                cloneExpense(expenseDto, orderCreateRequests);
            }

            if (quantityOf6 > 0) {
                long finalQuantityOf6 = quantityOf6;
                optionalExpenseOf6.ifPresentOrElse(expense -> {
                    var expenseDto = mapper.toExpenseDto(expense);
                    expenseDto.setQuantity(finalQuantityOf6);
                    cloneExpense(expenseDto, orderCreateRequests);
                }, () -> {
                    var divideMinutesString = parameterClient.getByKey("EXPENSE_CODE_FOR_6_MIN").getPayload().getValue();
                    var expense = optionalExpenseOf60.orElseThrow(ExpenseNotFoundException::new);
                    var expenseDto = mapper.toExpenseDto(expense);
                    var expenseCodeMinute = expenseDto.getExpenseCode().concat("_" + divideMinutesString);
                    var expenseCodeResponse = paramCommandClient.getExpenseCode(expenseCodeMinute, expense.getCurrency());
                    expenseDto.setQuantity(finalQuantityOf6);
                    expenseDto.setExpenseCode(expenseCodeMinute);
                    expenseDto.setUnitPrice(expenseCodeResponse.getPayload().getUnitPrice());
                    cloneExpense(expenseDto, orderCreateRequests);
                });
            }
            deleteExpenses(expenses);
        });
    }

    private void calculateUnitPricedExpenses(List<Expense> manuelExpenses, List<OrderCreateRequest> orderCreateRequests) {
        var splitCount = orderCreateRequests.size();
        var noUnitPriceExpenses = getNoUnitPriceExpenses(manuelExpenses);
        var unitPricedExpenses = manuelExpenses.stream()
                .filter(expense -> !noUnitPriceExpenses.contains(expense))
                .toList();
        var expeseCodeMap = unitPricedExpenses.stream()
                .collect(Collectors.groupingBy(Expense::getExpenseCode));
        expeseCodeMap.forEach((expenseCode, expenses) -> {
            var totalQuantity = expenses.stream()
                    .mapToLong(Expense::getQuantity)
                    .sum();
            var division = totalQuantity / splitCount;
            if (!isDivisible(totalQuantity, splitCount)) {
                throw new ExpenseNotDivisibleException();
            }
            var expenseDto = mapper.toExpenseDto(expenses.get(0));
            expenseDto.setQuantity(division);
            var expenseCodeResponse = paramCommandClient.getExpenseCode(expenseCode, expenseDto.getCurrency());
            expenseDto.setUnitPrice(expenseCodeResponse.getPayload().getUnitPrice());
            cloneExpense(expenseDto, orderCreateRequests);
            deleteExpenses(expenses);
        });
    }

    private boolean is6MinuteCode(Expense expense) {
        return expense.getExpenseCode().contains("_");
    }

    private void deleteExpenses(List<Expense> expenses) {
        expenses.forEach(expense -> expense.setDeleted(true));
        repository.saveAll(expenses);
        sendTransferEvent(expenses);
    }

    private boolean isDivisible(long a, long b) {
        return a % b == 0;
    }

    private void calculateNoUnitPriceExpenses(List<Expense> manuelExpenses, List<OrderCreateRequest> orderCreateRequests) {
        var splitCount = orderCreateRequests.size();
        var noUnitPriceExpenses = getNoUnitPriceExpenses(manuelExpenses);
        var expeseCodeMap = noUnitPriceExpenses.stream()
                .collect(Collectors.groupingBy(Expense::getExpenseCode));
        expeseCodeMap.forEach((expenseCode, expenses) -> {
            var totalAmount = calculateAmount(expenses);
            var unitPrice = totalAmount.divide(new BigDecimal(splitCount), 2, RoundingMode.HALF_UP);
            var expenseDto = mapper.toExpenseDto(expenses.get(0));
            expenseDto.setQuantity(1L);
            expenseDto.setUnitPrice(unitPrice);
            cloneExpense(expenseDto, orderCreateRequests);
            deleteExpenses(expenses);
        });
    }

    private List<Expense> getNoUnitPriceExpenses(List<Expense> expenses) {
        return expenses.stream()
                .filter(expense -> {
                    var expenseCode = paramCommandClient.getExpenseCode(expense.getExpenseCode(), expense.getCurrency());
                    return expenseCode.getPayload().getUnitPrice().compareTo(BigDecimal.ONE) == 0;
                })
                .toList();
    }

    private void cloneExpense(ExpenseDto expenseDto, List<OrderCreateRequest> orderCreateRequests) {
        orderCreateRequests.forEach(orderCreateRequest -> {
            var expense = mapper.toExpense(expenseDto);
            expense.setOrderDate(orderCreateRequest.getOrderDate());
            expense.setOrderNumber(orderCreateRequest.getOrderNumber());
            expense.setId(null);
            var savedExpense = repository.save(expense);
            sendTransferEvent(savedExpense.getId(), TransferType.INSERT);
        });
    }

    private void validateCurrencyConsistency(List<ExpenseDto> expenses, String currency) {
        var currencySame = expenses.stream()
                .allMatch(expenseDto -> currency.equals(expenseDto.getCurrency()));
        if (!currencySame) {
            var expenseCode = expenses.get(0).getExpenseCode();
            throw new CurrencyNotSameException(new Object[] {expenseCode});
        }
    }

    private void validateCurrencyConsistencyForOrder(List<Expense> expenses, String billingAccountCurrency) {
        var currencySame = expenses.stream()
                .allMatch(expenseDto -> billingAccountCurrency.equals(expenseDto.getCurrency()));
        if (!currencySame) {
            throw new CurrencyNotSameException(ExpenseResponseCode.CURRENCY_NOT_SAME_AS_BILLING_ACCOUNT);
        }
    }

    private List<ExpenseDto> filterOrderCreationExpenses(List<ActivityListResponse.Payload> activities) {
        var activityIds = activities.stream()
                .map(ActivityListResponse.Payload::getId)
                .toList();
        var expenseIds = getExpenseIds(activityIds);

        return getByIdIn(expenseIds).stream()
                .filter(expense ->
                        expense.getOrderDate() == null && BooleanUtils.isTrue(expense.getIsBillingApproved()))
                .toList();
    }

    private void validateBillingAccountConsistency(List<ActivityListResponse.Payload> activities) {
        var billingAccountIdsSame = activities.stream()
                .allMatch(activityDto ->
                        activities.get(0).getBillingAccountId().equals(activityDto.getBillingAccountId()));

        if (!billingAccountIdsSame) {
            throw new BillingAccountNotSameException();
        }
    }

    private ExpenseSummaryPageDto convertToPageDto(long page, int size, List<ExpenseSummaryDto> expenses) {
        var expenseSummaryDtos = expenses.stream()
                .skip(page * size)
                .limit(size)
                .toList();

        int totalSize = expenses.size();
        int totalPages = (int) Math.ceil((double) totalSize / size);

        return ExpenseSummaryPageDto.builder()
                .expenseSummaryDtos(expenseSummaryDtos)
                .totalPages(totalPages)
                .totalElements(totalSize)
                .build();
    }

    @Override
    public Boolean isBillingCancellableExpenses(Long activityId) {
        var activity = activityClient.getActivity(activityId).getPayload();
        var expenseIds = getExpenseIds(activityId);

        if(!expenseIds.isEmpty()) {
            var toBeBilledSpecification = ExpenseToBeBilledSpecification.builder()
                    .ids(expenseIds)
                    .billingPeriodEnds(activity.getBillingPeriodEnds())
                    .build();
            return repository.findAll(toBeBilledSpecification).isEmpty();
        }
        return true;
    }

    @Override
    public List<String> getOrderNumbersList() {
        return repository.getAllByIsDeletedFalse().stream()
                .map(OrderNumberInvoiceNumber::orderNumber)
                .filter(Objects::nonNull).toList();
    }


    @Override
    public List<String> getInvoiceNumbersList() {
        return repository.getAllByIsDeletedFalse().stream()
                .map(OrderNumberInvoiceNumber::invoiceNumber)
                .filter(Objects::nonNull).toList();
    }

    @Override
    public ExpensePageDto getAll(ExpenseFilterRequest filterRequest, int page, int size) {
        var expensePage = repository.findAll(mapSpecification(filterRequest), PageRequest.of(page, size));
        return ExpensePageDto.builder()
                .expenses(expensePage.getContent()
                        .stream()
                        .map(mapper::toExpenseDto)
                        .toList())
                .totalElements(expensePage.getTotalElements())
                .totalPages(expensePage.getTotalPages())
                .build();
    }

    @Override
    public void updateInvoiceFields(InvoiceFetchedEvent.Invoice invoice) {
        invoice.getInvoiceLines().forEach(invoiceLine -> {
            var expenses = getExpensesByInvoiceNumberIsEmpty(invoiceLine.getExpenseIds());
            expenses.forEach(expense -> {
                expense.setDiscountPrice(NumberUtils.parseBigDecimal(invoiceLine.getPriceAmount()));
                expense.setVat(NumberUtils.parseInteger(invoiceLine.getTaxRate()));
                expense.setInvoiceDate(LocalDate.parse(invoice.getInvoiceDate()));
                expense.setInvoiceNumber(invoice.getErpDocumentNo());
                repository.save(expense);
                sendTransferEvent(expense.getId(), TransferType.UPDATE);
            });
        });
    }

    @NotNull
    private List<Expense> getExpensesByInvoiceNumberIsEmpty(List<Long> expenseIds) {
        return repository.findByIdIn(expenseIds).stream()
                .filter(expense -> StringUtils.isEmpty(expense.getInvoiceNumber()))
                .toList();
    }

    @Override
    public Boolean isInvoiceCompleted(LocalDate billingPeriodEnds, List<Long> expenseIds) {
        return repository.findByIdInAndExpenseDateLessThanEqual(expenseIds, billingPeriodEnds).stream()
                .filter(expense -> StringUtils.isNotEmpty(expense.getOrderNumber()))
                .allMatch(expense -> StringUtils.isNotEmpty(expense.getInvoiceNumber()));
    }

    @Override
    public boolean isInvoiceCompleted(String orderNumber) {
        return getExpenseEntitiesByOrderNo(orderNumber).stream()
                .allMatch(expense -> StringUtils.isNotEmpty(expense.getInvoiceNumber()));
    }

    @Override
    public void clearDiscountRate(List<Long> expenseIds, LocalDate billingPeriodEnds) {
        var expenses = getToBeBilledExpenses(expenseIds, billingPeriodEnds);
        expenses.forEach(expense -> {
            expense.setDiscountRate(null);
            repository.save(expense);
            sendTransferEvent(expense.getId(), TransferType.UPDATE);
        });
    }

    @Override
    public boolean hasBillableExpenseByBillingPeriodEnds(List<Long> expenseIds, LocalDate billingPeriodEnds) {
        return !getToBeBilledExpenses(expenseIds, billingPeriodEnds).isEmpty();
    }

    @Override
    public boolean hasUnapprovedExpense(List<Long> expenseIds, LocalDate billingPeriodEnds) {
        return getToBeBilledExpenses(expenseIds, billingPeriodEnds).stream()
                .anyMatch(expense -> BooleanUtils.isNotTrue(expense.getIsBillingApproved()));
    }

    private ExpenseSpecification mapSpecification(ExpenseFilterRequest filterRequest) {
        return ExpenseSpecification.builder()
                .ids(filterRequest.getIds())
                .orderNumbers(filterRequest.getOrderNumbers())
                .invoiceNumbers(filterRequest.getInvoiceNumbers())
                .build();
    }

    @Override
    public List<ExpenseDto> getByActivityId(Long activityId) {
        return repository.findByActivityId(activityId).stream()
                .map(mapper::toExpenseDto)
                .toList();
    }

}
