package com.ipms.billing.service.impl;

import com.ipms.billing.dto.BillingAccountExpenseCodeDto;
import com.ipms.billing.dto.BillingAccountExpenseCodeFilterRequest;
import com.ipms.billing.mapper.BillingAccountExpenseCodeMapper;
import com.ipms.billing.repository.BillingAccountExpenseCodeRepository;
import com.ipms.billing.service.BillingAccountExpenseCodeService;
import com.ipms.billing.specification.BillingAccountExpenseCodeSpecification;
import lombok.RequiredArgsConstructor;
import org.springframework.stereotype.Service;

import javax.persistence.EntityNotFoundException;
import java.util.List;

@Service
@RequiredArgsConstructor
public class BillingAccountExpenseCodeServiceImpl implements BillingAccountExpenseCodeService {
    private final BillingAccountExpenseCodeRepository repository;
    private final BillingAccountExpenseCodeMapper mapper;

    @Override
    public List<BillingAccountExpenseCodeDto> getAll(
            BillingAccountExpenseCodeFilterRequest filterRequest) {
        return repository.findAll(mapSpecification(filterRequest)).stream()
                .map(mapper::toBillingAccountExpenseCodeDto)
                .toList();
    }

    @Override
    public BillingAccountExpenseCodeDto save(BillingAccountExpenseCodeDto billingAccountExpenseCodeDto) {
        if (billingAccountExpenseCodeDto.getId() != null) {
            throw new IllegalArgumentException("ID should not be provided for create operation");
        }

        var entity = mapper.toBillingAccountExpenseCode(billingAccountExpenseCodeDto);
        var savedEntity = repository.save(entity);
        return mapper.toBillingAccountExpenseCodeDto(savedEntity);
    }

    @Override
    public BillingAccountExpenseCodeDto update(BillingAccountExpenseCodeDto billingAccountExpenseCodeDto) {
        if (billingAccountExpenseCodeDto.getId() == null) {
            throw new IllegalArgumentException("ID must be provided for update operation");
        }

        var existingEntity = repository.findById(billingAccountExpenseCodeDto.getId())
                .orElseThrow(() -> new EntityNotFoundException("BillingAccountExpenseCode not found"));

        var entityToUpdate = mapper.toBillingAccountExpenseCode(billingAccountExpenseCodeDto);
        entityToUpdate.setId(existingEntity.getId());

        var updatedEntity = repository.save(entityToUpdate);
        return mapper.toBillingAccountExpenseCodeDto(updatedEntity);
    }

    @Override
    public void delete(Long id, Long version) {
        var existingEntity = repository.findById(id)
                .orElseThrow(() -> new EntityNotFoundException("BillingAccountExpenseCode not found"));

        var deleted = existingEntity.toBuilder()
                .version(version)
                .isDeleted(Boolean.TRUE)
                .build();
        repository.save(deleted);
    }

    private BillingAccountExpenseCodeSpecification mapSpecification(
            BillingAccountExpenseCodeFilterRequest filterRequest) {
        return BillingAccountExpenseCodeSpecification.builder()
                .billingAccountId(filterRequest.getBillingAccountId())
                .countryCode(filterRequest.getCountryCode())
                .protectionType(filterRequest.getProtectionType())
                .activityType(filterRequest.getActivityType())
                .generalExpenseCode(filterRequest.getGeneralExpenseCode())
                .build();
    }
}
