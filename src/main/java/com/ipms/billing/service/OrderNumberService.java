package com.ipms.billing.service;

import java.util.List;

/**
 * Service for generating atomic, unique order numbers for billing orders.
 * 
 * This service ensures thread-safe order number generation using database sequences
 * to prevent duplicate order numbers in concurrent scenarios.
 * 
 * Order number format: {PREFIX}{YY}{NNNNNN}
 * - PREFIX: Retrieved from parameter service (e.g., "BO")
 * - YY: Last two digits of current year
 * - NNNNNN: 6-digit sequence number from database sequence
 */
public interface OrderNumberService {
    
    /**
     * Generates a single unique order number.
     * 
     * This method is thread-safe and guarantees uniqueness even under
     * high concurrency scenarios.
     * 
     * @return A unique order number in format: PREFIX + YY + 6-digit sequence
     */
    String generateOrderNumber();
    
    /**
     * Generates multiple unique order numbers in bulk.
     * 
     * This method is optimized for bulk operations to minimize database
     * round trips while maintaining atomicity and uniqueness.
     * 
     * @param count Number of order numbers to generate
     * @return List of unique order numbers, guaranteed to be sequential and unique
     * @throws IllegalArgumentException if count is less than 1
     */
    List<String> generateOrderNumbers(int count);
    
    /**
     * Validates if an order number follows the expected format.
     * 
     * @param orderNumber The order number to validate
     * @return true if the order number matches the expected format, false otherwise
     */
    boolean isValidOrderNumberFormat(String orderNumber);
}