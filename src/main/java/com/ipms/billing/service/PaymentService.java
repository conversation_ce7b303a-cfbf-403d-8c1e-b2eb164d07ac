package com.ipms.billing.service;

import com.ipms.billing.dto.PaymentDto;
import com.ipms.billing.dto.PaymentFilterRequest;
import com.ipms.billing.dto.PaymentGroupPageDto;
import com.ipms.billing.dto.PaymentPageDto;
import com.ipms.billing.enums.PaymentStatus;
import com.ipms.billing.model.Payment;

import java.util.List;



public interface PaymentService {
    PaymentDto save(PaymentDto dto);
    Payment getPaymentById(Long id);
    PaymentDto getByAccrualNumber(String accrualNumber);
    PaymentPageDto getAll(PaymentFilterRequest filterRequest, int page, int size);
    PaymentGroupPageDto getAllGrouped(PaymentFilterRequest filterRequest, int page, int size);
    PaymentDto update(PaymentDto dto, Long id, Long version);
    void deleteUnapprovedPayments();
    void bulkUpdateStatus(List<Long> paymentIds, PaymentStatus newStatus);
}
