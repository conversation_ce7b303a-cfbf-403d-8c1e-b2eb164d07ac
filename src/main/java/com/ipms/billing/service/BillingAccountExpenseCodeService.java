package com.ipms.billing.service;

import com.ipms.billing.dto.BillingAccountExpenseCodeDto;
import com.ipms.billing.dto.BillingAccountExpenseCodeFilterRequest;

import java.util.List;

public interface BillingAccountExpenseCodeService {
    List<BillingAccountExpenseCodeDto> getAll(BillingAccountExpenseCodeFilterRequest filterRequest);
    BillingAccountExpenseCodeDto save(BillingAccountExpenseCodeDto billingAccountExpenseCodeDto);
    BillingAccountExpenseCodeDto update(BillingAccountExpenseCodeDto billingAccountExpenseCodeDto);
    void delete(Long id, Long version);

}
