package com.ipms.billing.service;

import com.ipms.billing.dto.*;
import com.ipms.billing.model.Expense;

import java.time.LocalDate;
import java.util.List;

public interface ExpenseService {
    ExpenseDto save(ExpenseDto dto);

    ExpenseDto update(Long id, ExpenseDto dto);

    List<ExpenseDto> approveBillingBulk(List<Long> ids);

    List<ExpenseDto> cancelApprovelBillingBulk(List<Long> ids);

    ExpensePageDto getByIds(List<Long> expenseIds, int page, int size);

    void delete(Long id, Long version);

    void deleteBulk(List<Long> ids);

    void deleteByDisbursementIds(List<Long> disbursementIds);

    void deleteByTimesheetIds(List<Long> timesheetIds);

    List<ExpenseDto> getByIdIn(List<Long> ids);

    void applyBillingDiscount(DiscountRequest discountRequest);

    ExpensePageDto getToBeBilledExpensePage(ExpenseFilterRequest filterRequest, int page, int size);

    TotalAmount getTotalAmountForDiscount(List<Long> ids);

    List<Expense> getToBeBilledExpenses(List<Long> ids, LocalDate billingPeriodEnds);

    ExpensePageDto getBilledExpenses(ExpenseFilterRequest filterRequest, int page, int size);

    List<Expense> getUnbilledExpenses(List<Long> ids);

    Boolean isBillingApprovableExpenses(Long activityId, LocalDate billingPeriodEnds);

    void deleteExpensesRelatedWithTimesheetAndDisbursement(List<Expense> expenses);

    void resetBillingData(List<Expense> expenses);

    Boolean isBillingCancellableExpenses(Long activityId);

    ExpenseSummaryPageDto getOrderCreationExpenses(String assignee, int page, int size);

    boolean hasOrderableExpense(List<Long> expenseIds);

    List<ExpenseSummaryDto> processOrderCreate(OrderCreateRequest orderCreateRequest);

    List<ExpenseDto> processOrderSplit(Long activityId, List<OrderCreateRequest> orderCreateRequests);

    Boolean isBillable(List<Long> expenseIds);

    List<ExpenseDto> getExpensesByOrderNo(String orderNo);

    List<Expense> getExpenseEntitiesByOrderNo(String orderNo);

    List<Expense> getExpensesByIdsAndOrderNo(List<Long> ids, String orderNo);

    List<Long> getExpenseIdsByOrderNo(String orderNo);

    List<String> getOrderNumbersList();

    List<String> getInvoiceNumbersList();

    ExpensePageDto getAll(ExpenseFilterRequest filterRequest, int page, int size);

    void updateInvoiceFields(InvoiceFetchedEvent.Invoice invoice);

    Boolean isInvoiceCompleted(LocalDate billingPeriodEnds, List<Long> expenseIds);

    boolean hasBillableExpenseByBillingPeriodEnds(List<Long> expenseIds, LocalDate billingPeriodEnds);

    boolean hasUnapprovedExpense(List<Long> expenseIds, LocalDate billingPeriodEnds);

    boolean isInvoiceCompleted(String orderNumber);

    void clearDiscountRate(List<Long> expenseIds, LocalDate billingPeriodEnds);

    List<ExpenseDto> getByActivityId(Long activityId);
}
