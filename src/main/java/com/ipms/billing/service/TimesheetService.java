package com.ipms.billing.service;

import com.ipms.billing.dto.*;
import com.ipms.billing.model.Timesheet;
import com.ipms.core.entity.Revision;

import java.math.BigDecimal;
import java.time.LocalDate;
import java.util.List;

public interface TimesheetService {
    TimesheetDto save(TimesheetDto timesheetDto);
    TimesheetDto getById(Long id);
    TimesheetSummaryDto getAllByDates(LocalDate startDate, LocalDate endDate);
    TimesheetTotalTimeDto getAllByDates(Long activityId, LocalDate startDate, LocalDate endDate);
    TimesheetPageDto getByActivity(Long activityId, int page, int size);
    TimesheetDto update(TimesheetDto dto);
    void delete(Long id, Long version);
    List<TimesheetDto> getByIdIn(List<Long> ids);
    TimesheetTotalTimeDto getTimesByActivity(Long activityId);
    TimesheetTotalTimeDto getTimesByActivityAndStartDate(Long activityId, LocalDate startDate);
    TimesheetTotalTimeDto getTimesByActivityAndStartDateForFds(Long activityId, LocalDate startDate);
    TimesheetTotalTimeDto getTimesByActivityAndEndDate(Long activityId, LocalDate endDate);
    TimesheetTotalTimeDto getUnbilledTimesByActivity(Long activityId);
    List<TimesheetDto> getUnbilledTimesheetsByActivityId(Long activityId);
    TimesheetTotalTimeDto getUnbilledTimes(Long activityId, LocalDate billingPeriodEnds);
    TimesheetPageDto getUnbilledTimesheetsPage(Long activityId, LocalDate billingPeriodEnds, int page, int size);
    TimesheetTotalTimeDto getOtherUnbilledTimes(Long activityId, LocalDate billingPeriodEnds);
    TimesheetPageDto getOtherUnbilledPage(Long activityId, LocalDate billingPeriodEnds, int page, int size);
    List<Revision> getRevisions(Long id);
    List<Revision> getRevisionsByField(Long id, String field);
    TimesheetPageDto getPage(TimesheetFilterRequest filterRequest, int page, int size);
    List<TimesheetDto> getAll(TimesheetFilterRequest filterRequest);
    List<TimesheetDto> approveBulk(List<Long> ids);
    List<TimesheetDto> approveBillingBulk(List<Long> ids);
    List<TimesheetDto> cancelApprovelBillingBulk(List<Long> ids);
    List<Timesheet> getUnbilledTimesheets(Long activityId, LocalDate billingPeriodEnds);
    List<Timesheet> getUnbilledTimesheetsAfterPeriod(Long activityId, LocalDate billingPeriodEnds);
    void approveBySystem(LocalDate beforeDate);
    void calculateIncludedInCharge(Long activityId, LocalDate packageStartDate, BigDecimal tsicIncludedCharge);
    void changeActivity(TimesheetChangeActivityRequest changeActivityRequest);
    void approveBilling(ApproveBillingRequest approveBillingRequest);
    Boolean isBillingApprovableTimesheets(Long activityId, LocalDate billingPeriodEnds);
    Boolean hasBillableTimesheet(Long activityId, LocalDate billingPeriodEnds);
    void updateTimesheetsForBillingCancellation(Long activityId, LocalDate billingPeriodEnds);
    void undoBillingApprovalTimesheets(Long activityId, LocalDate billingPeriodEnds);
    Boolean splitTimesheet(Long timesheetId, LocalDate billingPeriodEnds);
    boolean hasUnapproved(Long activityId, LocalDate billingPeriodEnds);
    void cancelBillingOrder(List<Long> timesheetIds);
    List<TimesheetDto> updateIncludedInChargeBulk(List<Long> ids, Boolean includedInCharge);
}
