package com.ipms.billing.service;

import com.ipms.billing.dto.ApproveBillingRequest;
import com.ipms.billing.dto.DisbursementDto;
import com.ipms.billing.dto.DisbursementFilterRequest;
import com.ipms.billing.dto.DisbursementPageDto;

import java.time.LocalDate;
import java.util.List;

public interface DisbursementService {
    DisbursementDto save(DisbursementDto dto);
    DisbursementDto update(Long id, DisbursementDto dto);
    DisbursementPageDto getToBeBilledDisbursements(DisbursementFilterRequest filterRequest, int page, int size);
    DisbursementPageDto getBilledDisbursements(DisbursementFilterRequest filterRequest, int page, int size);
    DisbursementPageDto getByIds(List<Long> disbursement, int page, int size);
    void delete(Long id, Long version);
    List<DisbursementDto> getByIdIn(List<Long> ids);
    List<DisbursementDto> approveBillingBulk(List<Long> ids);
    List<DisbursementDto> cancelApprovelBillingBulk(List<Long> ids);
    void approveBilling(ApproveBillingRequest approveBillingRequest);
    Boolean isBillingApprovableDisbursements(Long activityId, LocalDate billingPeriodEnds);
    Boolean isBillingCancellableDisbursements(Long activityId);
    Boolean hasBillable(List<Long> disbursementIds, List<Long> expenseIds);
    boolean isTotalAmountMoreThanBillingAmount(List<Long> disbursementIds, List<Long> expenseIds);
    boolean isTotalAmountMoreThanZero(List<Long> disbursementIds, List<Long> expenseIds);
    void undoBillingApproval(List<Long> disbursementIds, LocalDate billingPeriodEnds);
    boolean hasBillableDisbursementByBillingPeriodEnds(List<Long> disbursementIds, LocalDate billingPeriodEnds);
    boolean hasUnapprovedDisbursement(List<Long> disbursementIds, LocalDate billingPeriodEnds);
    void cancelBillingOrder(List<Long> disbursements);
    void updateDisbursementsForBillingCancellation(List<Long> ids, LocalDate billingPeriodEnds);

    List<DisbursementDto> getByActivityId(Long activityId);
}
