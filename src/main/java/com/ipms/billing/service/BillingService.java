package com.ipms.billing.service;

import java.time.LocalDate;
import java.util.List;

public interface BillingService {

    boolean hasBillableExpenseOrDisbursement(List<Long> expenseIds, List<Long> disbursementIds,
                                             LocalDate billingPeriodEnds);

    boolean hasUnapprovedExpenseOrDisbursement(List<Long> expenseIds, List<Long> disbursementIds,
                                               LocalDate billingPeriodEnds);
}
