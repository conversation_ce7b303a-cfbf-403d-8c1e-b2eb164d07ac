package com.ipms.billing.service;

import com.ipms.billing.dto.*;
import com.ipms.billing.model.BillingAccount;

import java.time.LocalDate;
import java.util.List;

public interface BillingAccountService {
    BillingAccountDto save(BillingAccountDto billingAccountDto);

    BillingAccountDto update(Long id, BillingAccountDto billingAccountDto);

    BillingAccountDto getById(Long id);

    BillingAccount getBillingAccountById(Long id);

    void delete(Long id, Long version);

    List<BillingAccountDto> getAll(BillingAccountFilterRequest filterRequest);

    BillingAccountPageDto getAllPageable(BillingAccountFilterRequest filterRequest, int page, int size);

    BillingAccountDto addIssue(Long id, Long issueId);

    BillableBillingAccount isBillable(Long id, List<LocalDate> favorableDates);

    List<BillingAccountDto> getByIdIn(List<Long> ids);

    void processJdePriceEvent(PriceEvent priceEvent);
}
