package com.ipms.billing.service;

import com.ipms.billing.dto.*;

import java.util.List;

public interface BillingOrderService {
    BillingOrderDto save(BillingOrderDto billingOrderDto);

    BillingOrderDto saveWithExpenses(BillingOrderCreateDto billingOrderCreateDto);

    BillingOrderDto getById(Long id);

    void delete(Long id);

    BillingOrderPageDto getPageDto(BillingOrderFilterRequest filterRequest, int page, int size);

    List<BillingOrderDto> saveBulkWithExpenses(List<BillingOrderCreateDto> billingOrderDtos);

    List<BillingOrderDto> getByIdIn(List<Long> ids);

    void updateExternalOrderNumber(BillingIntegrationUpdateEvent billingIntegrationUpdateEvent);

    void cancel(Long id);

    void processInvoiceFetchedEvent(InvoiceFetchedEvent.Invoice invoice);

    void processBillingApprovedEvent(BillingApprovedEvent billingApprovedEvent);

    void processOrderIntegrationErrorEvent(String orderNumber);

    void processOrderIntegrationSuccessEvent(String orderNumber);
}
