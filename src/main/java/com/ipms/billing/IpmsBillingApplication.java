package com.ipms.billing;

import com.ipms.core.common.constants.Constants;
import org.springframework.boot.SpringApplication;
import org.springframework.boot.autoconfigure.SpringBootApplication;
import org.springframework.cloud.openfeign.EnableFeignClients;

@SpringBootApplication(scanBasePackages = Constants.BASE_PACKAGE)
@EnableFeignClients(basePackages = Constants.BASE_PACKAGE)
public class IpmsBillingApplication {

	public static void main(String[] args) {
		SpringApplication.run(IpmsBillingApplication.class, args);
	}

}
