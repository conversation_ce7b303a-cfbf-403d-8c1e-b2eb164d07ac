package com.ipms.billing.specification;

import com.ipms.billing.model.Timesheet;
import com.ipms.core.specification.AbstractSpecification;
import lombok.experimental.SuperBuilder;
import org.springframework.format.annotation.DateTimeFormat;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDate;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class TimesheetSpecification extends AbstractSpecification<Timesheet> {
    private List<Long> activityIds;
    private List<Long> activityIdsNotIn;
    private Long activityId;
    private List<Long> matterIds;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate startDate;
    @DateTimeFormat(iso = DateTimeFormat.ISO.DATE)
    private LocalDate endDate;
    private Boolean isApproved;
    private List<String> createdBys;

    @Override
    public Predicate toPredicate(Root<Timesheet> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);

        var activityIdColumn = "activityId";
        Optional.ofNullable(activityId)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get(activityIdColumn), value)));
        Optional.ofNullable(activityIds)
                .ifPresent(value -> predicate.getExpressions().add(root.get(activityIdColumn).in(value)));
        Optional.ofNullable(activityIdsNotIn)
                .ifPresent(value -> predicate.getExpressions().add(cb.not(root.get(activityIdColumn).in(value))));
        Optional.ofNullable(startDate)
                .ifPresent(value -> predicate.getExpressions().add(cb.greaterThanOrEqualTo(root.get("date"), value)));
        Optional.ofNullable(endDate)
                .ifPresent(value -> predicate.getExpressions().add(cb.lessThan(root.get("date"), value)));
        Optional.ofNullable(isApproved)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("isApproved"), value)));
        Optional.ofNullable(matterIds)
                .ifPresent(value -> predicate.getExpressions().add(root.get("matterId").in(value)));
        Optional.ofNullable(createdBys)
                .ifPresent(value -> predicate.getExpressions().add(root.get("createdBy").in(value)));
        return predicate;
    }
}
