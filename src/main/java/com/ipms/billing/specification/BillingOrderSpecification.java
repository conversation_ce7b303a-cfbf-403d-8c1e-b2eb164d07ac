package com.ipms.billing.specification;

import com.ipms.billing.enums.BillingOrderStatus;
import com.ipms.billing.model.BillingOrder;
import com.ipms.core.specification.AbstractSpecification;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class BillingOrderSpecification extends AbstractSpecification<BillingOrder> {
    private final List<String> orderNumbers;
    private final String assignee;
    private final String invoiceNumber;
    private final String orderNumber;
    private final List<Long> issuerIds;
    private final List<String> assignees;
    private final List<BillingOrderStatus> statuses;

    @Override
    public Predicate toPredicate(Root<BillingOrder> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);
        assert predicate != null;

        Optional.ofNullable(orderNumbers)
                .ifPresent(values -> predicate.getExpressions().add(cb.and(root.get("orderNumber").in(values))));
        Optional.ofNullable(assignee)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("assignee"), value)));
        Optional.ofNullable(invoiceNumber)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("invoiceNumber"), value)));
        Optional.ofNullable(orderNumber)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("orderNumber"), value)));
        Optional.ofNullable(issuerIds)
                .ifPresent(values -> predicate.getExpressions().add(cb.and(root.get("issuerId").in(values))));
        Optional.ofNullable(assignees)
                .ifPresent(values -> predicate.getExpressions().add(cb.and(root.get("assignee").in(values))));
        Optional.ofNullable(statuses)
                .ifPresent(values -> predicate.getExpressions().add(cb.and(root.get("status").in(values))));

        return predicate;
    }
}
