package com.ipms.billing.specification;

import com.ipms.billing.enums.ToBeBilledChoice;
import com.ipms.billing.model.Disbursement;
import com.ipms.core.specification.AbstractSpecification;
import lombok.experimental.SuperBuilder;
import org.apache.commons.collections4.CollectionUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDate;
import java.util.Optional;

@SuperBuilder
public class DisbursementToBeBilledSpecification extends AbstractSpecification<Disbursement> {

    private final LocalDate billingPeriodEnds;
    private final ToBeBilledChoice toBeBilled;

    @Override
    public Predicate toPredicate(Root<Disbursement> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);

        if (CollectionUtils.isEmpty(this.ids)) {
            return cb.disjunction();
        }

        predicate.getExpressions().add(cb.and(
                cb.lessThanOrEqualTo(cb.function("DATE", LocalDate.class, root.get("disbursementDate")),
                        billingPeriodEnds),
                cb.or(
                        cb.isNull(root.get("expenseDate")),
                        cb.equal(cb.function("DATE", LocalDate.class, root.get("expenseDate")),
                                billingPeriodEnds)
                ))
        );

        Optional.ofNullable(toBeBilled)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("toBeBilled"), value)));

        return predicate;
    }
}
