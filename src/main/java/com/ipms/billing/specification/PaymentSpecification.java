package com.ipms.billing.specification;

import com.ipms.billing.enums.PaymentStatus;
import com.ipms.billing.model.Payment;
import com.ipms.core.specification.AbstractSpecification;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class PaymentSpecification extends AbstractSpecification<Payment> {
    private List<String> accrualNumbers;
    private List<PaymentStatus> statuses;
    private List<Long> issuerIds;
    private List<Long> matterIds;

    @Override
    public Predicate toPredicate(Root<Payment> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        Predicate predicate = super.toPredicate(root, query, cb);

        Optional.ofNullable(accrualNumbers)
                .ifPresent(values -> predicate.getExpressions().add(root.get("accrualNumber").in(values)));

        Optional.ofNullable(statuses)
                .ifPresent(values -> predicate.getExpressions().add(root.get("status").in(values)));

        Optional.ofNullable(issuerIds)
                .ifPresent(values -> predicate.getExpressions().add(root.get("issuerId").in(values)));

        Optional.ofNullable(matterIds)
                .ifPresent(values -> predicate.getExpressions().add(root.get("matterId").in(values)));

        return predicate;
    }
}
