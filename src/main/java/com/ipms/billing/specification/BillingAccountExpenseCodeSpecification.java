package com.ipms.billing.specification;

import com.ipms.billing.enums.ActivityType;
import com.ipms.billing.model.BillingAccountExpenseCode;
import com.ipms.core.specification.AbstractSpecification;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.Optional;

@SuperBuilder
public class BillingAccountExpenseCodeSpecification extends AbstractSpecification<BillingAccountExpenseCode> {

    private final Long billingAccountId;
    private final String generalExpenseCode;
    private final String countryCode;
    private final String protectionType;
    private final String activityType;

    @Override
    public Predicate toPredicate(Root<BillingAccountExpenseCode> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);
        assert predicate != null;

        Optional.ofNullable(billingAccountId)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("billingAccountId"), value)));

        Optional.ofNullable(generalExpenseCode)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("generalExpenseCode"), value)));

        Optional.ofNullable(countryCode)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("countryCode"), value)));

        Optional.ofNullable(protectionType)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("protectionType"), value)));

        Optional.ofNullable(activityType)
                .ifPresent(value -> predicate.getExpressions().add(cb.equal(root.get("activityType") , ActivityType.valueOf(value))));

        return predicate;
    }
}
