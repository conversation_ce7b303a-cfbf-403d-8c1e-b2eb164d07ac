package com.ipms.billing.specification;

import com.ipms.billing.model.Expense;
import com.ipms.core.specification.AbstractSpecification;
import lombok.experimental.SuperBuilder;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.util.List;
import java.util.Optional;

@SuperBuilder
public class ExpenseSpecification extends AbstractSpecification<Expense> {
    private List<String> orderNumbers;
    private List<String> invoiceNumbers;

    @Override
    public Predicate toPredicate(Root<Expense> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);

        Optional.ofNullable(orderNumbers)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("orderNumber").in(value))));
        Optional.ofNullable(invoiceNumbers)
                .ifPresent(value -> predicate.getExpressions().add(cb.and(root.get("invoiceNumber").in(value))));

        return predicate;
    }
}
