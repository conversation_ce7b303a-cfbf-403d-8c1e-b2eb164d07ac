package com.ipms.billing.specification;

import com.ipms.billing.model.Expense;
import com.ipms.core.specification.AbstractSpecification;
import lombok.experimental.SuperBuilder;
import org.apache.commons.collections4.CollectionUtils;

import javax.persistence.criteria.CriteriaBuilder;
import javax.persistence.criteria.CriteriaQuery;
import javax.persistence.criteria.Predicate;
import javax.persistence.criteria.Root;
import java.time.LocalDate;

@SuperBuilder
public class ExpenseToBeBilledSpecification extends AbstractSpecification<Expense> {

    private final LocalDate billingPeriodEnds;

    @Override
    public Predicate toPredicate(Root<Expense> root, CriteriaQuery<?> query, CriteriaBuilder cb) {
        var predicate = super.toPredicate(root, query, cb);

        if (CollectionUtils.isEmpty(this.ids)) {
            return cb.disjunction();
        }

        predicate.getExpressions().add(cb.and(
                cb.lessThanOrEqualTo(cb.function("DATE", LocalDate.class, root.get("expenseDate")),
                                billingPeriodEnds),
                cb.or(
                        cb.isNull(root.get("orderDate")),
                        cb.equal(cb.function("DATE", LocalDate.class, root.get("orderDate")),
                                billingPeriodEnds)
                ))
        );

        return predicate;
    }
}
