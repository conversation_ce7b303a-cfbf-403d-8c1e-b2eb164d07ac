package com.ipms.billing.consumer;

import com.ipms.billing.dto.*;
import com.ipms.billing.service.BillingOrderService;
import com.ipms.core.common.utils.ObjectUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Slf4j
public class BillingOrderConsumer {

    private final BillingOrderService billingOrderService;

    @KafkaListener(topics = "${kafka.billing-order-integration-update-topic}")
    public void consumeBillingOrder(String eventStr) {
        billingOrderService.updateExternalOrderNumber(ObjectUtils.fromJson(eventStr, BillingIntegrationUpdateEvent.class));
    }

    @KafkaListener(topics = "${kafka.invoice-fetched-topic}")
    public void consumerInvoiceFetchedTopic(String eventStr) {
            var invoiceFetchedEvent = ObjectUtils.fromJson(eventStr, InvoiceFetchedEvent.class);
            invoiceFetchedEvent.getInvoices().forEach(invoice -> {
                try {
                    billingOrderService.processInvoiceFetchedEvent(invoice);
                } catch (Exception e) {
                    log.error("Error processing invoice fetched event for invoice: {}", invoice, e);
                }
            });
    }

    @KafkaListener(topics = "${kafka.billing-approved-topic}")
    public void consumerBillingApproved(String eventStr) {
        billingOrderService.processBillingApprovedEvent(ObjectUtils.fromJson(eventStr, BillingApprovedEvent.class));
    }

    @KafkaListener(topics = "${kafka.billing-order-integration-error-topic}")
    public void consumeBillingOrderIntegrationError(String eventStr) {
        var event = ObjectUtils.fromJson(eventStr, BillingIntegrationErrorEvent.class);
        billingOrderService.processOrderIntegrationErrorEvent(event.getOrderNumber());
    }

    @KafkaListener(topics = "${kafka.billing-order-integration-success-topic}")
    public void consumeBillingOrderIntegrationSuccess(String eventStr) {
        var event = ObjectUtils.fromJson(eventStr, BillingIntegrationSuccessEvent.class);
        billingOrderService.processOrderIntegrationSuccessEvent(event.getOrderNumber());
    }
}
