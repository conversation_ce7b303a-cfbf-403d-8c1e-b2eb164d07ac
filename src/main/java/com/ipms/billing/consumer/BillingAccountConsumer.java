package com.ipms.billing.consumer;

import com.ipms.billing.dto.PriceEvent;
import com.ipms.billing.service.BillingAccountService;
import com.ipms.core.common.utils.ObjectUtils;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.kafka.annotation.KafkaListener;
import org.springframework.stereotype.Component;

@RequiredArgsConstructor
@Component
@Slf4j
public class BillingAccountConsumer {

    private final BillingAccountService billingAccountService;

    @KafkaListener(topics = "${kafka.jde-price-topic}")
    public void consumerJdePrice(String eventStr) {
        billingAccountService.processJdePriceEvent(ObjectUtils.fromJson(eventStr, PriceEvent.class));
    }
}
