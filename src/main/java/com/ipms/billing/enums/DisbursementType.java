package com.ipms.billing.enums;

import java.util.Arrays;

public enum DisbursementType {

    TRAVEL_EXPENSES("1"),
    EXPENSES_FOR_AGREEMENT("2"),
    NOTARY_EXPENSES("3"),
    COURT_EXPENSES("4"),
    INDIVIDUAL_FEES("5"),
    CORRESPONDENT_FEES("6"),
    SEDIN_OFFICIAL_INDIVIDUAL_FEE("7"),
    SEDIN_OFFICIAL_FEE_FOR_DOMAIN("8"),
    SEDIN_OFFICIAL_FEE_FOR_TRADEMARK("9"),
    CALLING_USA("10"),
    FIRST_LEVEL_PHONE_COST_PER_MINUTE("11"),
    SECOND_LEVEL_PHONE_COST_PER_MINUTE("12"),
    THIRD_LEVEL_PHONE_COST_PER_MINUTE("13"),
    FOURTH_LEVEL_PHONE_COST_PER_MINUTE("14"),
    PHOTOCOPY_COSTS("15"),
    COURIER_COSTS("16"),
    COLOURED_COSTS("17"),
    OFFICIAL_FEES("18"),
    EXPERT_FEES("19"),
    GUARANTY("20"),
    INVESTIGATION_COST("21"),
    SAMPLE_PURCHASE_COST("22"),
    STORAGE("23"),
    ENFORCEMENT("24"),
    INTERCITY_PHONE_FEE_PER_MINUTE("25"),
    DOMESTIC_PHONE_FEE_PER_MINUTE("26"),
    INTERNATIONAL_PHONE_FEE_PER_MINUTE("27"),
    CERTIFICATION_EXPENSES("28"),
    KADIR_KARASU_OPERATIONAL_FEES("29"),
    ORHAN_ERBIL_COURT_ACTION_FEES("30"),
    SERHAT_TEPE("31"),
    APB_MARAS_METER("32"),
    LOCAL_ATTORNEY_COURT_ACTION_FEES("33"),
    TRADEMARK_APPLICATION_OFFICIAL_FEE("34"),
    TRADEMARK_APPLICATION_LETTER_ADDITIONAL_FEES("35"),
    TRADEMARK_PRIOR_REGISTRATION_ADDITIONAL_FEES("36"),
    ASSOCIATE_FEES_OF_SEDIN("37"),
    DISBURSEMENT_OF_CUSTOMS_TRAINING("38"),
    POLICE_RAID_EVIDENTIAL_ACTION_DISBURSEMENT("39");

    private final String value;

    DisbursementType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static DisbursementType getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(() -> new IllegalArgumentException(value));
    }
}
