package com.ipms.billing.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;
import lombok.Getter;

public enum BillingAccountResponseCode implements Code {
    BILLING_ACCOUNT_NOT_FOUND(1000, "error.code.billing.billing_account.not_found"),
    ACCOUNT_NO_AND_ISSUER_MUST_BE_UNIQUE(1001,
            "error.code.billing.billing_account.account_no_and_issuer_must_be_unique"),
    ACCOUNT_NAME_MUST_BE_UNIQUE(1002,"error.code.billing.billing_account.account_name");

    private final Integer code;

    @Getter
    private final String messageKey;

    BillingAccountResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

}
