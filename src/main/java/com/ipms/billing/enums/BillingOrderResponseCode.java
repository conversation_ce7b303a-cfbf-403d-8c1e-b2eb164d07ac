package com.ipms.billing.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;
import lombok.Getter;

public enum BillingOrderResponseCode implements Code {
    BILLING_ORDER_NOT_FOUND(1000, "error.code.billing.billing_order.not_found"),
    BILLING_ORDER_CANNOT_CANCELLED(1001, "error.code.billing.billing_order.cancel");

    private final Integer code;

    @Getter
    private final String messageKey;

    BillingOrderResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

}
