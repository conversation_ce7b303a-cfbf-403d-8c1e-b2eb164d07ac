package com.ipms.billing.enums;

import java.util.Arrays;

public enum ExpenseCodeType {
    SERVICE_FEE("S"),
    OFFICIAL_FEE("O"),
    TRANSLATION_FEE("T");

    private final String value;

    ExpenseCodeType(String value) {
        this.value = value;
    }

    public String getValue() {
        return value;
    }

    public static ExpenseCodeType getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
