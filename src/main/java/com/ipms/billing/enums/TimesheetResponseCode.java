package com.ipms.billing.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum TimesheetResponseCode implements Code {
    BILLABLE_TIME_CAN_NOT_BE_BIGGER_THAN_LOGGED_TIME(1000, "error.code.timesheet.billable_time_can_not_be_bigger_than_logged_time"),
    CAN_NOT_LOG_ANYMORE(1002, "error.code.timesheet.can_not_log_anymore"),
    TIMESHEET_NOT_FOUND(1003,"error.code.timesheet_not_found"),
    TIMESHEET_CANNOT_BE_UPDATED(1004, "error.code.timesheet_cannot_be_updated"),
    ALL_TIMESHEETS_MUST_BE_APPROVED(1005, "error.code.all_timesheets_must_be_approved"),
    TIMESHEET_DATE_BEFORE_PACKAGE_START_DATE(1006, "error.code.timesheet.date_before_package_start_date"),
    TIMESHEET_DATE_BEFORE_PACKAGE_END_DATE(1007, "error.code.timesheet.date_before_package_end_date");

    private final Integer code;
    private final String messageKey;

    TimesheetResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
