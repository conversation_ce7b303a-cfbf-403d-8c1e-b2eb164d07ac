package com.ipms.billing.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum PaymentResponseCode implements Code {

    PAYMENT_NOT_FOUND(1001, "error.code.payment.payment_not_found");

    private final Integer code;
    private final String messageKey;

    PaymentResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}

