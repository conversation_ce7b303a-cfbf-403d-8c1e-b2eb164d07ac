package com.ipms.billing.enums;

import java.util.Arrays;

public enum ToBeBilledChoice {
    YES("1"),
    NO("2"),
    NO_DEBIT_NOT_WILL_BE_SENT("3");

    private final String value;

    ToBeBilledChoice(String value) { this.value = value; }

    public String getValue() {
        return value;
    }

    public static ToBeBilledChoice getEnum(String value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equalsIgnoreCase(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
