package com.ipms.billing.enums;

public enum CoreBusinessBusinessUnit {
    EMPTY                          	("410610"),
    PARALLEL_IMPORT                	("410610"),
    PATENT_ENFORCEMENT            	("410640"),
    PLANT_VARIETY                 	("410610"),
    ANTI_COUNTERFEITING_DIGITAL   	("410620"),
    ANTI_COUNTERFEITING_NON_DIGITAL	("410620"),
    ANTI_COUNTERFEITING_GUMRUK      ("410620"),
    ANTI_PIRACY_DIGITAL           	("410630"),
    ANTI_PIRACY_NON_DIGITAL       	("410630"),
    PATENT_ANTI_COUNTERFEITING    	("410640"),
    DATA_PROTECTION               	("410610"),
    TR_BASED                      	("410610");

    private final String businessUnit;

    CoreBusinessBusinessUnit(String businessUnit) {
        this.businessUnit = businessUnit;
    }

    public String getBusinessUnit() {
        return businessUnit;
    }

}
