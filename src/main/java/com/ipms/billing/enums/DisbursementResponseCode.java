package com.ipms.billing.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum DisbursementResponseCode implements Code {

    DISBURSEMENT_NOT_FOUND(1000, "error.code.disbursement.not_found"),
    DISBURSEMENT_TYPE_NOT_FOUND(1001, "error.code.disbursement_type.not_found"),
    DISBURSEMENT_TYPE_ALREADY_EXIST(1002, "error.code.disbursement_type.already_exist"),
    ALL_DISBURSEMENTS_MUST_BE_APPROVED(1003, "error.code.all_disbursements_must_be_approved"),
    EXCHANGE_RATE_NOT_FOUND(1004, "error.code.exchange_rate_not_found");

    private final Integer code;
    private final String messageKey;

    DisbursementResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
