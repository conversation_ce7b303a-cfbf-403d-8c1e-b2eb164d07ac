package com.ipms.billing.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum ExpenseResponseCode implements Code {

    EXPENSE_NOT_FOUND(1000, "error.code.expense.not_found"),
    BILLING_ACCOUNT_NOT_SAME(1001, "error.code.expense.billing_account_not_same"),
    CURRENCY_NOT_SAME(1002, "error.code.expense.currency_not_same"),
    EXPENSE_NOT_DIVISIBLE(1003, "error.code.expense.expense_not_divisible"),
    CURRENCY_NOT_SAME_AS_BILLING_ACCOUNT(1004, "error.code.expense.currency_not_same_as_billing_account"),
    ACCRUAL_NUMBER_EDIT_NOT_ALLOWED(1005, "error.code.expense.accrual_number_edit_not_allowed");

    private final Integer code;
    private final String messageKey;

    ExpenseResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
