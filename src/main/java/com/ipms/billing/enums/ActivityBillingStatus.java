package com.ipms.billing.enums;

import org.apache.commons.lang3.StringUtils;

import java.util.EnumSet;

public enum ActivityBillingStatus {
    BILLING_STARTED,
    REVISION_REQUESTED,
    REVISION_COMPLETED,
    APPROVED,
    ORDER_CREATED,
    ORDER_INTEGRATED,
    ERROR_IN_ORDER,
    INVOICE_IS_READY,
    WAITING_IN_BILLING_CART;

    private static final EnumSet<ActivityBillingStatus> DELETE_EXPENSES_STATUSES =
            EnumSet.of(BILLING_STARTED, REVISION_REQUESTED, REVISION_COMPLETED, WAITING_IN_BILLING_CART);

    private static final EnumSet<ActivityBillingStatus> NULLIFY_EXPENSES_STATUSES =
            EnumSet.of(ORDER_CREATED, ORDER_INTEGRATED, ERROR_IN_ORDER, INVOICE_IS_READY);

    public static boolean isDeleteExpensesStatus(String status) {
        return StringUtils.isBlank(status) || DELETE_EXPENSES_STATUSES.contains((valueOf(status)));
    }

    public static boolean isNullifyExpensesStatus(String status) {
        return StringUtils.isNotBlank(status) && NULLIFY_EXPENSES_STATUSES.contains(valueOf(status));
    }
}
