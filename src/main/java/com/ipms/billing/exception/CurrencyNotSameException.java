package com.ipms.billing.exception;

import com.ipms.billing.enums.ExpenseResponseCode;
import com.ipms.core.exception.BaseException;

public class CurrencyNotSameException extends BaseException {
    public CurrencyNotSameException(Object[] args) {
        super(ExpenseResponseCode.CURRENCY_NOT_SAME, args);
    }

    public CurrencyNotSameException(ExpenseResponseCode expenseResponseCode) {
        super(expenseResponseCode);
    }
}
