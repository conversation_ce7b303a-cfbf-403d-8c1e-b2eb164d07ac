package com.ipms.billing.exception;

import com.ipms.billing.enums.TimesheetResponseCode;
import com.ipms.core.exception.BaseException;

public class TimesheetValidationException extends BaseException {
    public TimesheetValidationException(TimesheetResponseCode validationResponseCode) {
        super(validationResponseCode);
    }

    public TimesheetValidationException(TimesheetResponseCode validationResponseCode, Object... params) {
        super(validationResponseCode, params);
    }
}
