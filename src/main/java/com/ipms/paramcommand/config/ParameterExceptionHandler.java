package com.ipms.paramcommand.config;

import com.ipms.core.exception.BaseResponse;
import com.ipms.core.exception.ResponseCode;
import com.ipms.core.exception.config.GlobalExceptionHandler;
import com.ipms.core.i18n.service.II18nMessageService;
import com.ipms.paramcommand.enums.ParameterResponseCode;
import org.springframework.dao.DataIntegrityViolationException;
import org.springframework.http.HttpStatus;
import org.springframework.web.bind.annotation.ExceptionHandler;
import org.springframework.web.bind.annotation.ResponseStatus;
import org.springframework.web.bind.annotation.RestControllerAdvice;

import java.util.Locale;

@RestControllerAdvice
public class ParameterExceptionHandler extends GlobalExceptionHandler {
    private final II18nMessageService ii18nMessageService;

    public ParameterExceptionHandler(II18nMessageService ii18nMessageService) {
        super(ii18nMessageService);
        this.ii18nMessageService = ii18nMessageService;
    }

    @ResponseStatus(HttpStatus.BAD_REQUEST)
    @ExceptionHandler({
            DataIntegrityViolationException.class
    })
    public BaseResponse<Object> handleParameterExceptions(Locale locale) {
        String message = this.ii18nMessageService.getMessage(ParameterResponseCode.PARAMETER_DUPLICATE_KEY.getMessageKey(), locale);
        return BaseResponse.builder()
                .code(ResponseCode.VALIDATION_ERROR)
                .message(message)
                .build();
    }
}
