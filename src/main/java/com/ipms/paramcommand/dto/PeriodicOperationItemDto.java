package com.ipms.paramcommand.dto;

import com.ipms.paramcommand.enums.PeriodicOperationBaseDateType;
import com.ipms.paramcommand.enums.PeriodicOperationPeriodType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PeriodicOperationItemDto {
    private Long id;
    private Long countryId;
    private Long ipOfficeId;
    private String matterType;
    private PeriodicOperationPeriodType period;
    private Integer value;
    private PeriodicOperationBaseDateType baseDate;
    private PeriodicOperationPeriodType nextPeriod;
    private Integer nextPeriodValue;
    private PeriodicOperationPeriodType additionalTimePeriod;
    private Integer additionalTimePeriodValue;
    private LocalDateTime createdAt;
    private LocalDate conditionOfValidityDate;
    private Long version;
}
