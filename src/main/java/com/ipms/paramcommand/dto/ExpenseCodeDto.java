package com.ipms.paramcommand.dto;

import com.ipms.paramcommand.dto.groups.CreateGroup;
import com.ipms.paramcommand.dto.groups.UpdateGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.Valid;
import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpenseCodeDto {
    @NotNull(groups = UpdateGroup.class)
    private Long id;
    @NotNull(groups = UpdateGroup.class)
    private Long version;
    @NotBlank(groups = {UpdateGroup.class, CreateGroup.class})
    private String code;
    @NotBlank(groups = {UpdateGroup.class, CreateGroup.class})
    private String type;
    private LocalDate expiryDate;
    @NotBlank(groups = {UpdateGroup.class, CreateGroup.class})
    private String definition;
    @Valid
    private List<CurrencyUnitPriceDto> currencyUnitPrices;
}
