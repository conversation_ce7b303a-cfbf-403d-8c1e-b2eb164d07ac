package com.ipms.paramcommand.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.io.Serializable;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityTypePageDto implements Serializable {
    private List<ActivityTypeDto> activityTypes;
    private long totalElements;
    private long totalPages;
}
