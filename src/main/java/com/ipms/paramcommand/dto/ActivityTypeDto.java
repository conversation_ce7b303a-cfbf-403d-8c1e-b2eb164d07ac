package com.ipms.paramcommand.dto;

import com.ipms.paramcommand.dto.groups.UpdateGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityTypeDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    
    @NotNull(groups = UpdateGroup.class)
    private Long id;
    @NotBlank
    private String country;
    @NotBlank
    private String matterType;
    @NotBlank
    private String activity;
    private List<ActivityStatusDto> statuses;
    private LocalDateTime createdAt;
    private Long version;
}