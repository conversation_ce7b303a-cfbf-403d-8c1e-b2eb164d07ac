package com.ipms.paramcommand.dto;

import com.ipms.paramcommand.dto.groups.CreateGroup;
import com.ipms.paramcommand.dto.groups.UpdateGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.NotNull;
import java.math.BigDecimal;
import java.time.LocalDate;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CurrencyUnitPriceDto {
    @NotNull
    private Long id;
    @NotNull
    private Long version;
    @NotBlank(groups = {UpdateGroup.class, CreateGroup.class})
    private String currency;
    @NotNull(groups = {UpdateGroup.class, CreateGroup.class})
    private BigDecimal unitPrice;
    private LocalDate fromDate;
    private LocalDate toDate;

}
