package com.ipms.paramcommand.dto;

import com.ipms.paramcommand.dto.groups.UpdateGroup;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotEmpty;
import javax.validation.constraints.NotNull;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ParameterDto {

    private Long id;
    @NotEmpty
    private String key;
    @NotEmpty
    private String value;
    @NotEmpty
    private String description;
    @NotNull(groups = UpdateGroup.class)
    private Long version;
}
