package com.ipms.paramcommand.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpenseCodeFilterRequest {
    private List<String> codes;
    private String code;
    private String definition;
    private List<String> types;
    private List<Long> ids;
    private String activityType;
    private String key;
}
