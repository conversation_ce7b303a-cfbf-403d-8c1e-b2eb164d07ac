package com.ipms.paramcommand.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class LocalAttorneyDto {
    private Long id;

    @NotBlank
    private String name;

    @NotBlank
    private String surname;

    private String city;
}
