package com.ipms.paramcommand.dto;

import com.ipms.paramcommand.enums.PeriodicOperationBaseDateType;
import com.ipms.paramcommand.enums.PeriodicOperationPeriodType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.time.LocalDate;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class PeriodicOperationDto {
    private List<Long> countryIds;
    private List<Long> ipOfficeIds;
    private String matterType;
    private PeriodicOperationPeriodType period;
    private Integer value;
    private PeriodicOperationBaseDateType baseDate;
    private PeriodicOperationPeriodType nextPeriod;
    private Integer nextPeriodValue;
    private PeriodicOperationPeriodType additionalTimePeriod;
    private Integer additionalTimePeriodValue;
    private LocalDate creationDate;
    private LocalDate conditionOfValidityDate;
}
