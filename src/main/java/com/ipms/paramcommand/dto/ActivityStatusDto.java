package com.ipms.paramcommand.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.io.Serial;
import java.io.Serializable;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityStatusDto implements Serializable {
    @Serial
    private static final long serialVersionUID = 1L;
    
    private Long id;

    @NotNull
    private String status;

    @NotNull
    private Long activityTypeId;

    private LocalDateTime createdAt;
}