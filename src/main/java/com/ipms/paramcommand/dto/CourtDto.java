package com.ipms.paramcommand.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotBlank;
import javax.validation.constraints.Size;
import java.util.List;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class CourtDto {
    private Long id;

    @NotBlank
    private String type;

    @NotBlank
    private String kind;

    @NotBlank
    @Size(max = 100)
    private String name;

    @NotBlank
    private String city;

    private List<String> judges;
}
