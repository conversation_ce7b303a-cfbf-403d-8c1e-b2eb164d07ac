package com.ipms.paramcommand.dto;

import com.ipms.paramcommand.enums.ExpenseCodeType;
import lombok.Builder;
import lombok.Data;
import org.apache.commons.lang3.StringUtils;

@Data
@Builder
public class PriceDto {
    private String mainCompany;
    private String itemNo;
    private String fromDate;
    private String toDate;
    private String customerorSupplierNumber;
    private String price;
    private String unitofMeasure;
    private String currencyCode;
    private String status;
    private String updateDate;

    public ExpenseCodeType getType() {
        return StringUtils.contains(itemNo, "*") ? ExpenseCodeType.OFFICIAL_FEE : ExpenseCodeType.SERVICE_FEE;
    }
}
