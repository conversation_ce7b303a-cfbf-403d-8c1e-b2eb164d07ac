package com.ipms.paramcommand.dto;

import com.ipms.paramcommand.enums.ExpenseCodeType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ExpenseCodeSummaryDto {
    private String definition;
    private BigDecimal unitPrice;
    private ExpenseCodeType type;
}
