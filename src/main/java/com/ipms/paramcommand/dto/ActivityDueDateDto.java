package com.ipms.paramcommand.dto;

import com.ipms.paramcommand.enums.ActivityBaseDateType;
import com.ipms.paramcommand.enums.ActivityPeriodType;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class ActivityDueDateDto {
    private Long id;

    @NotNull
    private String status;

    @NotNull
    private Long activityTypeId;

    @NotNull
    private ActivityBaseDateType baseDate;

    @NotNull
    private ActivityPeriodType period;

    @NotNull
    private int periodValue;

    private LocalDateTime createdAt;

}
