package com.ipms.paramcommand.dto;

import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

import javax.validation.constraints.NotNull;
import java.time.LocalDateTime;

@Data
@Builder
@NoArgsConstructor
@AllArgsConstructor
public class IssueTypeDto {
    private Long id;
    private String key;
    @NotNull
    private String type;
    @NotNull
    private String activityType;
    private String size;
    private int targetDateDay;
    private int replyDueDay;
    private LocalDateTime targetDate;
    private LocalDateTime replyDueDate;
}
