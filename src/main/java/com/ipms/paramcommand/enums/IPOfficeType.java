package com.ipms.paramcommand.enums;

import java.util.Arrays;

public enum IPOfficeType {
    TRADEMARK(0),
    PATENT(1),
    PLANT(2);

    private final Integer value;

    IPOfficeType(Integer value) {
        this.value = value;
    }

    public Integer getValue() {
        return value;
    }

    public static IPOfficeType getEnum(Integer value) {
        return Arrays.stream(values())
                .filter(v -> v.getValue().equals(value))
                .findFirst()
                .orElseThrow(IllegalArgumentException::new);
    }
}
