package com.ipms.paramcommand.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum PeriodicOperationResponseCode implements Code {
    PERIODIC_OPERATION_NOT_FOUND(1000, "error.code.periodic_operation.not_found"),
    PERIODIC_OPERATION_CONDITION_OF_VALIDITY_REQUIRED(1000, "error.code.periodic_operation.condition_of_validity_required");

    private final Integer code;
    private final String messageKey;

    PeriodicOperationResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}