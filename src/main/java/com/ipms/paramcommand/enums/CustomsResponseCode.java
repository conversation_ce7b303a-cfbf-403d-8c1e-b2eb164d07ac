package com.ipms.paramcommand.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum CustomsResponseCode implements Code {
    ALREADY_EXIST(1001, "error.code.customs.already_exist"),
    CUSTOMS_IN_USE(1002, "error.code.customs.customs_in_use");

    private final Integer code;
    private final String messageKey;

    CustomsResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
