package com.ipms.paramcommand.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum IPOfficeResponseCode implements Code {
    IP_OFFICE_NOT_FOUND(1000, "error.code.ip_office.not_found");

    private final Integer code;
    private final String messageKey;

    IPOfficeResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}