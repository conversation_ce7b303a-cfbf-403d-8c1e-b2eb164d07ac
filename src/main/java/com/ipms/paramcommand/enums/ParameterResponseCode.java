package com.ipms.paramcommand.enums;

import com.fasterxml.jackson.annotation.JsonValue;
import com.ipms.core.exception.Code;

public enum ParameterResponseCode implements Code {
    PARAMETER_NOT_FOUND(1000, "error.code.parameter.not_found"),
    PARAMETER_DUPLICATE_KEY(1001, "error.code.parameter.duplicate_key"),
    HOLIDAY_ALREADY_EXIST(1002, "error.code.holiday.already_exist"),
    HOLIDAY_NOT_FOUND(1003, "error.code.holiday.not_found"),
    COUNTRY_MATTER_TYPE_ALREADY_EXIST(1004, "error.code.record.already_exist"),
    COUNTRY_MATTER_TYPE_NOT_FOUND(1005, "error.code.country_matter_type.not_found"),
    ACTIVITY_TYPE_NOT_FOUND(1006, "error.code.activity_type.not_found"),
    ACTIVITY_TYPE_ALREADY_EXIST(1007, "error.code.record.already_exist"),
    EXPENSE_CODE_NOT_FOUND(1008, "error.code.expense_code.not_found"),
    EXPENSE_CODE_NOT_FOUND_WITH_CODE(1008, "error.code.expense_code.expense_code_not_found_with_code"),
    EXPENSE_CODE_ALREADY_EXIST(1009, "error.code.expense_code.already_exist"),
    ISSUER_NOT_FOUND(1008, "error.code.issuer.not_found");

    private final Integer code;
    private final String messageKey;

    ParameterResponseCode(Integer code, String messageKey) {
        this.code = code;
        this.messageKey = messageKey;
    }

    @JsonValue
    public Integer getCode() {
        return code;
    }

    public String getMessageKey() {
        return messageKey;
    }
}
