package com.ipms.paramcommand.model;

import com.ipms.core.entity.VersionedEntity;
import com.ipms.paramcommand.enums.CountryMatterTypeBaseDateType;
import com.ipms.paramcommand.enums.CountryMatterTypePeriodType;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "country_matter_type")
@Entity
@Where(clause = "is_deleted='0'")
public class CountryMatterType extends VersionedEntity {
    @Column
    private String country;
    @Column
    private String matterType;

    @Enumerated(EnumType.STRING)
    @Column
    private CountryMatterTypeBaseDateType baseDate;

    @Enumerated(EnumType.STRING)
    @Column
    private CountryMatterTypePeriodType period;

    @Column
    private int periodValue;

    @Column
    private Boolean usageRequired;
}
