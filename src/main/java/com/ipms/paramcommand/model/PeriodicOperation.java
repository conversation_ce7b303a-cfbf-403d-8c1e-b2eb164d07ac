package com.ipms.paramcommand.model;

import com.ipms.core.entity.VersionedEntity;
import com.ipms.paramcommand.enums.PeriodicOperationBaseDateType;
import com.ipms.paramcommand.enums.PeriodicOperationPeriodType;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "periodic_operation")
@Entity
@Where(clause = "is_deleted='0'")
public class PeriodicOperation extends VersionedEntity {

    @Column
    private Long ipOfficeId;

    @Column
    private Long countryId;

    @Column
    private String matterType;

    @Enumerated(EnumType.STRING)
    @Column
    private PeriodicOperationPeriodType period;

    @Column
    private Integer value;

    @Enumerated(EnumType.STRING)
    @Column
    private PeriodicOperationBaseDateType baseDate;

    @Column
    private PeriodicOperationPeriodType nextPeriod;

    @Column
    private Integer nextPeriodValue;

    @Enumerated(EnumType.STRING)
    @Column
    private PeriodicOperationPeriodType additionalTimePeriod;

    @Column
    private Integer additionalTimePeriodValue;

    @Column
    private LocalDate conditionOfValidityDate;
}
