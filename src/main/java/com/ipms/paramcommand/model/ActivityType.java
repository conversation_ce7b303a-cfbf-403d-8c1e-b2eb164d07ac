package com.ipms.paramcommand.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.BatchSize;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "activity_type")
@Entity
@Where(clause = "is_deleted='0'")
public class ActivityType extends VersionedEntity {

    @Column
    private String country;

    @Column
    private String matterType;

    @Column
    private String activity;

    @OneToMany(mappedBy="activityType", cascade = CascadeType.ALL, orphanRemoval = true)
    @BatchSize(size = 25)
    private List<ActivityStatus> statuses;

    @OneToMany(mappedBy="activityType", cascade = CascadeType.ALL, orphanRemoval = true)
    private List<ActivityDueDate> dueDates;
}
