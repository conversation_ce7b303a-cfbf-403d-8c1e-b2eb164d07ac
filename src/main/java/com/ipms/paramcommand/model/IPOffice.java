package com.ipms.paramcommand.model;

import com.ipms.core.entity.VersionedEntity;
import com.ipms.paramcommand.enums.IPOfficeType;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "ip_office")
@Entity
@Where(clause = "is_deleted='0'")
public class IPOffice extends VersionedEntity {
    @Column
    private String nameEn;

    @Column
    private String nameTr;

    @Column
    private String code;

    @Column
    private IPOfficeType type;
}
