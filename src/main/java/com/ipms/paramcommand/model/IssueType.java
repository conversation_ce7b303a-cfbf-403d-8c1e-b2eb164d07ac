package com.ipms.paramcommand.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "parameter_issue_type")
@Entity
@Where(clause = "is_deleted='0'")
public class IssueType extends VersionedEntity {
    @Column
    private String key;
    @Column
    private String type;
    @Column
    private String activityType;
    @Column
    private String size;
    @Column
    private int targetDateDay;
    @Column
    private int replyDueDay;
}
