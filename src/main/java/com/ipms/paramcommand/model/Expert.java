package com.ipms.paramcommand.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "expert")
@Entity
@Where(clause = "is_deleted='0'")
public class Expert extends VersionedEntity {

    @Column
    private String name;

    @Column
    private String surname;

    @Column
    private String profession;

    @Column
    private String city;

    @Column
    private String identityNumber;
}
