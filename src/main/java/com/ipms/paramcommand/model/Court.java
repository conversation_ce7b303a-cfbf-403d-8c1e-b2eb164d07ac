package com.ipms.paramcommand.model;

import com.ipms.core.entity.VersionedEntity;
import com.ipms.paramcommand.enums.CourtKind;
import com.ipms.paramcommand.enums.CourtType;
import lombok.*;
import lombok.experimental.SuperBuilder;

import javax.persistence.*;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "court")
@Entity
public class Court extends VersionedEntity {
    @Column
    private CourtType type;

    @Column
    private CourtKind kind;

    @Column
    private String name;

    @Column
    private String city;

    @ElementCollection
    @CollectionTable(name = "court_judges", joinColumns = @JoinColumn(name = "court_id", referencedColumnName = "id"))
    @Column(name = "judge")
    private List<String> judges;
}
