package com.ipms.paramcommand.model;

import com.ipms.core.entity.VersionedEntity;
import com.ipms.paramcommand.enums.ActivityBaseDateType;
import com.ipms.paramcommand.enums.ActivityPeriodType;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "activity_type_due_date")
@Entity
@Where(clause = "is_deleted='0'")
public class ActivityDueDate extends VersionedEntity {

    @ManyToOne
    @JoinColumn(name="activity_type_id")
    private ActivityType activityType;

    @Column
    private String status;

    @Enumerated(EnumType.STRING)
    @Column
    private ActivityBaseDateType baseDate;

    @Enumerated(EnumType.STRING)
    @Column
    private ActivityPeriodType period;

    @Column
    private int periodValue;
}
