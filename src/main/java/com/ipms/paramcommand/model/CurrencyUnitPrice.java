package com.ipms.paramcommand.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.Column;
import javax.persistence.Entity;
import javax.persistence.Table;
import java.math.BigDecimal;
import java.time.LocalDate;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "currency_unit_price")
@Entity
@Where(clause = "is_deleted='0'")
public class CurrencyUnitPrice extends VersionedEntity {

    @Column
    private String currency;

    @Column(precision = 19, scale = 4)
    private BigDecimal unitPrice;

    @Column
    private LocalDate fromDate;

    @Column
    private LocalDate toDate;
}
