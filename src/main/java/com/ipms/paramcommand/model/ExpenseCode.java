package com.ipms.paramcommand.model;

import com.ipms.core.entity.VersionedEntity;
import com.ipms.paramcommand.enums.ExpenseCodeType;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.*;
import java.time.LocalDate;
import java.util.List;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "expense_code")
@Entity
@Where(clause = "is_deleted='0'")
public class ExpenseCode extends VersionedEntity {

    @Column
    private String code;

    @Column
    private ExpenseCodeType type;

    @Column
    private LocalDate expiryDate;

    @Column(columnDefinition = "TEXT")
    private String definition;

    @OneToMany(cascade = CascadeType.ALL, fetch = FetchType.EAGER)
    private List<CurrencyUnitPrice> currencyUnitPrices;

    @ElementCollection
    @CollectionTable(name = "expense_code_activity_types", joinColumns = @JoinColumn(name = "expense_code_id", referencedColumnName = "id"))
    @Column(name = "type")
    private List<String> activityTypes;
}
