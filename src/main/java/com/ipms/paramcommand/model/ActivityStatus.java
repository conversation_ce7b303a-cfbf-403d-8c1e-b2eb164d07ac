package com.ipms.paramcommand.model;

import com.ipms.core.entity.VersionedEntity;
import lombok.*;
import lombok.experimental.SuperBuilder;
import org.hibernate.annotations.Where;

import javax.persistence.*;

@Getter
@Setter
@EqualsAndHashCode(onlyExplicitlyIncluded = true, callSuper = true)
@ToString(onlyExplicitlyIncluded = true, callSuper = true)
@SuperBuilder(toBuilder = true)
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "activity_status")
@Entity
@Where(clause = "is_deleted='0'")
public class ActivityStatus extends VersionedEntity {

    @Column
    private String status;

    @ManyToOne
    @JoinColumn(name="activity_type_id")
    private ActivityType activityType;
}
