package com.ipms.paramcommand.client;

import com.ipms.paramcommand.dto.CustomsResponse;
import org.springframework.cloud.openfeign.FeignClient;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PathVariable;


@FeignClient(name = "ipms-activity")
public interface ActivityClient {

    @GetMapping("/api/activity/customs/{customsId}")
    CustomsResponse getActivitiesByCustoms(@PathVariable Long customsId);

    @GetMapping("api/activity/customs-training-date/customs/{customsId}")
    CustomsResponse getCustomsTrainingDatesByCustoms(@PathVariable Long customsId);
}
