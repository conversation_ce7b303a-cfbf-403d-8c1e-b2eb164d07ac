package com.ipms.paramcommand.mapper;

import com.ipms.paramcommand.dto.HolidayDto;
import com.ipms.paramcommand.model.Holiday;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface HolidayMapper {
    Holiday toHoliday (HolidayDto holidayDto);
    HolidayDto toHolidayDto (Holiday holiday);
    Holiday toHolidayFromDto(HolidayDto dto, @MappingTarget Holiday holiday);
}
