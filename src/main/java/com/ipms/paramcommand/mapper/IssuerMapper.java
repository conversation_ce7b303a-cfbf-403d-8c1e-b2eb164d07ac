package com.ipms.paramcommand.mapper;

import com.ipms.paramcommand.dto.IssuerDto;
import com.ipms.paramcommand.model.Issuer;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;

@Mapper(componentModel = "spring")
public interface IssuerMapper {
    IssuerDto toIssuerDto(Issuer issuer);

    Issuer toIssuer(IssuerDto issuerDto);

    @Mapping(target = "id", ignore = true)
    Issuer toIssuerFromDto(IssuerDto dto, @MappingTarget Issuer issuer);
}
