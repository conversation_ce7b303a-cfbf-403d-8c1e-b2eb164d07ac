package com.ipms.paramcommand.mapper;

import com.ipms.paramcommand.dto.ExpertDto;
import com.ipms.paramcommand.model.Expert;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;

@Mapper(componentModel = "spring")
public interface ExpertMapper {

    ExpertDto toExpertDto(Expert expert);

    @Mapping(expression = "java(com.ipms.paramcommand.util.ExpertUtils.maskIdentityNumber(expertDto.getIdentityNumber()))", target = "identityNumber")
    Expert toExpert(ExpertDto expertDto);

}
