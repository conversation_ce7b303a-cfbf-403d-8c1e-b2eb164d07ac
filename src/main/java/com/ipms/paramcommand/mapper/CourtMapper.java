package com.ipms.paramcommand.mapper;

import com.ipms.paramcommand.dto.CourtDto;
import com.ipms.paramcommand.model.Court;
import org.mapstruct.Mapper;
import org.mapstruct.Mapping;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface CourtMapper {
    Court toCourt (CourtDto dto);
    @Mapping(target = "judges", source = "court.judges")
    CourtDto toCourtDto (Court court);
    Court toCourtFromDto(CourtDto dto, @MappingTarget Court court);
}
