package com.ipms.paramcommand.mapper;

import com.ipms.paramcommand.dto.IssueTypeDto;
import com.ipms.paramcommand.model.IssueType;
import org.mapstruct.Mapper;
import org.mapstruct.MappingTarget;
import org.mapstruct.NullValuePropertyMappingStrategy;

@Mapper(componentModel = "spring", nullValuePropertyMappingStrategy = NullValuePropertyMappingStrategy.IGNORE)
public interface IssueTypeMapper {
    IssueType toIssueType (IssueTypeDto issueTypeDto);
    IssueTypeDto toIssueTypeDto (IssueType issueType);
    IssueType toIssueTypeFromDto(IssueTypeDto dto, @MappingTarget IssueType issueType);

}
